/**
 * AI智切扩展高级代码还原工具
 * 能够实际执行解码函数来还原字符串
 */

const fs = require('fs');
const path = require('path');
const vm = require('vm');

class AdvancedDeobfuscator {
    constructor(filePath) {
        this.filePath = filePath;
        this.obfuscatedCode = '';
        this.stringArray = [];
        this.decodedStrings = new Map();
        this.decoderFunction = null;
        this.restoredCode = '';
        this.context = {};
    }

    /**
     * 加载混淆代码
     */
    loadCode() {
        try {
            this.obfuscatedCode = fs.readFileSync(this.filePath, 'utf8');
            console.log('✅ 混淆代码加载成功');
            return true;
        } catch (error) {
            console.error('❌ 加载失败:', error.message);
            return false;
        }
    }

    /**
     * 提取并执行解码函数
     */
    extractDecoderFunction() {
        console.log('\n🔍 提取解码函数...');

        try {
            // 更精确的模式匹配
            const functionPattern = /function a0_0x53e5\([^)]*\)[^{]*{[\s\S]*?},a0_0x53e5\([^)]*\);/;
            const arrayPattern = /function a0_0x561e\(\)[^{]*{[\s\S]*?return a0_0x561e\(\);[\s\S]*?}/;

            const funcMatch = this.obfuscatedCode.match(functionPattern);
            const arrayMatch = this.obfuscatedCode.match(arrayPattern);

            console.log('🔍 函数匹配结果:', !!funcMatch, !!arrayMatch);

            if (funcMatch && arrayMatch) {
                // 创建安全的执行环境
                const sandbox = {
                    console: console,
                    Buffer: Buffer,
                    decodeURIComponent: decodeURIComponent,
                    String: String,
                    parseInt: parseInt,
                    Math: Math,
                    undefined: undefined
                };

                const context = vm.createContext(sandbox);

                // 执行字符串数组函数
                console.log('🔍 执行字符串数组函数...');
                vm.runInContext(arrayMatch[0], context);

                // 执行解码函数
                console.log('🔍 执行解码函数...');
                vm.runInContext(funcMatch[0], context);

                this.decoderFunction = context.a0_0x53e5;
                console.log('📊 解码函数提取成功');
                return true;
            } else {
                console.log('⚠️ 尝试简化的字符串提取...');
                return this.extractStringsDirectly();
            }
        } catch (error) {
            console.error('❌ 解码函数提取失败:', error.message);
            console.log('⚠️ 尝试简化的字符串提取...');
            return this.extractStringsDirectly();
        }
    }

    /**
     * 直接提取字符串数组
     */
    extractStringsDirectly() {
        try {
            // 直接从代码中提取字符串数组
            const arrayPattern = /var _0x[a-f0-9]+=\['([^']+)'(?:,'([^']+)')*\]/;
            const fullArrayPattern = /var _0x[a-f0-9]+=\[([\s\S]*?)\]/;

            const match = this.obfuscatedCode.match(fullArrayPattern);
            if (match) {
                const arrayContent = match[1];
                const strings = arrayContent.match(/'([^'\\]|\\.)*'/g) || [];

                // 解码字符串
                strings.forEach((str, index) => {
                    const cleanStr = str.slice(1, -1); // 移除引号
                    const hexIndex = '0x' + (index + 0x7e).toString(16);

                    try {
                        // 尝试Base64解码
                        const decoded = this.tryDecodeString(cleanStr);
                        this.decodedStrings.set(hexIndex, decoded);
                    } catch (error) {
                        this.decodedStrings.set(hexIndex, cleanStr);
                    }
                });

                console.log(`📊 直接提取了 ${this.decodedStrings.size} 个字符串`);
                return true;
            }
        } catch (error) {
            console.error('❌ 直接字符串提取失败:', error.message);
        }

        return false;
    }

    /**
     * 尝试解码字符串
     */
    tryDecodeString(str) {
        try {
            // 尝试Base64解码
            if (this.isBase64Like(str)) {
                const decoded = Buffer.from(str, 'base64').toString('utf8');
                if (this.isPrintableString(decoded)) {
                    return decoded;
                }
            }

            // 尝试URI解码
            if (str.includes('%')) {
                return decodeURIComponent(str);
            }

            return str;
        } catch (error) {
            return str;
        }
    }

    /**
     * 检查是否像Base64编码
     */
    isBase64Like(str) {
        return /^[A-Za-z0-9+/]*={0,2}$/.test(str) && str.length % 4 === 0;
    }

    /**
     * 检查是否为可打印字符串
     */
    isPrintableString(str) {
        return /^[\x20-\x7E\s]*$/.test(str);
    }

    /**
     * 解码所有字符串
     */
    decodeAllStrings() {
        console.log('\n🔍 解码所有字符串...');

        if (this.decodedStrings.size > 0) {
            console.log(`📊 已有 ${this.decodedStrings.size} 个字符串，显示前20个:`);

            let count = 0;
            for (const [key, value] of this.decodedStrings) {
                if (count < 20) {
                    console.log(`  [${key}]: ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}`);
                    count++;
                } else {
                    break;
                }
            }
            return true;
        }

        if (!this.decoderFunction) {
            console.error('❌ 解码函数未找到，使用已提取的字符串');
            return this.decodedStrings.size > 0;
        }

        let decodedCount = 0;

        // 尝试解码从0x7e开始的索引
        for (let i = 0x7e; i < 0x7e + 1000; i++) {
            try {
                const hexIndex = '0x' + i.toString(16);
                const decoded = this.decoderFunction(hexIndex);

                if (decoded && typeof decoded === 'string' && decoded.length > 0) {
                    this.decodedStrings.set(hexIndex, decoded);
                    decodedCount++;

                    // 显示前20个解码的字符串
                    if (decodedCount <= 20) {
                        console.log(`  [${hexIndex}]: ${decoded.substring(0, 50)}${decoded.length > 50 ? '...' : ''}`);
                    }
                }
            } catch (error) {
                // 忽略解码失败的索引
                continue;
            }
        }

        console.log(`📊 成功解码 ${decodedCount} 个字符串`);
        return decodedCount > 0;
    }

    /**
     * 还原字符串引用
     */
    restoreStringReferences() {
        console.log('\n🔍 还原字符串引用...');
        
        let restoredCode = this.obfuscatedCode;
        let replacements = 0;
        
        // 查找字符串引用模式
        const refPattern = /a0_0x53e5\((0x[a-f0-9]+)\)/g;
        
        restoredCode = restoredCode.replace(refPattern, (match, hexIndex) => {
            if (this.decodedStrings.has(hexIndex)) {
                const decodedString = this.decodedStrings.get(hexIndex);
                replacements++;
                
                // 转义特殊字符
                const escapedString = this.escapeString(decodedString);
                
                // 如果是关键字符串，直接替换
                if (this.isKeyString(decodedString)) {
                    return `"${escapedString}"`;
                }
                
                // 其他字符串添加注释
                return `"${escapedString}" /* ${match} */`;
            }
            
            return match;
        });
        
        console.log(`📊 还原了 ${replacements} 个字符串引用`);
        this.restoredCode = restoredCode;
        return replacements > 0;
    }

    /**
     * 转义字符串中的特殊字符
     */
    escapeString(str) {
        return str
            .replace(/\\/g, '\\\\')
            .replace(/"/g, '\\"')
            .replace(/\n/g, '\\n')
            .replace(/\r/g, '\\r')
            .replace(/\t/g, '\\t');
    }

    /**
     * 检查是否为关键字符串
     */
    isKeyString(str) {
        const keyStrings = [
            'vscode', 'window', 'document', 'console', 'exports',
            'smartshift-manager', 'openPanel', 'openLogs',
            'axios', 'fernet', 'crypto', 'SHA256',
            'activate', 'deactivate', 'commands', 'registerCommand',
            'createWebviewPanel', 'showInformationMessage', 'showErrorMessage',
            'statusBar', 'webview', 'panel', 'html'
        ];
        
        return keyStrings.some(key => str.toLowerCase().includes(key.toLowerCase()));
    }

    /**
     * 分析还原后的代码
     */
    analyzeRestoredCode() {
        console.log('\n🔍 分析还原后的代码...');
        
        const analysis = {
            vscodeAPIs: [],
            cryptoFunctions: [],
            networkCalls: [],
            keyStrings: []
        };

        // 查找VSCode API调用
        const vscodePatterns = [
            /vscode\.commands\.registerCommand/g,
            /vscode\.window\.createWebviewPanel/g,
            /vscode\.window\.showInformationMessage/g,
            /vscode\.window\.showErrorMessage/g,
            /vscode\.workspace\.getConfiguration/g
        ];

        vscodePatterns.forEach(pattern => {
            const matches = [...this.restoredCode.matchAll(pattern)];
            if (matches.length > 0) {
                analysis.vscodeAPIs.push({
                    pattern: pattern.source,
                    count: matches.length
                });
            }
        });

        // 查找加密相关函数
        const cryptoPatterns = [
            /SHA256/g,
            /encrypt/gi,
            /decrypt/gi,
            /hash/gi,
            /fernet/gi
        ];

        cryptoPatterns.forEach(pattern => {
            const matches = [...this.restoredCode.matchAll(pattern)];
            if (matches.length > 0) {
                analysis.cryptoFunctions.push({
                    pattern: pattern.source,
                    count: matches.length
                });
            }
        });

        // 查找网络调用
        const networkPatterns = [
            /axios\./g,
            /fetch\(/g,
            /XMLHttpRequest/g,
            /http\./g
        ];

        networkPatterns.forEach(pattern => {
            const matches = [...this.restoredCode.matchAll(pattern)];
            if (matches.length > 0) {
                analysis.networkCalls.push({
                    pattern: pattern.source,
                    count: matches.length
                });
            }
        });

        // 提取关键字符串
        this.decodedStrings.forEach((value, key) => {
            if (this.isKeyString(value)) {
                analysis.keyStrings.push({
                    index: key,
                    value: value
                });
            }
        });

        console.log('📊 分析结果:');
        console.log(`  VSCode API调用: ${analysis.vscodeAPIs.length} 种类型`);
        console.log(`  加密函数: ${analysis.cryptoFunctions.length} 种类型`);
        console.log(`  网络调用: ${analysis.networkCalls.length} 种类型`);
        console.log(`  关键字符串: ${analysis.keyStrings.length} 个`);

        return analysis;
    }

    /**
     * 生成详细报告
     */
    generateDetailedReport() {
        console.log('\n📋 生成详细报告...');
        
        const analysis = this.analyzeRestoredCode();
        
        const report = {
            timestamp: new Date().toISOString(),
            originalSize: this.obfuscatedCode.length,
            restoredSize: this.restoredCode.length,
            decodedStringsCount: this.decodedStrings.size,
            analysis: {
                obfuscationType: 'webpack-obfuscator',
                decodingSuccess: true,
                restorationQuality: 'High'
            },
            vscodeAPIs: analysis.vscodeAPIs,
            cryptoFunctions: analysis.cryptoFunctions,
            networkCalls: analysis.networkCalls,
            keyStrings: analysis.keyStrings.slice(0, 50), // 前50个关键字符串
            decodedStrings: Array.from(this.decodedStrings.entries()).slice(0, 100) // 前100个解码字符串
        };

        // 保存还原后的代码
        const restoredPath = path.join(__dirname, 'extension_fully_restored.js');
        fs.writeFileSync(restoredPath, this.restoredCode, 'utf8');
        
        // 保存详细报告
        const reportPath = path.join(__dirname, '高级还原报告.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
        
        // 生成可读的字符串映射文件
        const mappingPath = path.join(__dirname, '字符串映射表.txt');
        let mappingContent = '# AI智切扩展字符串映射表\n\n';
        
        this.decodedStrings.forEach((value, key) => {
            mappingContent += `${key}: "${value}"\n`;
        });
        
        fs.writeFileSync(mappingPath, mappingContent, 'utf8');
        
        console.log(`📄 完全还原代码已保存到: ${restoredPath}`);
        console.log(`📄 详细报告已保存到: ${reportPath}`);
        console.log(`📄 字符串映射表已保存到: ${mappingPath}`);
        
        return report;
    }

    /**
     * 执行完整的高级还原流程
     */
    restore() {
        console.log('🚀 开始高级代码还原...\n');
        
        if (!this.loadCode()) {
            return false;
        }

        // 1. 提取解码函数
        if (!this.extractDecoderFunction()) {
            console.error('❌ 无法提取解码函数，使用基础还原方法');
            return false;
        }

        // 2. 解码所有字符串
        if (!this.decodeAllStrings()) {
            console.error('❌ 字符串解码失败');
            return false;
        }

        // 3. 还原字符串引用
        this.restoreStringReferences();

        // 4. 生成详细报告
        const report = this.generateDetailedReport();

        console.log('\n✅ 高级代码还原完成!');
        console.log('\n📊 还原摘要:');
        console.log(`  原始大小: ${(this.obfuscatedCode.length / 1024).toFixed(2)} KB`);
        console.log(`  还原大小: ${(this.restoredCode.length / 1024).toFixed(2)} KB`);
        console.log(`  解码字符串: ${this.decodedStrings.size} 个`);
        console.log(`  VSCode API: ${report.vscodeAPIs.length} 种类型`);
        console.log(`  加密函数: ${report.cryptoFunctions.length} 种类型`);
        console.log(`  关键字符串: ${report.keyStrings.length} 个`);

        return true;
    }
}

// 使用示例
if (require.main === module) {
    const deobfuscator = new AdvancedDeobfuscator('./extension/dist/extension.js');
    deobfuscator.restore();
}

module.exports = AdvancedDeobfuscator;
