/**
 * AI智切账号池多线程获取测试脚本
 * 基于深度逆向分析的API参数实现
 * 300次并发请求测试
 */

const https = require('https');
const http = require('http');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const fs = require('fs');
const crypto = require('crypto');

// 配置参数
const CONFIG = {
    activationCode: "90909420-c7f4-4bd6-8517-0bfc572ed3e1",
    baseUrl: "https://api.smartshift.com", // 根据实际情况修改
    concurrency: 300,
    timeout: 10000,
    retries: 3,
    userAgent: "SmartShift-Test/1.1.1"
};

// 生成机器ID
function generateMachineId() {
    const timestamp = Date.now().toString();
    const random = crypto.randomBytes(16).toString('hex');
    return crypto.createHash('sha256').update(timestamp + random).digest('hex').substring(0, 32);
}

// HTTP请求函数
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const protocol = options.protocol === 'https:' ? https : http;
        
        const req = protocol.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const result = {
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: data ? JSON.parse(data) : null
                    };
                    resolve(result);
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: null,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        req.setTimeout(CONFIG.timeout, () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

// 用户认证
async function authenticate(machineId) {
    const url = new URL('/api/auth', CONFIG.baseUrl);
    
    const postData = JSON.stringify({
        activationCode: CONFIG.activationCode,
        clientVersion: "1.1.1",
        platform: "test",
        machineId: machineId,
        timestamp: Date.now()
    });
    
    const options = {
        hostname: url.hostname,
        port: url.port || (url.protocol === 'https:' ? 443 : 80),
        path: url.pathname,
        method: 'POST',
        protocol: url.protocol,
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData),
            'User-Agent': CONFIG.userAgent,
            'Accept': 'application/json',
            'Cache-Control': 'no-cache'
        }
    };
    
    return await makeRequest(options, postData);
}

// 获取账号列表
async function getAccounts(token, machineId) {
    const url = new URL('/api/accounts', CONFIG.baseUrl);
    
    const options = {
        hostname: url.hostname,
        port: url.port || (url.protocol === 'https:' ? 443 : 80),
        path: url.pathname + '?limit=100&status=active',
        method: 'GET',
        protocol: url.protocol,
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'User-Agent': CONFIG.userAgent,
            'X-Client-Version': '1.1.1',
            'X-Platform': 'test',
            'X-Machine-Id': machineId,
            'X-Request-ID': `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            'Accept': 'application/json',
            'Cache-Control': 'no-cache'
        }
    };
    
    return await makeRequest(options);
}

// Worker线程执行函数
async function workerTask(workerId) {
    const results = {
        workerId: workerId,
        success: false,
        authResult: null,
        accountsResult: null,
        accounts: [],
        error: null,
        timing: {
            start: Date.now(),
            authTime: 0,
            accountsTime: 0,
            total: 0
        }
    };
    
    try {
        const machineId = generateMachineId();
        
        // 步骤1: 用户认证
        console.log(`[Worker ${workerId}] 开始认证...`);
        const authStart = Date.now();
        
        let authResponse = null;
        for (let retry = 0; retry < CONFIG.retries; retry++) {
            try {
                authResponse = await authenticate(machineId);
                if (authResponse.statusCode === 200 && authResponse.data && authResponse.data.success) {
                    break;
                }
            } catch (error) {
                if (retry === CONFIG.retries - 1) throw error;
                await new Promise(resolve => setTimeout(resolve, 1000 * (retry + 1)));
            }
        }
        
        results.timing.authTime = Date.now() - authStart;
        results.authResult = authResponse;
        
        if (!authResponse || authResponse.statusCode !== 200 || !authResponse.data || !authResponse.data.success) {
            throw new Error(`认证失败: ${authResponse ? authResponse.statusCode : 'No response'}`);
        }
        
        const token = authResponse.data.token;
        console.log(`[Worker ${workerId}] 认证成功，获得token`);
        
        // 步骤2: 获取账号列表
        console.log(`[Worker ${workerId}] 获取账号列表...`);
        const accountsStart = Date.now();
        
        let accountsResponse = null;
        for (let retry = 0; retry < CONFIG.retries; retry++) {
            try {
                accountsResponse = await getAccounts(token, machineId);
                if (accountsResponse.statusCode === 200) {
                    break;
                }
            } catch (error) {
                if (retry === CONFIG.retries - 1) throw error;
                await new Promise(resolve => setTimeout(resolve, 1000 * (retry + 1)));
            }
        }
        
        results.timing.accountsTime = Date.now() - accountsStart;
        results.accountsResult = accountsResponse;
        
        if (accountsResponse && accountsResponse.data && accountsResponse.data.accounts) {
            results.accounts = accountsResponse.data.accounts;
            results.success = true;
            console.log(`[Worker ${workerId}] 成功获取 ${results.accounts.length} 个账号`);
        } else {
            console.log(`[Worker ${workerId}] 获取账号失败: ${accountsResponse ? accountsResponse.statusCode : 'No response'}`);
        }
        
    } catch (error) {
        results.error = error.message;
        console.error(`[Worker ${workerId}] 执行失败:`, error.message);
    }
    
    results.timing.total = Date.now() - results.timing.start;
    return results;
}

// Worker线程代码
if (!isMainThread) {
    (async () => {
        try {
            const result = await workerTask(workerData.workerId);
            parentPort.postMessage(result);
        } catch (error) {
            parentPort.postMessage({
                workerId: workerData.workerId,
                success: false,
                error: error.message
            });
        }
    })();
} else {
    // 主线程代码
    async function runConcurrentTest() {
        console.log(`🚀 开始 ${CONFIG.concurrency} 线程并发账号获取测试`);
        console.log(`📋 配置信息:`);
        console.log(`   激活码: ${CONFIG.activationCode}`);
        console.log(`   并发数: ${CONFIG.concurrency}`);
        console.log(`   超时时间: ${CONFIG.timeout}ms`);
        console.log(`   重试次数: ${CONFIG.retries}`);
        console.log(`   目标URL: ${CONFIG.baseUrl}`);
        console.log('');
        
        const startTime = Date.now();
        const workers = [];
        const results = [];
        
        // 创建Worker线程
        for (let i = 0; i < CONFIG.concurrency; i++) {
            const worker = new Worker(__filename, {
                workerData: { workerId: i + 1 }
            });
            
            workers.push(new Promise((resolve) => {
                worker.on('message', (result) => {
                    results.push(result);
                    resolve();
                });
                
                worker.on('error', (error) => {
                    results.push({
                        workerId: i + 1,
                        success: false,
                        error: error.message
                    });
                    resolve();
                });
            }));
        }
        
        // 等待所有Worker完成
        await Promise.all(workers);
        
        const totalTime = Date.now() - startTime;
        
        // 统计结果
        const stats = {
            total: results.length,
            successful: results.filter(r => r.success).length,
            failed: results.filter(r => !r.success).length,
            totalAccounts: 0,
            uniqueAccounts: new Set(),
            avgAuthTime: 0,
            avgAccountsTime: 0,
            avgTotalTime: 0
        };
        
        let totalAuthTime = 0;
        let totalAccountsTime = 0;
        let totalWorkerTime = 0;
        
        results.forEach(result => {
            if (result.accounts) {
                stats.totalAccounts += result.accounts.length;
                result.accounts.forEach(acc => {
                    if (acc.id) stats.uniqueAccounts.add(acc.id);
                });
            }
            
            if (result.timing) {
                totalAuthTime += result.timing.authTime || 0;
                totalAccountsTime += result.timing.accountsTime || 0;
                totalWorkerTime += result.timing.total || 0;
            }
        });
        
        stats.avgAuthTime = Math.round(totalAuthTime / stats.total);
        stats.avgAccountsTime = Math.round(totalAccountsTime / stats.total);
        stats.avgTotalTime = Math.round(totalWorkerTime / stats.total);
        
        // 输出结果
        console.log('\n📊 测试结果统计:');
        console.log(`   总请求数: ${stats.total}`);
        console.log(`   成功数: ${stats.successful} (${(stats.successful/stats.total*100).toFixed(1)}%)`);
        console.log(`   失败数: ${stats.failed} (${(stats.failed/stats.total*100).toFixed(1)}%)`);
        console.log(`   总账号数: ${stats.totalAccounts}`);
        console.log(`   唯一账号数: ${stats.uniqueAccounts.size}`);
        console.log(`   总耗时: ${totalTime}ms`);
        console.log(`   平均认证时间: ${stats.avgAuthTime}ms`);
        console.log(`   平均获取账号时间: ${stats.avgAccountsTime}ms`);
        console.log(`   平均单线程时间: ${stats.avgTotalTime}ms`);
        console.log(`   并发效率: ${(stats.avgTotalTime/totalTime*100).toFixed(1)}%`);
        
        // 保存详细结果
        const reportData = {
            config: CONFIG,
            stats: stats,
            totalTime: totalTime,
            results: results,
            timestamp: new Date().toISOString()
        };
        
        const reportFile = `账号获取测试报告_${Date.now()}.json`;
        fs.writeFileSync(reportFile, JSON.stringify(reportData, null, 2));
        console.log(`\n📄 详细报告已保存到: ${reportFile}`);
        
        // 保存账号数据
        if (stats.totalAccounts > 0) {
            const allAccounts = [];
            results.forEach(result => {
                if (result.accounts) {
                    allAccounts.push(...result.accounts);
                }
            });
            
            const accountsFile = `获取的账号数据_${Date.now()}.json`;
            fs.writeFileSync(accountsFile, JSON.stringify(allAccounts, null, 2));
            console.log(`📋 账号数据已保存到: ${accountsFile}`);
        }
        
        // 错误分析
        const errors = results.filter(r => r.error).map(r => r.error);
        if (errors.length > 0) {
            console.log('\n❌ 错误分析:');
            const errorCounts = {};
            errors.forEach(error => {
                errorCounts[error] = (errorCounts[error] || 0) + 1;
            });
            
            Object.entries(errorCounts).forEach(([error, count]) => {
                console.log(`   ${error}: ${count}次`);
            });
        }
        
        console.log('\n✅ 测试完成!');
        
        if (stats.successful > 0) {
            console.log(`\n🎉 成功获取到 ${stats.uniqueAccounts.size} 个唯一账号!`);
        }
    }
    
    // 运行测试
    runConcurrentTest().catch(console.error);
}

/**
 * 使用方法:
 * 1. 修改CONFIG中的baseUrl为您的实际API地址
 * 2. 确认激活码正确
 * 3. 运行: node 多线程账号获取测试.js
 * 
 * 注意事项:
 * - 确保您的服务器能够处理300个并发连接
 * - 监控服务器资源使用情况
 * - 根据需要调整并发数和超时时间
 * - 测试结果会保存到JSON文件中
 */
