{"timestamp": "2025-08-05T11:00:23.925Z", "summary": {"totalAPIs": 151, "categories": [{"category": "vscode", "count": 39}, {"category": "http", "count": 1}, {"category": "crypto", "count": 30}, {"category": "filesystem", "count": 15}, {"category": "system", "count": 12}, {"category": "external", "count": 54}]}, "details": {"vscode": ["window.addEventListener", "window", "window", "vscode-augment.signOut", "vscode-augment.directLogin", "window", "vscode-augment.signOut", "window", "window", "window", "window", "\\x22>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20</div>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<button\\x20onclick=\\x22login()\\x22>一键换号</button>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<button\\x20onclick=\\x22resetid()\\x22>重置机器码</button>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<div\\x20id=\\x22userDetails\\x22\\x20class=\\x22info-display\\x22\\x20style=\\x22display:\\x20none;\\x22>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<h3>用户信息</h3>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<div\\x20id=\\x22userInfo\\x22></div>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20</div>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<div\\x20id=\\x22loginStatus\\x22\\x20class=\\x22status\\x22\\x20style=\\x22display:\\x20none;\\x22></div>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<h2>账号获取</h2>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<p\\x20style=\\x22color:\\x20var(--vscode-descriptionForeground);\\x20margin-bottom:\\x2010px;\\x22>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x201.\\x20请先点击上方\\x22检查\\x22按钮确保插件已正确配置\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20</p>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<p\\x20style=\\x22color:\\x20var(--vscode-descriptionForeground);\\x20margin-bottom:\\x2010px;\\x22>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x202.\\x20如重新获取现有账号直接点击获取，如需获取新账号请先登录\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20</p>\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<div\\x20id=\\x22accountStatus\\x22\\x20class=\\x22status\\x22\\x20style=\\x22display:\\x20none;\\x22></div>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20</div>\\x0a\\x20\\x20\\x20\\x20</div>\\x0a\\x0a\\x20\\x20\\x20\\x20<script>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20更彻底地禁用\\x20Service\\x20Worker\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(\\x27serviceWorker\\x27\\x20in\\x20navigator)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20delete\\x20navigator.serviceWorker;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20禁用所有可能的\\x20Service\\x20Worker\\x20相关\\x20API\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(typeof\\x20ServiceWorkerContainer\\x20!==\\x20\\x27undefined\\x27)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20ServiceWorkerContainer.prototype.register\\x20=\\x20function()\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20return\\x20Promise.reject(new\\x20Error(\\x27Service\\x20Worker\\x20disabled\\x20in\\x20webview\\x27));\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20};\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20重写\\x20navigator\\x20对象以完全移除\\x20serviceWorker\\x20属性\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20Object.defineProperty(navigator,\\x20\\x27serviceWorker\\x27,\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20value:\\x20undefined,\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20writable:\\x20false,\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20configurable:\\x20false\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20添加全局错误处理\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20window.addEventListener(\\x27error\\x27,\\x20function(event)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(event.error\\x20&&\\x20event.error.message\\x20&&\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20(event.error.message.includes(\\x27ServiceWorker\\x27)\\x20||\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20event.error.message.includes(\\x27service\\x20worker\\x27)\\x20||\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20event.error.message.includes(\\x27InvalidStateError\\x27)))\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20event.preventDefault();\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20return\\x20false;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20window.addEventListener(\\x27unhandledrejection\\x27,\\x20function(event)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(event.reason\\x20&&\\x20event.reason.message\\x20&&\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20(event.reason.message.includes(\\x27ServiceWorker\\x27)\\x20||\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20event.reason.message.includes(\\x27service\\x20worker\\x27)\\x20||\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20event.reason.message.includes(\\x27InvalidStateError\\x27)))\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20event.preventDefault();\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20return\\x20false;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20const\\x20vscode\\x20=\\x20acquireVsCodeApi();\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20function\\x20checkAugmentPlugin()\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20vscode.postMessage({\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20command:\\x20\\x27checkAugmentPlugin\\x27\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20function\\x20openWebsite()\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20vscode.postMessage({\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20command:\\x20\\x27openWebsite\\x27\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20function\\x20login()\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20const\\x20activationCode\\x20=\\x20document.getElementById(\\x27activationCode\\x27).value.trim();\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(!activationCode)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20showStatus(\\x27loginStatus\\x27,\\x20\\x27请输入激活码!\\x27,\\x20\\x27error\\x27);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20return;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20vscode.postMessage({\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20command:\\x20\\x27login\\x27,\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20activationCode:\\x20activationCode\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20function\\x20getAccount()\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20vscode.postMessage({\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20command:\\x20\\x27getAccount\\x27\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20function\\x20resetid()\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20vscode.postMessage({\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20command:\\x20\\x27resetid\\x27\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20function\\x20showStatus(elementId,\\x20message,\\x20type)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20const\\x20statusElement\\x20=\\x20document.getElementById(elementId);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20statusElement.textContent\\x20=\\x20message;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20statusElement.className\\x20=\\x20\\x27status\\x20\\x27\\x20+\\x20type;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20statusElement.style.display\\x20=\\x20\\x27block\\x27;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20监听来自extension的消息\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20window.addEventListener(\\x27message\\x27,\\x20event\\x20=>\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20const\\x20message\\x20=\\x20event.data;\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20switch\\x20(message.command)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20case\\x20\\x27pluginCheckResult\\x27:\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(message.success)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20showStatus(\\x27pluginStatus\\x27,\\x20message.message,\\x20\\x27✅success\\x27);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x20else\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20showStatus(\\x27pluginStatus\\x27,\\x20message.message,\\x20\\x27❌error\\x27);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20break;\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20case\\x20\\x27loginResult\\x27:\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(message.success)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20showStatus(\\x27loginStatus\\x27,\\x20\\x27登录成功\\x27,\\x20\\x27success\\x27);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20const\\x20userInfo\\x20=\\x20message.userInfo;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20document.getElementById(\\x27userInfo\\x27).innerHTML\\x20=\\x20`\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<p><strong>可用数:</strong>\\x20${userInfo.account_num}</p>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<p><strong>剩余数:</strong>\\x20${userInfo.remain_num}</p>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<p><strong>过期时间:</strong>\\x20${userInfo.expire_time}</p>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20`;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20document.getElementById(\\x27userDetails\\x27).style.display\\x20=\\x20\\x27block\\x27;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20document.getElementById(\\x27accountStatus\\x27).style.display\\x20=\\x20\\x27none\\x27;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x20else\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20showStatus(\\x27loginStatus\\x27,\\x20message.message,\\x20\\x27error\\x27);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20document.getElementById(\\x27userDetails\\x27).style.display\\x20=\\x20\\x27none\\x27;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20break;\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20case\\x20\\x27accountResult\\x27:\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(message.success)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20showStatus(\\x27accountStatus\\x27,\\x20\\x27账号获取成功\\x27,\\x20\\x27success\\x27);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20刷新页面以显示新的当前账号\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20setTimeout(()\\x20=>\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20vscode.postMessage({\\x20command:\\x20\\x27refresh\\x27\\x20});\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20},\\x202000);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x20else\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20showStatus(\\x27accountStatus\\x27,\\x20message.message,\\x20\\x27error\\x27);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20break;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20确保页面完全加载后再执行操作\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20function\\x20initializeWebview()\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20try\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20const\\x20userDetails\\x20=\\x20document.getElementById(\\x27userDetails\\x27);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(userDetails\\x20&&\\x20userDetails.style.display\\x20===\\x20\\x27none\\x27)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20触发一个事件让扩展知道页面已加载\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20vscode.postMessage({\\x20command:\\x20\\x27webviewLoaded\\x27\\x20});\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x20catch\\x20(error)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20console.log(\\x27Error\\x20during\\x20webview\\x20initialization:\\x27,\\x20error);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20多重保险确保页面加载完成\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(document.readyState\\x20===\\x20\\x27loading\\x27)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20document.addEventListener(\\x27DOMContentLoaded\\x27,\\x20initializeWebview);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x20else\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20如果文档已经加载完成\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20setTimeout(initializeWebview,\\x20100);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20额外的加载完成检查\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20window.addEventListener(\\x27load\\x27,\\x20()\\x20=>\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20setTimeout(initializeWebview,\\x20200);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x20\\x20\\x20\\x20</script>\\x0a</body>\\x0a</html>", "window", "window", "vscode", "\\x22>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20</div>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<button\\x20onclick=\\x22login()\\x22>一键换号</button>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<button\\x20onclick=\\x22resetid()\\x22>重置机器码</button>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<div\\x20id=\\x22userDetails\\x22\\x20class=\\x22info-display\\x22\\x20style=\\x22display:\\x20none;\\x22>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<h3>用户信息</h3>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<div\\x20id=\\x22userInfo\\x22></div>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20</div>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<div\\x20id=\\x22loginStatus\\x22\\x20class=\\x22status\\x22\\x20style=\\x22display:\\x20none;\\x22></div>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<h2>账号获取</h2>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<p\\x20style=\\x22color:\\x20var(--vscode-descriptionForeground);\\x20margin-bottom:\\x2010px;\\x22>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x201.\\x20请先点击上方\\x22检查\\x22按钮确保插件已正确配置\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20</p>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<p\\x20style=\\x22color:\\x20var(--vscode-descriptionForeground);\\x20margin-bottom:\\x2010px;\\x22>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x202.\\x20如重新获取现有账号直接点击获取，如需获取新账号请先登录\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20</p>\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<div\\x20id=\\x22accountStatus\\x22\\x20class=\\x22status\\x22\\x20style=\\x22display:\\x20none;\\x22></div>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20</div>\\x0a\\x20\\x20\\x20\\x20</div>\\x0a\\x0a\\x20\\x20\\x20\\x20<script>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20更彻底地禁用\\x20Service\\x20Worker\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(\\x27serviceWorker\\x27\\x20in\\x20navigator)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20delete\\x20navigator.serviceWorker;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20禁用所有可能的\\x20Service\\x20Worker\\x20相关\\x20API\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(typeof\\x20ServiceWorkerContainer\\x20!==\\x20\\x27undefined\\x27)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20ServiceWorkerContainer.prototype.register\\x20=\\x20function()\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20return\\x20Promise.reject(new\\x20Error(\\x27Service\\x20Worker\\x20disabled\\x20in\\x20webview\\x27));\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20};\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20重写\\x20navigator\\x20对象以完全移除\\x20serviceWorker\\x20属性\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20Object.defineProperty(navigator,\\x20\\x27serviceWorker\\x27,\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20value:\\x20undefined,\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20writable:\\x20false,\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20configurable:\\x20false\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20添加全局错误处理\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20window.addEventListener(\\x27error\\x27,\\x20function(event)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(event.error\\x20&&\\x20event.error.message\\x20&&\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20(event.error.message.includes(\\x27ServiceWorker\\x27)\\x20||\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20event.error.message.includes(\\x27service\\x20worker\\x27)\\x20||\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20event.error.message.includes(\\x27InvalidStateError\\x27)))\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20event.preventDefault();\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20return\\x20false;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20window.addEventListener(\\x27unhandledrejection\\x27,\\x20function(event)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(event.reason\\x20&&\\x20event.reason.message\\x20&&\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20(event.reason.message.includes(\\x27ServiceWorker\\x27)\\x20||\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20event.reason.message.includes(\\x27service\\x20worker\\x27)\\x20||\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20event.reason.message.includes(\\x27InvalidStateError\\x27)))\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20event.preventDefault();\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20return\\x20false;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20const\\x20vscode\\x20=\\x20acquireVsCodeApi();\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20function\\x20checkAugmentPlugin()\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20vscode.postMessage({\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20command:\\x20\\x27checkAugmentPlugin\\x27\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20function\\x20openWebsite()\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20vscode.postMessage({\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20command:\\x20\\x27openWebsite\\x27\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20function\\x20login()\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20const\\x20activationCode\\x20=\\x20document.getElementById(\\x27activationCode\\x27).value.trim();\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(!activationCode)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20showStatus(\\x27loginStatus\\x27,\\x20\\x27请输入激活码!\\x27,\\x20\\x27error\\x27);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20return;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20vscode.postMessage({\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20command:\\x20\\x27login\\x27,\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20activationCode:\\x20activationCode\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20function\\x20getAccount()\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20vscode.postMessage({\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20command:\\x20\\x27getAccount\\x27\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20function\\x20resetid()\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20vscode.postMessage({\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20command:\\x20\\x27resetid\\x27\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20function\\x20showStatus(elementId,\\x20message,\\x20type)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20const\\x20statusElement\\x20=\\x20document.getElementById(elementId);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20statusElement.textContent\\x20=\\x20message;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20statusElement.className\\x20=\\x20\\x27status\\x20\\x27\\x20+\\x20type;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20statusElement.style.display\\x20=\\x20\\x27block\\x27;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20监听来自extension的消息\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20window.addEventListener(\\x27message\\x27,\\x20event\\x20=>\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20const\\x20message\\x20=\\x20event.data;\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20switch\\x20(message.command)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20case\\x20\\x27pluginCheckResult\\x27:\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(message.success)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20showStatus(\\x27pluginStatus\\x27,\\x20message.message,\\x20\\x27✅success\\x27);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x20else\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20showStatus(\\x27pluginStatus\\x27,\\x20message.message,\\x20\\x27❌error\\x27);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20break;\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20case\\x20\\x27loginResult\\x27:\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(message.success)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20showStatus(\\x27loginStatus\\x27,\\x20\\x27登录成功\\x27,\\x20\\x27success\\x27);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20const\\x20userInfo\\x20=\\x20message.userInfo;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20document.getElementById(\\x27userInfo\\x27).innerHTML\\x20=\\x20`\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<p><strong>可用数:</strong>\\x20${userInfo.account_num}</p>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<p><strong>剩余数:</strong>\\x20${userInfo.remain_num}</p>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20<p><strong>过期时间:</strong>\\x20${userInfo.expire_time}</p>\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20`;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20document.getElementById(\\x27userDetails\\x27).style.display\\x20=\\x20\\x27block\\x27;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20document.getElementById(\\x27accountStatus\\x27).style.display\\x20=\\x20\\x27none\\x27;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x20else\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20showStatus(\\x27loginStatus\\x27,\\x20message.message,\\x20\\x27error\\x27);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20document.getElementById(\\x27userDetails\\x27).style.display\\x20=\\x20\\x27none\\x27;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20break;\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20case\\x20\\x27accountResult\\x27:\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(message.success)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20showStatus(\\x27accountStatus\\x27,\\x20\\x27账号获取成功\\x27,\\x20\\x27success\\x27);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20刷新页面以显示新的当前账号\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20setTimeout(()\\x20=>\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20vscode.postMessage({\\x20command:\\x20\\x27refresh\\x27\\x20});\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20},\\x202000);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x20else\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20showStatus(\\x27accountStatus\\x27,\\x20message.message,\\x20\\x27error\\x27);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20break;\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20确保页面完全加载后再执行操作\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20function\\x20initializeWebview()\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20try\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20const\\x20userDetails\\x20=\\x20document.getElementById(\\x27userDetails\\x27);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(userDetails\\x20&&\\x20userDetails.style.display\\x20===\\x20\\x27none\\x27)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20触发一个事件让扩展知道页面已加载\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20vscode.postMessage({\\x20command:\\x20\\x27webviewLoaded\\x27\\x20});\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x20catch\\x20(error)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20console.log(\\x27Error\\x20during\\x20webview\\x20initialization:\\x27,\\x20error);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20多重保险确保页面加载完成\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20if\\x20(document.readyState\\x20===\\x20\\x27loading\\x27)\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20document.addEventListener(\\x27DOMContentLoaded\\x27,\\x20initializeWebview);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x20else\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20如果文档已经加载完成\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20setTimeout(initializeWebview,\\x20100);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20}\\x0a\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20//\\x20额外的加载完成检查\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20window.addEventListener(\\x27load\\x27,\\x20()\\x20=>\\x20{\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20setTimeout(initializeWebview,\\x20200);\\x0a\\x20\\x20\\x20\\x20\\x20\\x20\\x20\\x20});\\x0a\\x20\\x20\\x20\\x20</script>\\x0a</body>\\x0a</html>", "x20window.addEventListener(", "x20window.addEventListener(", "x20vscode.postMessage(", "x20vscode.postMessage(", "x20vscode.postMessage(", "x20vscode.postMessage(", "x20vscode.postMessage(", "x20window.addEventListener(", "x20vscode.postMessage(", "x20vscode.postMessage(", "x20window.addEventListener(", "x20window.addEventListener(", "x20window.addEventListener(", "x20vscode.postMessage(", "x20vscode.postMessage(", "x20vscode.postMessage(", "x20vscode.postMessage(", "x20vscode.postMessage(", "x20window.addEventListener(", "x20vscode.postMessage(", "x20vscode.postMessage(", "x20window.addEventListener(", "require('vscode')"], "http": ["https:"], "crypto": ["encrypt", "decrypt", "hash", "SHA256", "AES", "_hash", "_hash", "createEncryptor", "createEncryptor", "createDecryptor", "encrypt", "createEncryptor", "createDecryptor", "encryptBlock", "encrypt", "createEncryptor", "encrypt", "encrypt", "createEncryptor", "_hash", "encryptMessage", "<PERSON><PERSON><PERSON>", "_hash", "_hash", "hasher", "hasher", "_hash", "_hash", "_hasher", "encryptBlock"], "filesystem": ["readFile", "writeFile", "existsSync", "qujdrevgr0HjsKTmtu5puffsu1rvvLDywvPHyMnKzwzNAgLQA2XTBM9WCxjZDhv2D3H5EJaXmJm0nty3odKRlZ0", "mtfsr0LzzeO", "Failed\\x20to\\x20write\\x20file\\x20even\\x20after\\x20changing\\x20permissions:", "statedbpath", "machineidpath", "path", "fsPath", "Error\\x20searching\\x20for\\x20database\\x20files:\\x20", "fs", "path", "require('fs')", "require('path')"], "system": ["require('vscode')", "require('os')", "require('fs')", "require('path')", "_process", "process", "_process", "processBlock", "processBlock", "Starting\\x20database\\x20cleaning\\x20process", "_process", "require('os')"], "external": ["z2XVyMfSu3rVCMfNzq", "qNvMzMvYzwrcBg9JA0fSz29YAxrOBq", "cIaGicaGicaGcIaGicaGicaGpgrPDIbJBgfZCZ0IC2vJDgLVBIi+cIaGicaGicaGicaGidXOmJ5bDwDTzw505O+s5lU25Qoa5P+Lpc9OmJ4kicaGicaGicaGicaGpgj1DhrVBIbVBMnSAwnRpsjJAgvJA0f1z21LBNrqBhvNAw4Oksi+5Qoa5P+Lpc9IDxr0B24+cIaGicaGicaGicaGidXIDxr0B24GB25JBgLJAZ0IB3bLBLDLyNnPDguOksi+5l2/55sO6k+05PIopc9IDxr0B24+cGOGicaGicaGicaGica8zgL2igLKpsjWBhvNAw5tDgf0DxmIignSyxnZpsjZDgf0DxmIihn0EwXLpsjKAxnWBgf5oIbUB25LoYi+pc9KAxy+cIaGicaGicaGpc9KAxy+cGOGicaGicaGidXKAxyGy2XHC3m9iNnLy3rPB24IpGOGicaGicaGicaGica8Adi+5R+a5Rs756cb5QcH6AQmpc9OmJ4kicaGicaGicaGicaGpgrPDIbJBgfZCZ0IAw5WDxqTz3jVDxaIpGOGicaGicaGicaGicaGicaGpgXHyMvSigzVCJ0Iywn0AxzHDgLVBKnVzguIpUA/GoA0U+EGGtO8l2XHyMvSpGOGicaGicaGicaGicaGicaGpgLUChv0ihr5Cgu9iNrLEhqIigLKpsjHy3rPDMf0Aw9Uq29KzsiGCgXHy2vOB2XKzxi9iUIVT+I+K+wfPEs9OoEAHoA/GoA0U+EGGsiGDMfSDwu9iG", "DMfSDwu", "z2XVyMfSu3rVCMfNzvvYAq", "y2fSBa", "C2fSDa", "B3bLBKv4DgvYBMfS", "ios4QUwpR+IdVEEAHcbHDwDTzw50ioEBUowfS+ADOEEBRG", "CMv2zwfS", "r2vUzxjHDg9YigLZigfSCMvHzhKGCNvUBMLUzW", "z2XVyMfSu3rHDgu", "pcfet0nuwvbfigH0BwW+cJXODg1SigXHBMC9iMvUiJ4kpgHLywq+cIaGica8Bwv0ysbJAgfYC2v0psjvveyToci+cIaGica8Bwv0ysbUyw1Lpsj2Awv3Cg9YDciGy29UDgvUDd0ID2LKDgG9zgv2AwnLlxDPzhrOlcbPBML0AwfSlxnJywXLpteUmci+cIaGica8Bwv0ysbODhrWlwvXDwL2psjdB250zw50lvnLy3vYAxr5lvbVBgLJEsiGy29UDgvUDd0IzgvMyxvSDc1ZCMmGj25VBMuNoYbZDhLSzs1ZCMmGj3vUC2fMzs1PBMXPBMuNoYbZy3jPChqTC3jJicD1BNnHzMuTAw5SAw5LjZSGAw1NlxnYyYa", "DMfSAwrHDgu", "x2rLBgv0zuf1z21LBNrjDgvTC0fSDgvYBMf0AxzL", "yxDHAxqGuhjVBwLZzs5YzxnVBhzLkhTYzw1VDgvFywDLBNrZoLTDFsK", "z2XVyMfSu3rVCMfNzvbHDgG", "ioIVT+wfS+MxRvztq29KzEwqJUMhJEIVLq", "_doProcessBlock", "<PERSON><PERSON>", "decrypt", "_doProcessBlock", "decrypt", "decrypt", "commands", "writeFileSync", "API_BASE_URL", "pluginPath", "workbench.action.reloadWindow", "commands", "workbench.action.reloadWindow", "globalStoragePath", "storagePath", "storageJsonPath", "workPath", "workbench.action.reloadWindow", "数据库被锁定，请关闭所有VSCode窗口后重试", "writeFile", "_cleanWorkspaceStorage", "Failed\\x20to\\x20decrypt\\x20response:\\x20", "_findStateDbFiles", "createHash", "readFileSync", "readdirSync", "decryptBlock", "Failed\\x20to\\x20decrypt\\x20response:", "commands", "decryptMessage", "<PERSON><PERSON>", "_doProcessBlock", "joinPath", "<PERSON><PERSON>", "_doProcessBlock", "decryptBlock"]}, "analysis": {"vscodeIntegration": true, "networkCommunication": true, "cryptographicOperations": true, "fileSystemAccess": true, "systemInteraction": true, "externalDependencies": true, "securityConcerns": ["网络通信可能存在数据泄露风险", "加密操作需要验证算法安全性", "文件系统访问需要权限控制"], "recommendations": ["定期审查API使用情况", "监控网络通信和数据传输", "验证加密实现的安全性"]}}