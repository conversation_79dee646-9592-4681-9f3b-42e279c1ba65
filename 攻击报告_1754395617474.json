{"config": {"activationCode": "********-c7f4-4bd6-8517-0bfc572ed3e1", "baseUrl": "https://api.smartshift.com", "concurrency": 300, "timeout": 3000, "maxRetries": 1, "keepAlive": true, "maxSockets": 1000, "batchSize": 50, "noDelay": true}, "stats": {"total": 300, "successful": 0, "failed": 300, "totalSwitchAttempts": 0, "successfulSwitches": 0, "totalSwitchedAccounts": 0, "uniqueSwitchedAccounts": {}, "uniqueIPs": {}, "avgTime": 607, "minTime": 40, "maxTime": 1146, "switchSuccessRate": 0}, "results": [{"workerId": 33, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 856}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 37, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 930}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 44, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 929}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 41, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 943}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 27, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 965}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 30, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 940}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 16, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1102}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 21, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1049}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 42, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 994}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 1, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1127}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 35, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1056}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 31, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1069}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 32, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1071}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 9, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1089}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 3, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1110}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 95, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 859}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 10, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1112}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 15, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1100}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 67, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 868}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 297, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 74}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 224, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 347}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 204, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 364}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 191, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 497}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 87, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 859}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 165, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 519}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 235, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 281}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 206, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 348}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 220, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 359}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 269, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 150}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 36, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1008}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 210, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 334}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 241, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 269}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 110, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 804}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 240, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 267}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 208, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 342}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 242, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 266}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 203, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 345}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 70, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 823}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 81, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 825}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 113, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 859}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 212, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 355}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 39, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 926}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 80, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 927}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 193, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 581}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 179, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 582}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 62, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 807}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 153, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 542}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 285, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 40}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 202, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 335}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 127, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 698}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 207, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 337}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 237, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 274}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 277, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 170}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 106, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 849}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 190, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 546}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 233, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 343}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 54, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 890}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 282, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 143}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 118, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 895}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 50, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 831}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 264, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 154}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 215, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 358}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 92, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 711}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 167, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 520}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 270, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 168}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 17, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 837}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 189, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 581}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 125, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 699}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 109, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 875}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 66, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 931}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 156, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 627}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 147, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 697}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 111, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 903}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 262, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 172}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 152, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 618}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 275, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 163}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 256, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 137}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 295, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 147}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 148, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 734}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 178, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 521}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 230, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 335}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 20, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1089}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 214, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 334}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 238, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 277}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 271, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 113}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 141, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 692}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 216, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 325}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 244, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 262}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 251, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 185}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 227, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 330}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 19, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1028}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 99, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 867}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 135, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 704}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 86, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 848}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 290, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 63}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 200, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 521}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 126, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 734}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 184, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 570}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 213, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 359}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 14, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1054}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 6, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1077}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 25, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1060}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 5, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1105}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 22, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1108}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 12, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1090}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 7, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1120}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 4, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1146}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 8, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1078}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 46, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 902}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 28, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1082}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 279, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 138}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 158, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 523}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 132, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 694}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 120, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 728}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 211, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 348}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 176, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 574}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 171, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 581}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 260, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 171}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 53, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 992}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 186, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 518}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 217, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 337}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 130, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 735}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 101, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 851}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 38, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 832}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 205, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 358}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 255, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 189}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 218, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 333}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 196, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 537}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 155, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 587}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 291, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 98}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 177, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 531}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 252, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 165}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 175, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 562}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 45, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 827}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 232, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 368}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 89, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 852}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 280, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 231}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 136, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 685}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 221, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 320}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 187, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 530}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 48, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 871}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 209, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 349}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 231, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 358}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 239, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 263}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 225, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 351}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 283, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 90}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 58, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 984}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 108, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 781}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 74, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 873}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 65, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 808}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 60, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 896}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 265, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 192}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 234, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 300}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 272, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 150}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 274, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 158}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 26, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1081}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 222, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 329}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 103, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 788}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 63, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 813}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 69, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 849}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 180, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 547}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 115, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 904}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 56, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 922}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 172, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 504}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 102, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 793}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 47, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 941}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 96, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 778}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 243, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 244}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 284, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 52}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 185, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 524}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 72, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 832}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 166, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 564}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 55, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 851}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 117, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 921}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 98, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 795}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 94, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 871}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 199, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 613}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 142, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 700}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 134, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 734}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 91, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 899}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 73, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 835}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 138, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 718}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 149, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 603}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 292, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 110}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 286, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 57}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 76, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 956}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 183, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 562}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 151, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 688}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 40, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 915}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 228, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 348}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 154, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 559}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 266, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 182}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 170, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 532}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 100, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 899}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 174, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 570}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 182, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 522}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 122, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 698}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 248, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 247}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 246, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 263}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 168, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 556}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 93, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 865}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 124, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 727}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 287, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 55}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 254, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 167}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 194, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 591}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 57, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 831}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 29, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1076}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 259, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 164}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 13, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1096}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 157, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 568}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 159, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 513}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 119, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 907}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 263, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 169}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 181, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 567}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 78, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 881}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 267, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 187}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 281, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 168}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 236, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 279}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 88, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 853}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 173, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 615}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 146, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 553}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 77, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 881}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 105, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 852}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 129, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 662}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 68, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 856}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 137, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 722}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 85, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 788}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 64, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 806}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 188, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 580}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 289, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 52}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 162, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 520}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 219, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 332}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 273, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 67}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 253, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 165}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 144, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 693}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 198, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 552}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 276, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 121}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 90, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 832}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 49, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 829}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 226, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 350}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 116, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 792}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 250, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 261}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 245, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 262}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 128, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 719}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 247, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 261}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 288, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 65}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 123, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 687}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 268, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 159}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 104, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 928}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 201, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 349}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 163, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 520}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 150, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 696}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 131, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 698}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 140, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 700}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 278, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 59}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 97, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 866}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 143, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 703}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 169, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 586}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 195, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 521}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 11, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1123}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 300, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 60}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 121, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 736}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 249, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 257}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 52, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 904}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 61, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 854}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 229, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 322}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 299, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 74}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 164, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 578}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 139, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 694}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 18, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1090}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 71, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 896}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 2, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1134}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 24, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 1091}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 34, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 924}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 43, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 906}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 83, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 925}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 84, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 849}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 112, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 838}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 82, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 862}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 23, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 971}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 294, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 50}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 197, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 568}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 192, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 573}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 223, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 358}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 133, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 721}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 160, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 532}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 261, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 156}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 257, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 165}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 51, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 830}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 293, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 52}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 75, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 883}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 107, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 850}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 298, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 97}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 161, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 531}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 258, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 152}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 145, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 685}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 79, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 807}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 114, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 845}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 59, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 842}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}, {"workerId": 296, "success": false, "switchResults": [], "switchedAccounts": [], "ip": null, "timing": {"start": *************, "auth": 0, "switches": 0, "total": 445}, "error": "getaddrinfo ENOTFOUND api.smartshift.com"}], "totalTime": 1699}