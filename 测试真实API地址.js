/**
 * 测试真实的API地址进行登录
 * 激活码: 90909420-c7f4-4bd6-8517-0bfc572ed3e1
 * 真实API: https://i0.api.augmentcode.com/
 */

const https = require('https');
const crypto = require('crypto');
const fs = require('fs');

// 配置
const CONFIG = {
    activationCode: "90909420-c7f4-4bd6-8517-0bfc572ed3e1",
    // 使用您手动操作中发现的真实API地址
    baseUrl: "https://i0.api.augmentcode.com",
    timeout: 10000
};

// 生成机器ID
function generateMachineId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// HTTP请求函数
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: data ? JSON.parse(data) : null
                    });
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: null,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(CONFIG.timeout, () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

// 测试真实API登录
async function testRealAPI() {
    console.log('🚀 测试真实API地址登录...');
    console.log(`🌐 真实API: ${CONFIG.baseUrl}`);
    console.log(`🔑 激活码: ${CONFIG.activationCode}`);
    console.log('');
    
    const machineId = generateMachineId();
    const timestamp = Date.now();
    
    // 可能的认证端点
    const authEndpoints = [
        '/api/auth',
        '/api/login', 
        '/api/v1/auth',
        '/api/v1/login',
        '/auth',
        '/login',
        '/api/activate',
        '/activate',
        '/api/token',
        '/token'
    ];
    
    // 构造请求体
    const requestBody = {
        activationCode: CONFIG.activationCode,
        clientVersion: "1.1.1",
        platform: "vscode",
        machineId: machineId,
        timestamp: timestamp
    };
    
    const payload = JSON.stringify(requestBody);
    
    // 构造请求头
    const headers = {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(payload),
        'User-Agent': 'SmartShift-VSCode/1.1.1',
        'Accept': 'application/json',
        'X-Client-Version': '1.1.1',
        'X-Platform': 'vscode'
    };
    
    console.log(`📋 请求体:`, JSON.stringify(requestBody, null, 2));
    console.log('');
    
    for (const endpoint of authEndpoints) {
        try {
            console.log(`🔍 尝试: ${CONFIG.baseUrl}${endpoint}`);
            
            const url = new URL(endpoint, CONFIG.baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: url.port || 443,
                path: url.pathname,
                method: 'POST',
                headers: headers
            };
            
            const response = await makeRequest(options, payload);
            
            console.log(`📊 ${endpoint} - 状态码: ${response.statusCode}`);
            
            if (response.statusCode !== 404) {
                console.log(`📄 响应体: ${response.body}`);
                
                if (response.data) {
                    console.log(`✅ JSON数据:`, JSON.stringify(response.data, null, 2));
                    
                    // 如果登录成功
                    if (response.statusCode === 200 && 
                        (response.data.success || response.data.accessToken || response.data.token)) {
                        
                        console.log('\n🎉 登录成功!');
                        
                        // 保存成功的登录响应
                        const successFile = `成功登录响应_${Date.now()}.json`;
                        fs.writeFileSync(successFile, JSON.stringify({
                            endpoint: endpoint,
                            request: {
                                url: `${CONFIG.baseUrl}${endpoint}`,
                                method: 'POST',
                                headers: headers,
                                body: requestBody
                            },
                            response: {
                                statusCode: response.statusCode,
                                headers: response.headers,
                                body: response.body,
                                data: response.data
                            },
                            timestamp: new Date().toISOString()
                        }, null, 2));
                        
                        console.log(`💾 成功响应已保存到: ${successFile}`);
                        
                        // 分析响应数据
                        console.log('\n🔍 分析登录响应:');
                        if (response.data.accessToken || response.data.token) {
                            console.log(`🔑 Access Token: ${response.data.accessToken || response.data.token}`);
                        }
                        if (response.data.tenantURL) {
                            console.log(`🌐 Tenant URL: ${response.data.tenantURL}`);
                        }
                        if (response.data.email) {
                            console.log(`📧 Email: ${response.data.email}`);
                        }
                        if (response.data.userId || response.data.user_id) {
                            console.log(`👤 User ID: ${response.data.userId || response.data.user_id}`);
                        }
                        if (response.data.accounts && Array.isArray(response.data.accounts)) {
                            console.log(`📊 账号数量: ${response.data.accounts.length}`);
                            response.data.accounts.forEach((acc, i) => {
                                console.log(`   账号${i+1}: ${acc.id || acc.email || acc.name || JSON.stringify(acc)}`);
                            });
                        }
                        if (response.data.refreshToken) {
                            console.log(`🔄 Refresh Token: ${response.data.refreshToken}`);
                        }
                        
                        console.log('\n📋 现在您可以使用这些信息进行后续的token获取操作!');
                        return response.data;
                    }
                }
                
                console.log('');
            }
            
            // 小延迟
            await new Promise(resolve => setTimeout(resolve, 200));
            
        } catch (error) {
            console.log(`❌ ${endpoint} - 错误: ${error.message}`);
        }
    }
    
    console.log('\n❌ 所有端点都登录失败');
    return null;
}

// 主函数
async function main() {
    const loginData = await testRealAPI();
    
    if (loginData) {
        console.log('\n🎯 登录成功! 获得的关键信息:');
        console.log('- accessToken: 用于后续API调用');
        console.log('- tenantURL: API基础地址'); 
        console.log('- accounts: 可用账号列表');
        console.log('- refreshToken: 用于刷新token');
        console.log('\n现在可以利用这些信息获取更多token!');
    } else {
        console.log('\n❌ 登录失败，可能需要尝试其他API地址或参数');
    }
}

// 运行
main().catch(console.error);

/**
 * 使用方法:
 * node 测试真实API地址.js
 * 
 * 功能:
 * 1. 使用真实的API地址进行登录尝试
 * 2. 测试多个可能的认证端点
 * 3. 显示详细的请求和响应信息
 * 4. 自动保存成功的登录响应
 * 5. 分析响应中的关键信息
 */
