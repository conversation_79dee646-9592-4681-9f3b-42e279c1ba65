/**
 * AI智切扩展独立测试套件
 * 不依赖外部模块的综合测试工具
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const http = require('http');
const https = require('https');

class IndependentTestSuite {
    constructor() {
        this.testResults = [];
        this.config = {
            timeout: 10000,
            retries: 3
        };
    }

    /**
     * 记录测试结果
     */
    logTest(category, testName, status, message, details = null) {
        const result = {
            category,
            testName,
            status,
            message,
            details,
            timestamp: new Date().toISOString()
        };
        
        this.testResults.push(result);
        
        const statusIcon = {
            'PASS': '✅',
            'FAIL': '❌',
            'SKIP': '⏭️',
            'WARN': '⚠️'
        };
        
        console.log(`${statusIcon[status]} [${category}] ${testName}: ${message}`);
    }

    /**
     * 文件系统测试
     */
    testFileSystem() {
        console.log('\n📁 开始文件系统测试...');
        
        const requiredFiles = [
            'extension.vsixmanifest',
            'extension/package.json',
            'extension/dist/extension.js',
            'extension/LICENSE.md',
            'extension/README.md'
        ];

        requiredFiles.forEach(file => {
            try {
                if (fs.existsSync(file)) {
                    const stats = fs.statSync(file);
                    this.logTest(
                        'FileSystem',
                        `文件存在性: ${file}`,
                        'PASS',
                        `文件存在，大小: ${stats.size} bytes`
                    );
                } else {
                    this.logTest(
                        'FileSystem',
                        `文件存在性: ${file}`,
                        'FAIL',
                        '文件不存在'
                    );
                }
            } catch (error) {
                this.logTest(
                    'FileSystem',
                    `文件存在性: ${file}`,
                    'FAIL',
                    `检查失败: ${error.message}`
                );
            }
        });

        // 测试文件权限
        try {
            const extensionJs = 'extension/dist/extension.js';
            if (fs.existsSync(extensionJs)) {
                const stats = fs.statSync(extensionJs);
                const isReadable = !!(stats.mode & parseInt('444', 8));
                
                if (isReadable) {
                    this.logTest(
                        'FileSystem',
                        '文件权限检查',
                        'PASS',
                        '主程序文件可读'
                    );
                } else {
                    this.logTest(
                        'FileSystem',
                        '文件权限检查',
                        'FAIL',
                        '主程序文件不可读'
                    );
                }
            }
        } catch (error) {
            this.logTest(
                'FileSystem',
                '文件权限检查',
                'FAIL',
                `权限检查失败: ${error.message}`
            );
        }
    }

    /**
     * 配置文件测试
     */
    testConfiguration() {
        console.log('\n⚙️ 开始配置文件测试...');
        
        // 测试package.json
        try {
            const packagePath = 'extension/package.json';
            if (fs.existsSync(packagePath)) {
                const packageContent = fs.readFileSync(packagePath, 'utf8');
                const packageJson = JSON.parse(packageContent);
                
                const requiredFields = ['name', 'version', 'engines', 'main', 'contributes'];
                const missingFields = requiredFields.filter(field => !packageJson[field]);
                
                if (missingFields.length === 0) {
                    this.logTest(
                        'Configuration',
                        'package.json结构',
                        'PASS',
                        '所有必需字段存在'
                    );
                } else {
                    this.logTest(
                        'Configuration',
                        'package.json结构',
                        'FAIL',
                        `缺少字段: ${missingFields.join(', ')}`
                    );
                }

                // 检查依赖项
                if (packageJson.dependencies) {
                    const deps = Object.keys(packageJson.dependencies);
                    this.logTest(
                        'Configuration',
                        '依赖项检查',
                        'PASS',
                        `发现 ${deps.length} 个依赖项: ${deps.join(', ')}`
                    );
                } else {
                    this.logTest(
                        'Configuration',
                        '依赖项检查',
                        'WARN',
                        '未发现依赖项'
                    );
                }

                // 检查命令配置
                if (packageJson.contributes && packageJson.contributes.commands) {
                    const commands = packageJson.contributes.commands;
                    this.logTest(
                        'Configuration',
                        'VSCode命令配置',
                        'PASS',
                        `配置了 ${commands.length} 个命令`
                    );
                } else {
                    this.logTest(
                        'Configuration',
                        'VSCode命令配置',
                        'FAIL',
                        '未配置VSCode命令'
                    );
                }
            }
        } catch (error) {
            this.logTest(
                'Configuration',
                'package.json解析',
                'FAIL',
                `解析失败: ${error.message}`
            );
        }

        // 测试VSIX清单
        try {
            const manifestPath = 'extension.vsixmanifest';
            if (fs.existsSync(manifestPath)) {
                const manifestContent = fs.readFileSync(manifestPath, 'utf8');
                
                if (manifestContent.includes('<Identity') && 
                    manifestContent.includes('<DisplayName>') &&
                    manifestContent.includes('<Description>')) {
                    this.logTest(
                        'Configuration',
                        'VSIX清单结构',
                        'PASS',
                        '清单文件结构正确'
                    );
                } else {
                    this.logTest(
                        'Configuration',
                        'VSIX清单结构',
                        'FAIL',
                        '清单文件结构不完整'
                    );
                }
            }
        } catch (error) {
            this.logTest(
                'Configuration',
                'VSIX清单解析',
                'FAIL',
                `解析失败: ${error.message}`
            );
        }
    }

    /**
     * 代码质量测试
     */
    testCodeQuality() {
        console.log('\n🔍 开始代码质量测试...');
        
        try {
            const extensionJs = 'extension/dist/extension.js';
            if (fs.existsSync(extensionJs)) {
                const code = fs.readFileSync(extensionJs, 'utf8');
                
                // 基本统计
                const stats = {
                    size: code.length,
                    lines: code.split('\n').length,
                    functions: (code.match(/function/g) || []).length,
                    variables: (code.match(/var\s+/g) || []).length
                };
                
                this.logTest(
                    'CodeQuality',
                    '代码统计',
                    'PASS',
                    `大小: ${(stats.size/1024).toFixed(2)}KB, 函数: ${stats.functions}, 变量: ${stats.variables}`
                );

                // 混淆检测
                const obfuscationIndicators = [
                    /function\s+_0x[a-f0-9]+/g,
                    /var\s+_0x[a-f0-9]+/g,
                    /'[a-zA-Z0-9+/=]{20,}'/g
                ];

                let obfuscationScore = 0;
                obfuscationIndicators.forEach(pattern => {
                    const matches = code.match(pattern) || [];
                    obfuscationScore += matches.length;
                });

                if (obfuscationScore > 100) {
                    this.logTest(
                        'CodeQuality',
                        '代码混淆检测',
                        'WARN',
                        `检测到高度混淆 (评分: ${obfuscationScore})`
                    );
                } else {
                    this.logTest(
                        'CodeQuality',
                        '代码混淆检测',
                        'PASS',
                        '代码混淆程度正常'
                    );
                }

                // 安全模式检测
                const securityPatterns = [
                    { pattern: /eval\s*\(/g, risk: 'HIGH', desc: '动态代码执行' },
                    { pattern: /Function\s*\(/g, risk: 'HIGH', desc: '动态函数创建' },
                    { pattern: /innerHTML/g, risk: 'MEDIUM', desc: 'HTML注入风险' },
                    { pattern: /document\.write/g, risk: 'MEDIUM', desc: '文档写入' }
                ];

                securityPatterns.forEach(({ pattern, risk, desc }) => {
                    const matches = code.match(pattern) || [];
                    if (matches.length > 0) {
                        this.logTest(
                            'CodeQuality',
                            `安全检查: ${desc}`,
                            risk === 'HIGH' ? 'FAIL' : 'WARN',
                            `发现 ${matches.length} 个匹配项`
                        );
                    } else {
                        this.logTest(
                            'CodeQuality',
                            `安全检查: ${desc}`,
                            'PASS',
                            '未发现风险'
                        );
                    }
                });
            }
        } catch (error) {
            this.logTest(
                'CodeQuality',
                '代码质量分析',
                'FAIL',
                `分析失败: ${error.message}`
            );
        }
    }

    /**
     * 加密功能测试
     */
    testCryptography() {
        console.log('\n🔐 开始加密功能测试...');
        
        // 测试1: 基本哈希功能
        try {
            const testData = 'test-data-for-hashing';
            const hash1 = crypto.createHash('sha256').update(testData).digest('hex');
            const hash2 = crypto.createHash('sha256').update(testData).digest('hex');
            const hash3 = crypto.createHash('sha256').update(testData + 'x').digest('hex');
            
            if (hash1 === hash2 && hash1 !== hash3) {
                this.logTest(
                    'Cryptography',
                    'SHA256哈希测试',
                    'PASS',
                    '哈希功能正常'
                );
            } else {
                this.logTest(
                    'Cryptography',
                    'SHA256哈希测试',
                    'FAIL',
                    '哈希结果异常'
                );
            }
        } catch (error) {
            this.logTest(
                'Cryptography',
                'SHA256哈希测试',
                'FAIL',
                `哈希测试失败: ${error.message}`
            );
        }

        // 测试2: 随机数生成
        try {
            const random1 = crypto.randomBytes(32);
            const random2 = crypto.randomBytes(32);
            
            if (random1.length === 32 && random2.length === 32 && !random1.equals(random2)) {
                this.logTest(
                    'Cryptography',
                    '随机数生成测试',
                    'PASS',
                    '随机数生成正常'
                );
            } else {
                this.logTest(
                    'Cryptography',
                    '随机数生成测试',
                    'FAIL',
                    '随机数生成异常'
                );
            }
        } catch (error) {
            this.logTest(
                'Cryptography',
                '随机数生成测试',
                'FAIL',
                `随机数测试失败: ${error.message}`
            );
        }

        // 测试3: 对称加密
        try {
            const key = crypto.randomBytes(32);
            const iv = crypto.randomBytes(16);
            const testData = 'sensitive-test-data';
            
            const cipher = crypto.createCipherGCM('aes-256-gcm');
            cipher.setAAD(Buffer.from('additional-data'));
            let encrypted = cipher.update(testData, 'utf8', 'hex');
            encrypted += cipher.final('hex');
            const authTag = cipher.getAuthTag();
            
            const decipher = crypto.createDecipherGCM('aes-256-gcm');
            decipher.setAAD(Buffer.from('additional-data'));
            decipher.setAuthTag(authTag);
            let decrypted = decipher.update(encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            
            if (decrypted === testData) {
                this.logTest(
                    'Cryptography',
                    'AES-GCM加密测试',
                    'PASS',
                    '对称加密功能正常'
                );
            } else {
                this.logTest(
                    'Cryptography',
                    'AES-GCM加密测试',
                    'FAIL',
                    '加密解密结果不匹配'
                );
            }
        } catch (error) {
            this.logTest(
                'Cryptography',
                'AES-GCM加密测试',
                'FAIL',
                `加密测试失败: ${error.message}`
            );
        }
    }

    /**
     * 输入验证测试
     */
    testInputValidation() {
        console.log('\n🛡️ 开始输入验证测试...');
        
        const testInputs = [
            { input: 'VALID-CODE-123', expected: true, desc: '有效激活码' },
            { input: '', expected: false, desc: '空字符串' },
            { input: 'x'.repeat(100), expected: false, desc: '过长字符串' },
            { input: 'CODE<script>', expected: false, desc: 'XSS攻击载荷' },
            { input: "'; DROP TABLE users; --", expected: false, desc: 'SQL注入载荷' },
            { input: '../../../etc/passwd', expected: false, desc: '路径遍历载荷' }
        ];

        testInputs.forEach(({ input, expected, desc }) => {
            try {
                const isValid = this.validateActivationCode(input);
                
                if (isValid === expected) {
                    this.logTest(
                        'InputValidation',
                        `输入验证: ${desc}`,
                        'PASS',
                        `验证结果正确: ${isValid}`
                    );
                } else {
                    this.logTest(
                        'InputValidation',
                        `输入验证: ${desc}`,
                        'FAIL',
                        `验证结果错误: 期望${expected}, 实际${isValid}`
                    );
                }
            } catch (error) {
                this.logTest(
                    'InputValidation',
                    `输入验证: ${desc}`,
                    'FAIL',
                    `验证异常: ${error.message}`
                );
            }
        });
    }

    /**
     * 激活码验证函数
     */
    validateActivationCode(code) {
        if (!code || typeof code !== 'string') return false;
        if (code.length < 8 || code.length > 64) return false;
        if (!/^[a-zA-Z0-9\-_]+$/.test(code)) return false;
        if (/<script|javascript:|onload=|DROP TABLE|UNION SELECT/i.test(code)) return false;
        if (/[<>\"'&]/.test(code)) return false;
        if (/\.\.[\/\\]/.test(code)) return false;
        return true;
    }

    /**
     * 性能测试
     */
    testPerformance() {
        console.log('\n⚡ 开始性能测试...');
        
        // 测试1: 文件读取性能
        try {
            const startTime = Date.now();
            const extensionJs = 'extension/dist/extension.js';
            
            if (fs.existsSync(extensionJs)) {
                for (let i = 0; i < 10; i++) {
                    fs.readFileSync(extensionJs, 'utf8');
                }
                
                const duration = Date.now() - startTime;
                
                if (duration < 1000) {
                    this.logTest(
                        'Performance',
                        '文件读取性能',
                        'PASS',
                        `10次读取耗时: ${duration}ms`
                    );
                } else {
                    this.logTest(
                        'Performance',
                        '文件读取性能',
                        'WARN',
                        `性能较慢: ${duration}ms`
                    );
                }
            }
        } catch (error) {
            this.logTest(
                'Performance',
                '文件读取性能',
                'FAIL',
                `性能测试失败: ${error.message}`
            );
        }

        // 测试2: 加密性能
        try {
            const startTime = Date.now();
            const testData = 'x'.repeat(1000); // 1KB数据
            
            for (let i = 0; i < 100; i++) {
                crypto.createHash('sha256').update(testData).digest('hex');
            }
            
            const duration = Date.now() - startTime;
            
            if (duration < 500) {
                this.logTest(
                    'Performance',
                    '加密性能测试',
                    'PASS',
                    `100次SHA256耗时: ${duration}ms`
                );
            } else {
                this.logTest(
                    'Performance',
                    '加密性能测试',
                    'WARN',
                    `加密性能较慢: ${duration}ms`
                );
            }
        } catch (error) {
            this.logTest(
                'Performance',
                '加密性能测试',
                'FAIL',
                `性能测试失败: ${error.message}`
            );
        }
    }

    /**
     * 生成测试报告
     */
    generateTestReport() {
        console.log('\n📋 生成测试报告...');
        
        const summary = {
            totalTests: this.testResults.length,
            passed: this.testResults.filter(r => r.status === 'PASS').length,
            failed: this.testResults.filter(r => r.status === 'FAIL').length,
            warnings: this.testResults.filter(r => r.status === 'WARN').length,
            skipped: this.testResults.filter(r => r.status === 'SKIP').length
        };
        
        const categories = {};
        this.testResults.forEach(result => {
            if (!categories[result.category]) {
                categories[result.category] = { total: 0, passed: 0, failed: 0, warnings: 0, skipped: 0 };
            }
            categories[result.category].total++;
            categories[result.category][result.status.toLowerCase()]++;
        });
        
        const report = {
            timestamp: new Date().toISOString(),
            summary: summary,
            categories: categories,
            results: this.testResults,
            recommendations: [
                '定期更新依赖项以修复安全漏洞',
                '实施更严格的输入验证机制',
                '考虑使用更安全的加密算法',
                '添加更多的单元测试覆盖',
                '监控扩展的运行时性能'
            ]
        };
        
        const reportPath = path.join(__dirname, '独立测试报告.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
        
        console.log(`📄 测试报告已保存到: ${reportPath}`);
        
        // 打印摘要
        console.log('\n📊 测试摘要:');
        console.log(`  总测试数: ${summary.totalTests}`);
        console.log(`  通过: ${summary.passed} (${(summary.passed/summary.totalTests*100).toFixed(1)}%)`);
        console.log(`  失败: ${summary.failed} (${(summary.failed/summary.totalTests*100).toFixed(1)}%)`);
        console.log(`  警告: ${summary.warnings} (${(summary.warnings/summary.totalTests*100).toFixed(1)}%)`);
        console.log(`  跳过: ${summary.skipped} (${(summary.skipped/summary.totalTests*100).toFixed(1)}%)`);
        
        console.log('\n📈 分类统计:');
        Object.entries(categories).forEach(([category, stats]) => {
            console.log(`  ${category}: ${stats.passed}/${stats.total} 通过`);
        });
        
        return report;
    }

    /**
     * 运行所有测试
     */
    runAllTests() {
        console.log('🚀 开始独立测试套件...\n');
        
        const startTime = Date.now();
        
        this.testFileSystem();
        this.testConfiguration();
        this.testCodeQuality();
        this.testCryptography();
        this.testInputValidation();
        this.testPerformance();
        
        const report = this.generateTestReport();
        
        const duration = Date.now() - startTime;
        console.log(`\n✅ 所有测试完成! 总耗时: ${duration}ms`);
        
        return report;
    }
}

// 使用示例
if (require.main === module) {
    const testSuite = new IndependentTestSuite();
    testSuite.runAllTests();
}

module.exports = IndependentTestSuite;
