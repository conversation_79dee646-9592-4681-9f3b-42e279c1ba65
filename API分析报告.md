# AI智切扩展API分析报告

## 📊 概览
- **生成时间**: 2025-08-05T11:00:23.925Z
- **总API数量**: 151

## 📋 分类统计
- **vscode**: 39个API
- **http**: 1个API
- **crypto**: 30个API
- **filesystem**: 15个API
- **system**: 12个API
- **external**: 54个API

## 🔍 详细分析

### VSCode API (39个)
- `window.addEventListener`
- `window`
- `window`
- `vscode-augment.signOut`
- `vscode-augment.directLogin`
- `window`
- `vscode-augment.signOut`
- `window`
- `window`
- `window`

... 还有29个

### HTTP API (1个)
- `https:`


### 加密API (30个)
- `encrypt`
- `decrypt`
- `hash`
- `SHA256`
- `AES`
- `_hash`
- `_hash`
- `createEncryptor`
- `createEncryptor`
- `createDecryptor`

... 还有20个

### 文件系统API (15个)
- `readFile`
- `writeFile`
- `existsSync`
- `qujdrevgr0HjsKTmtu5puffsu1rvvLDywvPHyMnKzwzNAgLQA2XTBM9WCxjZDhv2D3H5EJaXmJm0nty3odKRlZ0`
- `mtfsr0LzzeO`
- `Failed\x20to\x20write\x20file\x20even\x20after\x20changing\x20permissions:`
- `statedbpath`
- `machineidpath`
- `path`
- `fsPath`

... 还有5个

### 系统API (12个)
- `require('vscode')`
- `require('os')`
- `require('fs')`
- `require('path')`
- `_process`
- `process`
- `_process`
- `processBlock`
- `processBlock`
- `Starting\x20database\x20cleaning\x20process`

... 还有2个

## 🛡️ 安全分析
- ⚠️ 网络通信可能存在数据泄露风险
- ⚠️ 加密操作需要验证算法安全性
- ⚠️ 文件系统访问需要权限控制

## 💡 建议
- 📌 定期审查API使用情况
- 📌 监控网络通信和数据传输
- 📌 验证加密实现的安全性

---
*此报告由AI智切扩展API提取器自动生成*
