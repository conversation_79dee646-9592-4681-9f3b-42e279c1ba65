/**
 * API响应解密器 - 分析加密的API响应
 * 基于Fernet加密格式分析
 */

const crypto = require('crypto');
const fs = require('fs');

// 加密响应数据
const ENCRYPTED_RESPONSE = "gAAAAABokgF9FINboDqbhOVD39sUlQFWHr8Jmm1nfP8dOblpYVxlFfNqDqS52b0gHDtbp5jPeSdqPRXerwPlAF0IQF0hIvU9cigjALBE3yLCRAPAfk_ziJeQ7nxZMtDKeNVVkan_TptRRcLUzgcD12S3KFa2HUbxCvRlnPQ5TCHvraIEJl9jPCEo4Poc_nnrjWqQAFvaaoWDmNcumynZXZSdQHplvjQ4wdoVZyShhQxMTavwuPNJquQMHPASFMjO-oeQ3Lw4XMUFwIXV3uPrZKh5Hnf1hiLDmLKPXm00ZnrwdVjhhscCbfjb-7gY4LzTVyHl_St1Jd87V395duQrQklayUQLqp3RTA==";

// 可能的密钥列表（基于项目分析）
const POSSIBLE_KEYS = [
    "smartshift-secret-2024-v1.1.1-auth-key",
    "90909420-c7f4-4bd6-8517-0bfc572ed3e1",
    "5e2d40637b44ca6e2b4fa420bad082bc6dedb5aef3cec494a7b6d83c4b50a8ef",
    "aug.202578.xyz",
    "get_user_info",
    "SunnyNet",
    "smartshift-manager",
    "AI智切",
    "1754398997",
    // 从项目文件中提取的可能密钥
    "smartshift",
    "augment",
    "vscode",
    "extension",
    // 常见的默认密钥
    "secret",
    "key",
    "password",
    "auth",
    "token"
];

// Fernet解密尝试
function tryFernetDecrypt(encryptedData, key) {
    try {
        // 尝试使用fernet库
        const fernet = require('fernet');
        const secret = new fernet.Secret(key);
        const token = new fernet.Token({
            secret: secret,
            token: encryptedData,
            ttl: 0
        });
        return token.decode();
    } catch (error) {
        return null;
    }
}

// Base64解密尝试
function tryBase64Decrypt(encryptedData) {
    try {
        const decoded = Buffer.from(encryptedData, 'base64').toString('utf8');
        // 检查是否是有效的JSON或文本
        if (decoded.includes('{') || decoded.includes('user') || decoded.includes('account')) {
            return decoded;
        }
        return null;
    } catch (error) {
        return null;
    }
}

// AES解密尝试
function tryAESDecrypt(encryptedData, key, algorithm = 'aes-256-cbc') {
    try {
        // 假设前16字节是IV
        const data = Buffer.from(encryptedData, 'base64');
        if (data.length < 16) return null;
        
        const iv = data.slice(0, 16);
        const encrypted = data.slice(16);
        
        // 生成密钥哈希
        const keyHash = crypto.createHash('sha256').update(key).digest();
        
        const decipher = crypto.createDecipheriv(algorithm, keyHash, iv);
        let decrypted = decipher.update(encrypted, null, 'utf8');
        decrypted += decipher.final('utf8');
        
        return decrypted;
    } catch (error) {
        return null;
    }
}

// 简单XOR解密
function tryXORDecrypt(encryptedData, key) {
    try {
        const data = Buffer.from(encryptedData, 'base64');
        const keyBuffer = Buffer.from(key);
        const result = Buffer.alloc(data.length);
        
        for (let i = 0; i < data.length; i++) {
            result[i] = data[i] ^ keyBuffer[i % keyBuffer.length];
        }
        
        const decrypted = result.toString('utf8');
        // 检查是否包含可读文本
        if (decrypted.includes('{') || decrypted.includes('user') || decrypted.includes('account')) {
            return decrypted;
        }
        return null;
    } catch (error) {
        return null;
    }
}

// 分析Fernet token结构
function analyzeFernetToken(token) {
    try {
        const data = Buffer.from(token, 'base64');
        
        console.log('🔍 Fernet Token 结构分析:');
        console.log(`总长度: ${data.length} 字节`);
        console.log(`版本: ${data[0]} (应该是0x80)`);
        
        if (data.length >= 9) {
            const timestamp = data.readUInt32BE(1) + (data.readUInt32BE(5) << 32);
            console.log(`时间戳: ${timestamp} (${new Date(timestamp * 1000)})`);
        }
        
        if (data.length >= 25) {
            const iv = data.slice(9, 25);
            console.log(`IV: ${iv.toString('hex')}`);
        }
        
        console.log(`密文长度: ${data.length - 57} 字节`);
        
        if (data.length >= 32) {
            const hmac = data.slice(-32);
            console.log(`HMAC: ${hmac.toString('hex')}`);
        }
        
    } catch (error) {
        console.log(`❌ Token分析失败: ${error.message}`);
    }
}

// 主解密函数
async function decryptAPIResponse() {
    console.log('🔓 开始解密API响应...');
    console.log(`📦 加密数据: ${ENCRYPTED_RESPONSE.substring(0, 50)}...`);
    console.log('');
    
    // 分析token结构
    analyzeFernetToken(ENCRYPTED_RESPONSE);
    console.log('');
    
    const results = [];
    
    // 1. 尝试Fernet解密
    console.log('🔑 尝试Fernet解密...');
    for (const key of POSSIBLE_KEYS) {
        const decrypted = tryFernetDecrypt(ENCRYPTED_RESPONSE, key);
        if (decrypted) {
            results.push({
                method: 'Fernet',
                key: key,
                result: decrypted
            });
            console.log(`✅ Fernet解密成功! 密钥: ${key}`);
            console.log(`📄 解密结果: ${decrypted}`);
        }
    }
    
    // 2. 尝试Base64解密
    console.log('\n📝 尝试Base64解密...');
    const base64Result = tryBase64Decrypt(ENCRYPTED_RESPONSE);
    if (base64Result) {
        results.push({
            method: 'Base64',
            key: 'none',
            result: base64Result
        });
        console.log(`✅ Base64解密成功!`);
        console.log(`📄 解密结果: ${base64Result}`);
    }
    
    // 3. 尝试AES解密
    console.log('\n🔐 尝试AES解密...');
    for (const key of POSSIBLE_KEYS) {
        const decrypted = tryAESDecrypt(ENCRYPTED_RESPONSE, key);
        if (decrypted) {
            results.push({
                method: 'AES-256-CBC',
                key: key,
                result: decrypted
            });
            console.log(`✅ AES解密成功! 密钥: ${key}`);
            console.log(`📄 解密结果: ${decrypted}`);
        }
    }
    
    // 4. 尝试XOR解密
    console.log('\n⚡ 尝试XOR解密...');
    for (const key of POSSIBLE_KEYS) {
        const decrypted = tryXORDecrypt(ENCRYPTED_RESPONSE, key);
        if (decrypted) {
            results.push({
                method: 'XOR',
                key: key,
                result: decrypted
            });
            console.log(`✅ XOR解密成功! 密钥: ${key}`);
            console.log(`📄 解密结果: ${decrypted}`);
        }
    }
    
    // 保存结果
    if (results.length > 0) {
        const reportFile = `API解密结果_${Date.now()}.json`;
        fs.writeFileSync(reportFile, JSON.stringify({
            encryptedData: ENCRYPTED_RESPONSE,
            decryptionResults: results,
            timestamp: new Date().toISOString()
        }, null, 2));
        
        console.log(`\n💾 解密结果已保存: ${reportFile}`);
        console.log(`🎉 成功解密 ${results.length} 种方法!`);
        
        // 返回最可能的结果
        return results[0];
    } else {
        console.log('\n❌ 所有解密方法都失败了');
        console.log('💡 可能需要:');
        console.log('   1. 正确的Fernet密钥');
        console.log('   2. 不同的加密算法');
        console.log('   3. 额外的解密参数');
        return null;
    }
}

// 安装fernet依赖检查
function checkDependencies() {
    try {
        require('fernet');
        return true;
    } catch (error) {
        console.log('⚠️  缺少fernet依赖，请安装: npm install fernet');
        return false;
    }
}

// 启动解密
if (checkDependencies()) {
    decryptAPIResponse().then(result => {
        if (result) {
            console.log('\n🏆 推荐使用的解密方法:');
            console.log(`方法: ${result.method}`);
            console.log(`密钥: ${result.key}`);
            console.log(`结果: ${result.result}`);
        }
    }).catch(console.error);
} else {
    console.log('❌ 依赖检查失败，无法继续');
}

module.exports = {
    decryptAPIResponse,
    tryFernetDecrypt,
    ENCRYPTED_RESPONSE,
    POSSIBLE_KEYS
};
