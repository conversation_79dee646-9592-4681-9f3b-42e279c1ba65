# Ai 智切

AI换号插件, 一键换号, 解决free user account exceeded问题。

![使用说明](https://github.com/dockermen/SmartShift/raw/HEAD/resources/help.png)

## 功能特点

- 🔄 一键切换AI账号
- 🚀 快速解决free user account exceeded问题
- 💼 支持augment
- 🔌 简单易用的VSCode插件
- 🔒 安全可靠的账号管理
****
## 安装要求

- Visual Studio Code 1.23.0 或更高版本

## 适配
- [x] ≤0.516.3

## 安装方法

1. 下载最新版本的 vsix 文件
2. 在 VS Code 中打开扩展市场 (Ctrl+Shift+X)
3. 点击扩展侧边栏右上角的"..."，选择"从VSIX安装"
4. 或者直接将 vsix 文件拖拽到 VS Code 的插件侧边栏

![安装说明](https://github.com/dockermen/SmartShift/raw/HEAD/resources/install.png)

## 使用方法

1. 点击 VS Code 底部状态栏上的 智切 图标
2. 输入激活码
3. 点击登录
4. 点击获取新账号即可切换账号

![使用方法](https://github.com/dockermen/SmartShift/raw/HEAD/resources/help.png)

## 常见问题解决

### 提示 free user account exceeded 怎么办

1. 点击 VS Code 底部状态栏上的 智切 图标
2. 点击 获取

### Linux 系统特别说明

如果您使用的是Linux系统，需要先修改文件权限：

```bash
sudo chown $(whoami) /Users/<USER>/.vscode/extensions
```

## 命令列表

插件提供以下命令：
使用方法: 同时按下键盘的Ctrl+Shift+P，然后在弹出的命令面板中输入命令名称即可执行相应命令。

- `打开智切管理页面`: 智切:账号管理
- `打开智切日志页面`: 智切:查看日志

## 版本历史

当前最新版本：1.1.1

## 问题反馈

如果您在使用过程中遇到任何问题，请通过以下方式反馈：

1. 在插件中使用日志面板记录详细错误信息
2. 联系插件开发者获取支持