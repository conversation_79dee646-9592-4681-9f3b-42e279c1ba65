/**
 * 模拟VSCode扩展的真实登录行为
 * 基于项目中发现的加密和签名机制
 * 激活码: 90909420-c7f4-4bd6-8517-0bfc572ed3e1
 */

const https = require('https');
const crypto = require('crypto');
const fs = require('fs');

// 配置
const CONFIG = {
    activationCode: "90909420-c7f4-4bd6-8517-0bfc572ed3e1",
    // 从项目中发现的真实API信息
    apiBaseUrl: "aHR0cHM6Ly9hdWcuMjAyNTc4Lnh5eg==", // base64编码的API地址
    secretKey: "smartshift-secret-2024-v1.1.1-auth-key", // 从项目中推断的密钥
    timeout: 15000
};

// 解码base64 API地址
function decodeApiUrl() {
    try {
        return Buffer.from(CONFIG.apiBaseUrl, 'base64').toString('utf8');
    } catch (error) {
        return "https://aug.202578.xyz";
    }
}

// 生成真实的VSCode机器ID (模拟telemetry.machineId)
function generateVSCodeMachineId() {
    // 基于系统信息生成更真实的机器ID
    const os = require('os');
    const systemInfo = {
        hostname: os.hostname(),
        platform: os.platform(),
        arch: os.arch(),
        cpus: os.cpus().length,
        timestamp: Date.now()
    };
    
    const hash = crypto.createHash('sha256').update(JSON.stringify(systemInfo)).digest('hex');
    const uuid = hash.substring(0, 8) + '-' + hash.substring(8, 12) + '-4' + hash.substring(13, 16) + '-' + 
                 (parseInt(hash.substring(16, 17), 16) & 0x3 | 0x8).toString(16) + hash.substring(17, 20) + '-' + 
                 hash.substring(20, 32);
    
    return uuid;
}

// 生成API签名 (基于项目中的签名算法)
function generateSignature(payload, timestamp, secretKey) {
    const message = payload + timestamp.toString() + secretKey;
    return crypto.createHash('sha256').update(message).digest('hex');
}

// 加密数据 (模拟项目中的fernet加密)
function encryptData(data, key) {
    const cipher = crypto.createCipher('aes-256-cbc', key);
    let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
}

// HTTP请求函数 (模拟扩展的请求方式)
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: data ? JSON.parse(data) : null
                    });
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: null,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(CONFIG.timeout, () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

// 模拟扩展的完整登录流程
async function simulateExtensionLogin() {
    console.log('🔧 模拟VSCode扩展的真实登录行为...');
    
    const baseUrl = decodeApiUrl();
    console.log(`🌐 解码后的API地址: ${baseUrl}`);
    console.log(`🔑 激活码: ${CONFIG.activationCode}`);
    console.log('');
    
    const machineId = generateVSCodeMachineId();
    const timestamp = Date.now();
    const nonce = crypto.randomBytes(16).toString('hex');
    const sessionId = crypto.randomBytes(32).toString('hex');
    
    console.log(`🖥️  生成的机器ID: ${machineId}`);
    console.log(`⏰ 时间戳: ${timestamp}`);
    console.log(`🎲 随机数: ${nonce}`);
    console.log('');
    
    // 可能的API端点 (基于项目分析)
    const endpoints = [
        '/api/auth',
        '/api/v1/auth',
        '/auth',
        '/api/login',
        '/login',
        '/api/activate',
        '/activate',
        '/api/session',
        '/session',
        '/get_session', // 从项目中发现的端点
        '/api/user/auth',
        '/api/token'
    ];
    
    for (const endpoint of endpoints) {
        try {
            console.log(`🔍 尝试端点: ${endpoint}`);
            
            // 构造请求体 (模拟扩展的真实请求格式)
            const requestBody = {
                activationCode: CONFIG.activationCode,
                clientVersion: "1.1.1",
                platform: "vscode",
                machineId: machineId,
                timestamp: timestamp,
                nonce: nonce,
                sessionId: sessionId,
                deviceInfo: {
                    os: require('os').platform(),
                    arch: require('os').arch(),
                    hostname: require('os').hostname(),
                    nodeVersion: process.version,
                    vscodeVersion: "1.85.0"
                },
                // 添加可能需要的额外字段
                clientId: "vscode-smartshift",
                scope: "read write",
                grantType: "activation_code"
            };
            
            const payload = JSON.stringify(requestBody);
            const signature = generateSignature(payload, timestamp, CONFIG.secretKey);
            
            // 构造完整的请求头 (模拟扩展的真实头部)
            const headers = {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(payload),
                'User-Agent': 'SmartShift-VSCode/1.1.1',
                'Accept': 'application/json',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'en-US,en;q=0.9',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                // 扩展特有的头部
                'X-Client-Version': '1.1.1',
                'X-Platform': 'vscode',
                'X-Timestamp': timestamp.toString(),
                'X-Nonce': nonce,
                'X-Session-ID': sessionId,
                'X-Machine-ID': machineId,
                'X-Signature': signature,
                'X-Request-ID': `req_${timestamp}_${crypto.randomBytes(4).toString('hex')}`,
                // 可能的认证头部
                'X-API-Key': CONFIG.secretKey,
                'X-Client-Secret': crypto.createHash('md5').update(CONFIG.activationCode + machineId).digest('hex'),
                'Authorization': `Bearer ${Buffer.from(CONFIG.activationCode).toString('base64')}`
            };
            
            const url = new URL(endpoint, baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: url.port || 443,
                path: url.pathname,
                method: 'POST',
                headers: headers,
                // 添加SSL选项
                rejectUnauthorized: false,
                secureProtocol: 'TLSv1_2_method'
            };
            
            console.log(`📡 发送请求到: ${baseUrl}${endpoint}`);
            
            const response = await makeRequest(options, payload);
            
            console.log(`📊 状态码: ${response.statusCode}`);
            
            if (response.statusCode !== 404 && response.statusCode !== 502 && response.statusCode !== 503) {
                console.log(`📄 响应体: ${response.body}`);
                
                if (response.data) {
                    console.log(`✅ JSON数据:`, JSON.stringify(response.data, null, 2));
                    
                    // 检查是否登录成功
                    if (response.statusCode === 200 || 
                        (response.data && (response.data.success || response.data.accessToken || response.data.token))) {
                        
                        console.log('\n🎉 登录成功!');
                        
                        // 保存成功的登录信息
                        const successFile = `扩展模拟登录成功_${Date.now()}.json`;
                        fs.writeFileSync(successFile, JSON.stringify({
                            endpoint: endpoint,
                            baseUrl: baseUrl,
                            request: {
                                url: `${baseUrl}${endpoint}`,
                                method: 'POST',
                                headers: headers,
                                body: requestBody
                            },
                            response: {
                                statusCode: response.statusCode,
                                headers: response.headers,
                                body: response.body,
                                data: response.data
                            },
                            machineId: machineId,
                            timestamp: new Date().toISOString()
                        }, null, 2));
                        
                        console.log(`💾 成功信息已保存到: ${successFile}`);
                        
                        return response.data;
                    }
                }
                
                console.log('');
            } else {
                console.log(`❌ ${response.statusCode} - ${response.body}`);
            }
            
            // 延迟避免请求过快
            await new Promise(resolve => setTimeout(resolve, 500));
            
        } catch (error) {
            console.log(`❌ ${endpoint} - 错误: ${error.message}`);
        }
    }
    
    return null;
}

// 主函数
async function main() {
    console.log('🚀 开始模拟VSCode扩展登录...');
    console.log('基于项目逆向分析的完整认证流程');
    console.log('');
    
    const result = await simulateExtensionLogin();
    
    if (result) {
        console.log('\n🏆 模拟登录成功!');
        console.log('现在可以使用获得的信息进行后续操作。');
    } else {
        console.log('\n❌ 所有尝试都失败了');
        console.log('可能需要更深入的逆向分析或不同的认证方式。');
        
        // 建议下一步操作
        console.log('\n💡 建议尝试:');
        console.log('1. 检查扩展是否使用了不同的加密算法');
        console.log('2. 分析扩展的网络请求抓包');
        console.log('3. 查看扩展是否有特殊的认证流程');
        console.log('4. 尝试本地模拟扩展环境');
    }
}

// 运行
main().catch(console.error);

/**
 * 使用方法:
 * node 模拟VSCode扩展登录.js
 * 
 * 特性:
 * 1. 模拟真实的VSCode扩展行为
 * 2. 使用项目中发现的加密和签名机制
 * 3. 完整的请求头和认证信息
 * 4. 多种可能的API端点尝试
 * 5. 详细的调试信息输出
 */
