[2025-08-05T10:49:48.722Z] [SUCCESS] 开始完整构建流程...
[2025-08-05T10:49:48.725Z] [INFO] 检查环境依赖...
[2025-08-05T10:49:48.725Z] [INFO] 执行命令: node --version
[2025-08-05T10:49:48.774Z] [SUCCESS] 命令执行成功: node --version
[2025-08-05T10:49:48.774Z] [SUCCESS] Node.js: v24.3.0
[2025-08-05T10:49:48.775Z] [INFO] 执行命令: npm --version
[2025-08-05T10:49:49.010Z] [SUCCESS] 命令执行成功: npm --version
[2025-08-05T10:49:49.010Z] [SUCCESS] npm: 11.4.2
[2025-08-05T10:49:49.010Z] [INFO] 执行命令: code --version
[2025-08-05T10:49:49.203Z] [SUCCESS] 命令执行成功: code --version
[2025-08-05T10:49:49.203Z] [SUCCESS] VSCode CLI: 1.102.2
[2025-08-05T10:49:49.203Z] [INFO] 创建开发环境配置...
[2025-08-05T10:49:49.204Z] [INFO] 创建目录: C:\Users\<USER>\Desktop\smartshift-manager-1.1.1\build
[2025-08-05T10:49:49.204Z] [INFO] 创建目录: C:\Users\<USER>\Desktop\smartshift-manager-1.1.1\demo
[2025-08-05T10:49:49.205Z] [INFO] 开发配置已创建: C:\Users\<USER>\Desktop\smartshift-manager-1.1.1\build\dev-config.json
[2025-08-05T10:49:49.206Z] [INFO] VSCode工作区配置已创建: C:\Users\<USER>\Desktop\smartshift-manager-1.1.1\build\smartshift-dev.code-workspace
[2025-08-05T10:49:49.206Z] [INFO] 验证扩展包...
[2025-08-05T10:49:49.206Z] [INFO] 文件验证通过: extension.vsixmanifest (2451 bytes)
[2025-08-05T10:49:49.207Z] [INFO] 文件验证通过: extension/package.json (1547 bytes)
[2025-08-05T10:49:49.207Z] [INFO] 文件验证通过: extension/dist/extension.js (214543 bytes)
[2025-08-05T10:49:49.207Z] [INFO] 文件验证通过: extension/LICENSE.md (1090 bytes)
[2025-08-05T10:49:49.208Z] [INFO] 文件验证通过: extension/README.md (1914 bytes)
[2025-08-05T10:49:49.208Z] [SUCCESS] package.json验证通过
[2025-08-05T10:49:49.209Z] [INFO] 主程序验证: 208.26KB, 320个函数
[2025-08-05T10:49:49.209Z] [WARN] 检测到代码混淆
[2025-08-05T10:49:49.210Z] [INFO] 创建安全测试环境...
[2025-08-05T10:49:49.210Z] [INFO] 沙箱配置已创建: C:\Users\<USER>\Desktop\smartshift-manager-1.1.1\build\sandbox-config.json
[2025-08-05T10:49:49.211Z] [INFO] 安全测试脚本已创建: C:\Users\<USER>\Desktop\smartshift-manager-1.1.1\build\security-test.sh
[2025-08-05T10:49:49.211Z] [INFO] 生成演示版本...
[2025-08-05T10:49:49.213Z] [INFO] 演示页面已创建: C:\Users\<USER>\Desktop\smartshift-manager-1.1.1\demo\demo.html
[2025-08-05T10:49:49.213Z] [INFO] 生成构建报告...
[2025-08-05T10:49:49.213Z] [INFO] 检查环境依赖...
[2025-08-05T10:49:49.213Z] [INFO] 执行命令: node --version
[2025-08-05T10:49:49.259Z] [SUCCESS] 命令执行成功: node --version
[2025-08-05T10:49:49.259Z] [SUCCESS] Node.js: v24.3.0
[2025-08-05T10:49:49.259Z] [INFO] 执行命令: npm --version
[2025-08-05T10:49:49.475Z] [SUCCESS] 命令执行成功: npm --version
[2025-08-05T10:49:49.475Z] [SUCCESS] npm: 11.4.2
[2025-08-05T10:49:49.475Z] [INFO] 执行命令: code --version
[2025-08-05T10:49:49.677Z] [SUCCESS] 命令执行成功: code --version
[2025-08-05T10:49:49.678Z] [SUCCESS] VSCode CLI: 1.102.2
[2025-08-05T10:49:49.678Z] [INFO] 构建报告已保存: C:\Users\<USER>\Desktop\smartshift-manager-1.1.1\build\构建报告.json
[2025-08-05T10:49:49.679Z] [SUCCESS] 构建流程完成！
[2025-08-05T10:49:49.679Z] [INFO] 
[2025-08-05T10:49:49.679Z] [INFO] 📦 构建产物:
[2025-08-05T10:49:49.679Z] [INFO]   开发环境: C:\Users\<USER>\Desktop\smartshift-manager-1.1.1\build
[2025-08-05T10:49:49.679Z] [INFO]   演示版本: C:\Users\<USER>\Desktop\smartshift-manager-1.1.1\demo
[2025-08-05T10:49:49.680Z] [INFO]   构建报告: C:\Users\<USER>\Desktop\smartshift-manager-1.1.1\build\构建报告.json
[2025-08-05T10:49:49.680Z] [INFO]   构建日志: C:\Users\<USER>\Desktop\smartshift-manager-1.1.1\build.log
[2025-08-05T10:49:49.680Z] [INFO] 
[2025-08-05T10:49:49.680Z] [INFO] 🚀 下一步操作:
[2025-08-05T10:49:49.680Z] [INFO] 1. 打开演示页面: open demo/demo.html
[2025-08-05T10:49:49.680Z] [INFO] 2. 运行安全测试: bash build/security-test.sh
[2025-08-05T10:49:49.681Z] [INFO] 3. 查看构建报告: cat build/构建报告.json
[2025-08-05T10:49:49.681Z] [INFO] 4. 总耗时: 954ms
[2025-08-05T10:49:56.501Z] [INFO] 启动演示模式...
[2025-08-05T10:49:56.504Z] [INFO] 演示页面路径: C:\Users\<USER>\Desktop\smartshift-manager-1.1.1\demo\demo.html
[2025-08-05T10:49:56.504Z] [INFO] 请在浏览器中打开演示页面进行功能测试
[2025-08-05T10:49:56.575Z] [SUCCESS] 已自动打开演示页面
