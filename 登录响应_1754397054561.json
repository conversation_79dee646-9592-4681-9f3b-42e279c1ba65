{"request": {"url": "https://aug.202578.xyz/api/auth", "method": "POST", "headers": {"Content-Type": "application/json", "Content-Length": 178, "User-Agent": "SmartShift-VSCode/1.1.1", "Accept": "application/json"}, "body": {"activationCode": "90909420-c7f4-4bd6-8517-0bfc572ed3e1", "clientVersion": "1.1.1", "platform": "vscode", "machineId": "19564371-81ae-4cd4-88f4-91a5658aac2a", "timestamp": 1754397052004}}, "response": {"statusCode": 404, "headers": {"date": "Tue, 05 Aug 2025 12:32:36 GMT", "content-type": "application/json", "content-length": "179", "connection": "keep-alive", "server": "cloudflare", "cf-cache-status": "DYNAMIC", "nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}", "report-to": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=d7d3C6ekZ2kN62Kxz5ftIj7fpEnB9Cg%2F5WAgPct5SoPyhgmm2ehNU8eHWM%2F3EzFEuZvc0Pvmko%2BItsG0N4pXuNmKaMYXMFUYphmKZOAL\"}]}", "cf-ray": "96a6516f3af5a00e-AMS", "alt-svc": "h3=\":443\"; ma=86400"}, "body": "{\"error\":\"\\u672a\\u627e\\u5230\",\"message\":\"404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.\"}\n", "data": {"error": "未找到", "message": "404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again."}}, "timestamp": "2025-08-05T12:30:54.561Z"}