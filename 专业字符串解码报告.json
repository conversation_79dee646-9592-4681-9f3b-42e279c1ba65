{"timestamp": "2025-08-05T11:03:57.638Z", "summary": {"totalStrings": 738, "decodedStrings": 686, "totalAPIs": 7}, "decodedStrings": [{"index": 0, "original": "C2HVD1rLEhreB2n1BwvUDa", "decoded": "P2UIQ1eYRuerO2a1OjiHQn", "isDecoded": true}, {"index": 1, "original": "y2LWAgvYDgv4Da", "decoded": "l2YJNtiLQti4Qn", "isDecoded": true}, {"index": 2, "original": "w29IAMvJDcbhzw5LCMf0B3jD", "decoded": "j29VNZiWQpoumj5YPZs0O3wQ", "isDecoded": true}, {"index": 3, "original": "x2zPBMrbBMrqCM9JzxnZu3rHDgveyKzPBgvZ", "decoded": "k2mCOZeoOZedPZ9WmkaMh3eUQtirlXmCOtiM", "isDecoded": true}, {"index": 4, "original": "z2XVyMfSu3rVCMfNzq", "decoded": "m2KIlZsFh3eIPZsAmd", "isDecoded": true}, {"index": 5, "original": "x2LUDM9Rzq", "decoded": "k2YHQZ9Emd", "isDecoded": true}, {"index": 7, "original": "DvfguK0", "decoded": "QisthX0", "isDecoded": true}, {"index": 8, "original": "DuLrBvu", "decoded": "QhYeOih", "isDecoded": true}, {"index": 9, "original": "DgLTzq", "decoded": "QtYGmd", "isDecoded": true}, {"index": 10, "original": "ANLYuKO", "decoded": "NAYLhXB", "isDecoded": true}, {"index": 11, "original": "y29TChv0zq", "decoded": "l29GPui0md", "isDecoded": true}, {"index": 12, "original": "uhPQCfO", "decoded": "huCDPsB", "isDecoded": true}, {"index": 13, "original": "uKHpwgS", "decoded": "hXUcjtF", "isDecoded": true}, {"index": 14, "original": "zxHPC3rZu3LUyW", "decoded": "mkUCP3eMh3YHlJ", "isDecoded": true}, {"index": 15, "original": "yMLUza", "decoded": "lZYHmn", "isDecoded": true}, {"index": 16, "original": "CgvYzM9YBvvWBg9HzcGKmsL7CMv0DxjUifbYB21PC2uUCMvZB2X2zsGPoW", "decoded": "PtiLmZ9LOiiJOt9UmpTXzfY7PZi0QkwHvsoLO21CP2hHPZiMO2K2mfTCbJ", "isDecoded": true}, {"index": 17, "original": "r3P5vMW", "decoded": "e3C5iZJ", "isDecoded": true}, {"index": 18, "original": "lL9HDxrOu2vZC2LVBI5ZyxzLu2vZC2LVBI5HChbSEsG", "decoded": "yY9UQkeBh2iMP2YIOV5MlkmYh2iMP2YIOV5UPuoFRfT", "isDecoded": true}, {"index": 19, "original": "ugf0AcbKB2vZig5VDcbLEgLZDdOG", "decoded": "hts0NpoXO2iMvt5IQpoYRtYMQqBT", "isDecoded": true}, {"index": 20, "original": "zgvJCNLWDe1LC3nHz2u", "decoded": "mtiWPAYJQr1YP3aUm2h", "isDecoded": true}], "apiCalls": {"vscodeAPIs": [], "httpRequests": [], "fileOperations": ["/", "/", "/", "/get_session", "/", "/", "/"], "cryptoOperations": [], "webviewMessages": [], "systemCalls": [], "configKeys": [], "errorMessages": []}, "analysis": {"decodingSuccess": "93.0", "mainFeatures": ["文件系统访问"], "securityRisks": [], "recommendations": ["对所有网络通信使用HTTPS", "避免硬编码敏感信息", "实施适当的输入验证", "使用安全的数据存储方法", "定期进行安全审计"]}}