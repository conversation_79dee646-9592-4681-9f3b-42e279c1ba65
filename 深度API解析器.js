/**
 * AI智切扩展深度API解析器
 * 专门解析混淆代码中的API调用和字符串信息
 */

const fs = require('fs');
const path = require('path');

class DeepAPIAnalyzer {
    constructor() {
        this.extractedAPIs = {
            vscodeCommands: [],
            webviewMessages: [],
            httpEndpoints: [],
            fileOperations: [],
            cryptoOperations: [],
            systemCalls: [],
            configKeys: [],
            errorMessages: [],
            uiElements: []
        };
        this.decodedStrings = [];
        this.functionMappings = new Map();
    }

    /**
     * 分析混淆代码
     */
    analyzeObfuscatedCode(filePath) {
        console.log('🔍 开始深度API解析...');
        
        try {
            const code = fs.readFileSync(filePath, 'utf8');
            
            // 1. 提取字符串数组
            this.extractStringArray(code);
            
            // 2. 分析解码函数
            this.analyzeDecodingFunction(code);
            
            // 3. 提取API调用模式
            this.extractAPIPatterns(code);
            
            // 4. 分析WebView内容
            this.analyzeWebViewContent(code);
            
            // 5. 提取配置和常量
            this.extractConfigurationData(code);
            
            // 6. 分析错误处理
            this.analyzeErrorHandling(code);
            
            console.log('✅ 深度API解析完成');
            return this.generateDetailedReport();
            
        } catch (error) {
            console.error('❌ 深度解析失败:', error.message);
            return null;
        }
    }

    /**
     * 提取字符串数组
     */
    extractStringArray(code) {
        console.log('📋 提取混淆字符串数组...');
        
        // 查找字符串数组定义
        const arrayPattern = /var\s+_0x[a-f0-9]+\s*=\s*\[(.*?)\]/s;
        const match = code.match(arrayPattern);
        
        if (match) {
            const arrayContent = match[1];
            // 提取所有字符串
            const stringPattern = /'([^'\\]|\\.)*'/g;
            const strings = arrayContent.match(stringPattern) || [];
            
            console.log(`📊 发现 ${strings.length} 个混淆字符串`);
            
            strings.forEach((str, index) => {
                const cleanStr = str.slice(1, -1); // 移除引号
                this.decodedStrings.push({
                    index: index,
                    encoded: cleanStr,
                    decoded: this.attemptDecode(cleanStr)
                });
            });
        }
    }

    /**
     * 尝试解码字符串
     */
    attemptDecode(encodedStr) {
        try {
            // 尝试Base64解码
            if (this.isBase64(encodedStr)) {
                const decoded = Buffer.from(encodedStr, 'base64').toString('utf8');
                if (this.isPrintableString(decoded)) {
                    return decoded;
                }
            }
            
            // 尝试URI解码
            try {
                const uriDecoded = decodeURIComponent(encodedStr);
                if (uriDecoded !== encodedStr) {
                    return uriDecoded;
                }
            } catch (e) {}
            
            // 尝试十六进制解码
            if (/^[0-9a-fA-F]+$/.test(encodedStr) && encodedStr.length % 2 === 0) {
                try {
                    const hexDecoded = Buffer.from(encodedStr, 'hex').toString('utf8');
                    if (this.isPrintableString(hexDecoded)) {
                        return hexDecoded;
                    }
                } catch (e) {}
            }
            
            return encodedStr; // 无法解码，返回原始字符串
        } catch (error) {
            return encodedStr;
        }
    }

    /**
     * 判断是否为Base64字符串
     */
    isBase64(str) {
        try {
            return btoa(atob(str)) === str;
        } catch (err) {
            return false;
        }
    }

    /**
     * 判断是否为可打印字符串
     */
    isPrintableString(str) {
        return /^[\x20-\x7E\s]*$/.test(str) && str.length > 0;
    }

    /**
     * 分析解码函数
     */
    analyzeDecodingFunction(code) {
        console.log('🔧 分析解码函数...');
        
        // 查找主要的解码函数
        const decodeFunctionPattern = /function\s+a0_0x53e5\([^)]*\)\s*{([^}]+)}/;
        const match = code.match(decodeFunctionPattern);
        
        if (match) {
            const functionBody = match[1];
            console.log('📋 发现主解码函数 a0_0x53e5');
            
            // 分析解码逻辑
            if (functionBody.includes('decodeURIComponent')) {
                console.log('  - 使用URI解码');
            }
            if (functionBody.includes('fromCharCode')) {
                console.log('  - 使用字符码转换');
            }
            if (functionBody.includes('charAt')) {
                console.log('  - 使用字符位置访问');
            }
        }
    }

    /**
     * 提取API调用模式
     */
    extractAPIPatterns(code) {
        console.log('🔍 提取API调用模式...');
        
        // VSCode API模式
        const vscodePatterns = [
            /vscode\.commands\.registerCommand\(['"]([^'"]+)['"]/g,
            /vscode\.window\.createWebviewPanel/g,
            /vscode\.window\.showInformationMessage/g,
            /vscode\.window\.showErrorMessage/g,
            /vscode\.workspace\.getConfiguration/g,
            /vscode\.env\.openExternal/g
        ];
        
        vscodePatterns.forEach(pattern => {
            const matches = [...code.matchAll(pattern)];
            matches.forEach(match => {
                if (match[1]) {
                    this.extractedAPIs.vscodeCommands.push(match[1]);
                } else {
                    this.extractedAPIs.vscodeCommands.push(match[0]);
                }
            });
        });

        // WebView消息模式
        const webviewPatterns = [
            /command\s*:\s*['"]([^'"]+)['"]/g,
            /postMessage\s*\(\s*{[^}]*command\s*:\s*['"]([^'"]+)['"]/g
        ];
        
        webviewPatterns.forEach(pattern => {
            const matches = [...code.matchAll(pattern)];
            matches.forEach(match => {
                if (match[1] && !this.extractedAPIs.webviewMessages.includes(match[1])) {
                    this.extractedAPIs.webviewMessages.push(match[1]);
                }
            });
        });

        // HTTP端点模式
        const httpPatterns = [
            /https?:\/\/[^\s'"`,)}\]]+/g,
            /\/api\/[^\s'"`,)}\]]+/g
        ];
        
        httpPatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            matches.forEach(url => {
                if (!this.extractedAPIs.httpEndpoints.includes(url)) {
                    this.extractedAPIs.httpEndpoints.push(url);
                }
            });
        });

        // 文件操作模式
        const filePatterns = [
            /fs\.readFileSync/g,
            /fs\.writeFileSync/g,
            /fs\.existsSync/g,
            /path\.join/g,
            /path\.resolve/g
        ];
        
        filePatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            this.extractedAPIs.fileOperations.push(...matches);
        });
    }

    /**
     * 分析WebView内容
     */
    analyzeWebViewContent(code) {
        console.log('🌐 分析WebView内容...');
        
        // 查找HTML内容
        const htmlPattern = /<!DOCTYPE html>.*?<\/html>/s;
        const htmlMatch = code.match(htmlPattern);
        
        if (htmlMatch) {
            const htmlContent = htmlMatch[0];
            
            // 提取JavaScript函数
            const jsFunctionPattern = /function\s+(\w+)\s*\(/g;
            const functions = [...htmlContent.matchAll(jsFunctionPattern)];
            functions.forEach(match => {
                this.extractedAPIs.uiElements.push(`JavaScript函数: ${match[1]}`);
            });
            
            // 提取按钮和表单元素
            const buttonPattern = /<button[^>]*onclick\s*=\s*["']([^"']+)["']/g;
            const buttons = [...htmlContent.matchAll(buttonPattern)];
            buttons.forEach(match => {
                this.extractedAPIs.uiElements.push(`按钮事件: ${match[1]}`);
            });
            
            // 提取输入框
            const inputPattern = /<input[^>]*id\s*=\s*["']([^"']+)["']/g;
            const inputs = [...htmlContent.matchAll(inputPattern)];
            inputs.forEach(match => {
                this.extractedAPIs.uiElements.push(`输入框: ${match[1]}`);
            });
            
            // 提取消息类型
            const messagePattern = /case\s+['"]([^'"]+)['"]\s*:/g;
            const messages = [...htmlContent.matchAll(messagePattern)];
            messages.forEach(match => {
                if (!this.extractedAPIs.webviewMessages.includes(match[1])) {
                    this.extractedAPIs.webviewMessages.push(match[1]);
                }
            });
        }
    }

    /**
     * 提取配置数据
     */
    extractConfigurationData(code) {
        console.log('⚙️ 提取配置数据...');
        
        // 查找配置键
        const configPatterns = [
            /['"]([a-zA-Z][a-zA-Z0-9]*\.[a-zA-Z][a-zA-Z0-9]*)['"]/g,
            /getConfiguration\(['"]([^'"]+)['"]/g
        ];
        
        configPatterns.forEach(pattern => {
            const matches = [...code.matchAll(pattern)];
            matches.forEach(match => {
                if (match[1] && match[1].includes('.')) {
                    this.extractedAPIs.configKeys.push(match[1]);
                }
            });
        });
        
        // 查找文件路径
        const pathPatterns = [
            /['"]([a-zA-Z]:[\\\/][^'"]*)['"]/g,
            /['"](\/[^'"]*)['"]/g,
            /['"]([^'"]*\.json)['"]/g,
            /['"]([^'"]*\.db)['"]/g
        ];
        
        pathPatterns.forEach(pattern => {
            const matches = [...code.matchAll(pattern)];
            matches.forEach(match => {
                if (match[1] && (match[1].includes('/') || match[1].includes('\\'))) {
                    this.extractedAPIs.fileOperations.push(`路径: ${match[1]}`);
                }
            });
        });
    }

    /**
     * 分析错误处理
     */
    analyzeErrorHandling(code) {
        console.log('🚨 分析错误处理...');
        
        // 查找错误消息
        const errorPatterns = [
            /throw\s+new\s+Error\(['"]([^'"]+)['"]/g,
            /console\.error\(['"]([^'"]+)['"]/g,
            /showErrorMessage\(['"]([^'"]+)['"]/g
        ];
        
        errorPatterns.forEach(pattern => {
            const matches = [...code.matchAll(pattern)];
            matches.forEach(match => {
                if (match[1]) {
                    this.extractedAPIs.errorMessages.push(match[1]);
                }
            });
        });
        
        // 查找try-catch块
        const tryCatchPattern = /try\s*{[^}]*}\s*catch\s*\([^)]*\)\s*{[^}]*}/g;
        const tryCatchMatches = code.match(tryCatchPattern) || [];
        console.log(`📊 发现 ${tryCatchMatches.length} 个错误处理块`);
    }

    /**
     * 生成详细报告
     */
    generateDetailedReport() {
        console.log('📋 生成详细API报告...');
        
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalStrings: this.decodedStrings.length,
                decodedStrings: this.decodedStrings.filter(s => s.decoded !== s.encoded).length,
                totalAPIs: Object.values(this.extractedAPIs).reduce((sum, arr) => sum + arr.length, 0)
            },
            decodedStrings: this.decodedStrings.slice(0, 50), // 只显示前50个
            apis: this.extractedAPIs,
            analysis: this.performDeepAnalysis()
        };
        
        // 保存详细报告
        const reportPath = path.join(__dirname, '深度API分析报告.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        // 生成可读报告
        this.generateReadableAPIReport(report);
        
        console.log(`📄 详细报告已保存到: ${reportPath}`);
        return report;
    }

    /**
     * 执行深度分析
     */
    performDeepAnalysis() {
        return {
            coreFeatures: {
                hasWebView: this.extractedAPIs.uiElements.length > 0,
                hasNetworking: this.extractedAPIs.httpEndpoints.length > 0,
                hasFileAccess: this.extractedAPIs.fileOperations.length > 0,
                hasCrypto: this.extractedAPIs.cryptoOperations.length > 0,
                hasCommands: this.extractedAPIs.vscodeCommands.length > 0
            },
            securityConcerns: this.identifySecurityConcerns(),
            functionalityMap: this.mapFunctionality(),
            recommendations: this.generateRecommendations()
        };
    }

    /**
     * 识别安全问题
     */
    identifySecurityConcerns() {
        const concerns = [];
        
        if (this.extractedAPIs.httpEndpoints.length > 0) {
            concerns.push('网络通信可能泄露敏感数据');
        }
        
        if (this.extractedAPIs.fileOperations.some(op => op.includes('writeFile'))) {
            concerns.push('文件写入操作可能存在安全风险');
        }
        
        if (this.decodedStrings.some(s => s.decoded.includes('password') || s.decoded.includes('token'))) {
            concerns.push('代码中可能包含硬编码的敏感信息');
        }
        
        return concerns;
    }

    /**
     * 映射功能
     */
    mapFunctionality() {
        return {
            authentication: this.extractedAPIs.webviewMessages.includes('login'),
            accountManagement: this.extractedAPIs.webviewMessages.includes('getAccount'),
            pluginIntegration: this.extractedAPIs.webviewMessages.includes('checkAugmentPlugin'),
            userInterface: this.extractedAPIs.uiElements.length > 0,
            dataStorage: this.extractedAPIs.fileOperations.some(op => op.includes('write') || op.includes('read'))
        };
    }

    /**
     * 生成建议
     */
    generateRecommendations() {
        return [
            '对所有网络通信实施加密和验证',
            '避免在代码中硬编码敏感信息',
            '实施适当的错误处理和日志记录',
            '定期审查和更新安全措施',
            '使用安全的数据存储方法'
        ];
    }

    /**
     * 生成可读API报告
     */
    generateReadableAPIReport(report) {
        const readableReport = `# AI智切扩展深度API分析报告

## 📊 分析概览
- **分析时间**: ${report.timestamp}
- **总字符串数**: ${report.summary.totalStrings}
- **成功解码**: ${report.summary.decodedStrings}
- **总API数量**: ${report.summary.totalAPIs}

## 🔧 VSCode命令 (${this.extractedAPIs.vscodeCommands.length}个)
${this.extractedAPIs.vscodeCommands.map(cmd => `- \`${cmd}\``).join('\n') || '- 无'}

## 💬 WebView消息类型 (${this.extractedAPIs.webviewMessages.length}个)
${this.extractedAPIs.webviewMessages.map(msg => `- \`${msg}\``).join('\n') || '- 无'}

## 🌐 HTTP端点 (${this.extractedAPIs.httpEndpoints.length}个)
${this.extractedAPIs.httpEndpoints.map(url => `- \`${url}\``).join('\n') || '- 无'}

## 📁 文件操作 (${this.extractedAPIs.fileOperations.length}个)
${this.extractedAPIs.fileOperations.slice(0, 10).map(op => `- \`${op}\``).join('\n') || '- 无'}
${this.extractedAPIs.fileOperations.length > 10 ? `\n... 还有${this.extractedAPIs.fileOperations.length - 10}个` : ''}

## 🎨 UI元素 (${this.extractedAPIs.uiElements.length}个)
${this.extractedAPIs.uiElements.slice(0, 10).map(ui => `- ${ui}`).join('\n') || '- 无'}
${this.extractedAPIs.uiElements.length > 10 ? `\n... 还有${this.extractedAPIs.uiElements.length - 10}个` : ''}

## ⚙️ 配置键 (${this.extractedAPIs.configKeys.length}个)
${this.extractedAPIs.configKeys.map(key => `- \`${key}\``).join('\n') || '- 无'}

## 🚨 错误消息 (${this.extractedAPIs.errorMessages.length}个)
${this.extractedAPIs.errorMessages.slice(0, 5).map(err => `- "${err}"`).join('\n') || '- 无'}
${this.extractedAPIs.errorMessages.length > 5 ? `\n... 还有${this.extractedAPIs.errorMessages.length - 5}个` : ''}

## 🔍 解码字符串示例 (前10个)
${this.decodedStrings.slice(0, 10).map((str, i) => 
    str.decoded !== str.encoded ? 
    `${i + 1}. \`${str.encoded.substring(0, 30)}...\` → \`${str.decoded.substring(0, 50)}...\`` :
    `${i + 1}. \`${str.encoded.substring(0, 50)}...\` (未解码)`
).join('\n')}

## 🛡️ 安全分析
${report.analysis.securityConcerns.map(concern => `- ⚠️ ${concern}`).join('\n')}

## 🎯 功能映射
- **用户认证**: ${report.analysis.functionalityMap.authentication ? '✅' : '❌'}
- **账号管理**: ${report.analysis.functionalityMap.accountManagement ? '✅' : '❌'}
- **插件集成**: ${report.analysis.functionalityMap.pluginIntegration ? '✅' : '❌'}
- **用户界面**: ${report.analysis.functionalityMap.userInterface ? '✅' : '❌'}
- **数据存储**: ${report.analysis.functionalityMap.dataStorage ? '✅' : '❌'}

## 💡 建议
${report.analysis.recommendations.map(rec => `- 📌 ${rec}`).join('\n')}

---
*此报告由AI智切扩展深度API解析器自动生成*
`;

        const readablePath = path.join(__dirname, '深度API分析报告.md');
        fs.writeFileSync(readablePath, readableReport);
        
        console.log(`📄 可读报告已保存到: ${readablePath}`);
    }

    /**
     * 运行深度分析
     */
    run(filePath = 'extension/dist/extension.js') {
        console.log('🚀 启动深度API解析器...\n');
        
        const startTime = Date.now();
        
        try {
            const report = this.analyzeObfuscatedCode(filePath);
            
            if (report) {
                const duration = Date.now() - startTime;
                
                console.log('\n✅ 深度API解析完成！');
                console.log(`⏱️ 总耗时: ${duration}ms`);
                console.log('\n📊 解析结果:');
                console.log(`  解码字符串: ${report.summary.decodedStrings}/${report.summary.totalStrings}`);
                console.log(`  VSCode命令: ${this.extractedAPIs.vscodeCommands.length}个`);
                console.log(`  WebView消息: ${this.extractedAPIs.webviewMessages.length}个`);
                console.log(`  HTTP端点: ${this.extractedAPIs.httpEndpoints.length}个`);
                console.log(`  文件操作: ${this.extractedAPIs.fileOperations.length}个`);
                console.log(`  UI元素: ${this.extractedAPIs.uiElements.length}个`);
                
                return report;
            } else {
                console.error('❌ 深度解析失败');
                return null;
            }
            
        } catch (error) {
            console.error('❌ 执行过程中发生错误:', error.message);
            return null;
        }
    }
}

// 使用示例
if (require.main === module) {
    const analyzer = new DeepAPIAnalyzer();
    analyzer.run();
}

module.exports = DeepAPIAnalyzer;
