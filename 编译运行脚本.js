/**
 * AI智切扩展编译和运行脚本
 * 用于编译、打包和安全运行扩展
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');

class ExtensionBuilder {
    constructor() {
        this.projectRoot = process.cwd();
        this.extensionPath = path.join(this.projectRoot, 'extension');
        this.buildOutput = path.join(this.projectRoot, 'build');
        this.logFile = path.join(this.projectRoot, 'build.log');
    }

    /**
     * 记录日志
     */
    log(message, level = 'INFO') {
        const timestamp = new Date().toISOString();
        const logEntry = `[${timestamp}] [${level}] ${message}\n`;
        
        console.log(`${level === 'ERROR' ? '❌' : level === 'WARN' ? '⚠️' : 'ℹ️'} ${message}`);
        
        // 写入日志文件
        fs.appendFileSync(this.logFile, logEntry);
    }

    /**
     * 执行命令并记录输出
     */
    executeCommand(command, options = {}) {
        this.log(`执行命令: ${command}`);
        
        try {
            const result = execSync(command, {
                cwd: options.cwd || this.projectRoot,
                encoding: 'utf8',
                stdio: 'pipe',
                ...options
            });
            
            this.log(`命令执行成功: ${command}`);
            return { success: true, output: result };
        } catch (error) {
            this.log(`命令执行失败: ${command} - ${error.message}`, 'ERROR');
            return { success: false, error: error.message, output: error.stdout };
        }
    }

    /**
     * 检查环境依赖
     */
    checkEnvironment() {
        this.log('检查环境依赖...');
        
        const requirements = [
            { command: 'node --version', name: 'Node.js' },
            { command: 'npm --version', name: 'npm' }
        ];

        let allPassed = true;

        requirements.forEach(req => {
            const result = this.executeCommand(req.command);
            if (result.success) {
                this.log(`✅ ${req.name}: ${result.output.trim()}`);
            } else {
                this.log(`❌ ${req.name}: 未安装或不可用`, 'ERROR');
                allPassed = false;
            }
        });

        // 检查VSCode CLI
        const vscodeResult = this.executeCommand('code --version');
        if (vscodeResult.success) {
            this.log(`✅ VSCode: ${vscodeResult.output.split('\n')[0]}`);
        } else {
            this.log('⚠️ VSCode CLI未安装，无法直接安装扩展', 'WARN');
        }

        return allPassed;
    }

    /**
     * 安装依赖
     */
    installDependencies() {
        this.log('安装项目依赖...');
        
        if (!fs.existsSync(this.extensionPath)) {
            this.log('扩展目录不存在', 'ERROR');
            return false;
        }

        const packageJsonPath = path.join(this.extensionPath, 'package.json');
        if (!fs.existsSync(packageJsonPath)) {
            this.log('package.json不存在', 'ERROR');
            return false;
        }

        // 安装生产依赖
        const installResult = this.executeCommand('npm install --production', {
            cwd: this.extensionPath
        });

        if (!installResult.success) {
            this.log('依赖安装失败', 'ERROR');
            return false;
        }

        this.log('依赖安装成功');
        return true;
    }

    /**
     * 创建安全的运行环境
     */
    createSafeEnvironment() {
        this.log('创建安全运行环境...');
        
        // 创建构建输出目录
        if (!fs.existsSync(this.buildOutput)) {
            fs.mkdirSync(this.buildOutput, { recursive: true });
        }

        // 创建沙箱配置
        const sandboxConfig = {
            name: 'AI智切扩展沙箱',
            restrictions: {
                network: 'monitored',
                filesystem: 'limited',
                processes: 'restricted'
            },
            monitoring: {
                networkRequests: true,
                fileAccess: true,
                processSpawn: true
            }
        };

        const configPath = path.join(this.buildOutput, 'sandbox-config.json');
        fs.writeFileSync(configPath, JSON.stringify(sandboxConfig, null, 2));
        
        this.log(`沙箱配置已创建: ${configPath}`);
        return true;
    }

    /**
     * 验证扩展包
     */
    validateExtension() {
        this.log('验证扩展包...');
        
        const packageJsonPath = path.join(this.extensionPath, 'package.json');
        const extensionJsPath = path.join(this.extensionPath, 'dist', 'extension.js');
        
        try {
            // 验证package.json
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            
            const requiredFields = ['name', 'version', 'engines', 'main', 'contributes'];
            const missingFields = requiredFields.filter(field => !packageJson[field]);
            
            if (missingFields.length > 0) {
                this.log(`package.json缺少必要字段: ${missingFields.join(', ')}`, 'ERROR');
                return false;
            }

            // 验证主文件
            if (!fs.existsSync(extensionJsPath)) {
                this.log('主程序文件不存在', 'ERROR');
                return false;
            }

            const stats = fs.statSync(extensionJsPath);
            this.log(`主程序文件大小: ${(stats.size / 1024).toFixed(2)} KB`);

            // 检查文件完整性
            const content = fs.readFileSync(extensionJsPath, 'utf8');
            if (content.length === 0) {
                this.log('主程序文件为空', 'ERROR');
                return false;
            }

            this.log('扩展包验证通过');
            return true;

        } catch (error) {
            this.log(`扩展包验证失败: ${error.message}`, 'ERROR');
            return false;
        }
    }

    /**
     * 打包扩展
     */
    packageExtension() {
        this.log('打包扩展...');
        
        // 检查是否安装了vsce
        const vsceCheck = this.executeCommand('npx vsce --version');
        if (!vsceCheck.success) {
            this.log('安装vsce打包工具...');
            const installVsce = this.executeCommand('npm install -g vsce');
            if (!installVsce.success) {
                this.log('vsce安装失败，尝试使用npx', 'WARN');
            }
        }

        // 打包扩展
        const packageCommand = 'npx vsce package --no-dependencies';
        const packageResult = this.executeCommand(packageCommand, {
            cwd: this.extensionPath
        });

        if (packageResult.success) {
            this.log('扩展打包成功');
            
            // 查找生成的vsix文件
            const files = fs.readdirSync(this.extensionPath);
            const vsixFile = files.find(file => file.endsWith('.vsix'));
            
            if (vsixFile) {
                const vsixPath = path.join(this.extensionPath, vsixFile);
                const targetPath = path.join(this.buildOutput, vsixFile);
                
                // 复制到构建目录
                fs.copyFileSync(vsixPath, targetPath);
                this.log(`VSIX文件已复制到: ${targetPath}`);
                
                return targetPath;
            }
        }

        this.log('扩展打包失败', 'ERROR');
        return null;
    }

    /**
     * 安装扩展到VSCode
     */
    installToVSCode(vsixPath) {
        this.log('安装扩展到VSCode...');
        
        if (!vsixPath || !fs.existsSync(vsixPath)) {
            this.log('VSIX文件不存在', 'ERROR');
            return false;
        }

        const installCommand = `code --install-extension "${vsixPath}"`;
        const installResult = this.executeCommand(installCommand);

        if (installResult.success) {
            this.log('扩展安装成功');
            this.log('请重启VSCode以激活扩展');
            return true;
        } else {
            this.log('扩展安装失败', 'ERROR');
            this.log('您可以手动安装VSIX文件：');
            this.log(`1. 打开VSCode`);
            this.log(`2. 按Ctrl+Shift+P打开命令面板`);
            this.log(`3. 输入"Extensions: Install from VSIX"`);
            this.log(`4. 选择文件: ${vsixPath}`);
            return false;
        }
    }

    /**
     * 启动监控模式
     */
    startMonitoring() {
        this.log('启动扩展监控...');
        
        const monitorScript = `
const fs = require('fs');
const path = require('path');

console.log('🔍 扩展监控已启动');
console.log('监控内容：');
console.log('- 网络请求');
console.log('- 文件访问');
console.log('- 进程启动');
console.log('');
console.log('按Ctrl+C停止监控');

// 这里可以添加实际的监控逻辑
// 例如：网络代理、文件系统监控、进程监控等

process.on('SIGINT', () => {
    console.log('\\n监控已停止');
    process.exit(0);
});

// 保持进程运行
setInterval(() => {
    // 监控逻辑
}, 1000);
        `;

        const monitorPath = path.join(this.buildOutput, 'monitor.js');
        fs.writeFileSync(monitorPath, monitorScript);
        
        this.log(`监控脚本已创建: ${monitorPath}`);
        this.log('运行监控: node ' + monitorPath);
        
        return monitorPath;
    }

    /**
     * 执行完整的构建流程
     */
    build() {
        this.log('开始构建流程...');
        
        // 清理日志文件
        if (fs.existsSync(this.logFile)) {
            fs.unlinkSync(this.logFile);
        }

        // 1. 检查环境
        if (!this.checkEnvironment()) {
            this.log('环境检查失败，构建终止', 'ERROR');
            return false;
        }

        // 2. 安装依赖
        if (!this.installDependencies()) {
            this.log('依赖安装失败，构建终止', 'ERROR');
            return false;
        }

        // 3. 验证扩展
        if (!this.validateExtension()) {
            this.log('扩展验证失败，构建终止', 'ERROR');
            return false;
        }

        // 4. 创建安全环境
        if (!this.createSafeEnvironment()) {
            this.log('安全环境创建失败，构建终止', 'ERROR');
            return false;
        }

        // 5. 打包扩展
        const vsixPath = this.packageExtension();
        if (!vsixPath) {
            this.log('扩展打包失败，构建终止', 'ERROR');
            return false;
        }

        // 6. 创建监控脚本
        const monitorPath = this.startMonitoring();

        this.log('构建流程完成！');
        this.log('');
        this.log('📦 构建产物:');
        this.log(`  VSIX文件: ${vsixPath}`);
        this.log(`  监控脚本: ${monitorPath}`);
        this.log(`  构建日志: ${this.logFile}`);
        this.log('');
        this.log('🚀 下一步操作:');
        this.log('1. 安装扩展: node 编译运行脚本.js --install');
        this.log('2. 启动监控: node ' + path.relative(this.projectRoot, monitorPath));
        this.log('3. 查看日志: cat ' + path.relative(this.projectRoot, this.logFile));

        return true;
    }

    /**
     * 安装模式
     */
    install() {
        this.log('安装模式...');
        
        // 查找VSIX文件
        const buildFiles = fs.readdirSync(this.buildOutput);
        const vsixFile = buildFiles.find(file => file.endsWith('.vsix'));
        
        if (!vsixFile) {
            this.log('未找到VSIX文件，请先运行构建', 'ERROR');
            return false;
        }

        const vsixPath = path.join(this.buildOutput, vsixFile);
        return this.installToVSCode(vsixPath);
    }
}

// 命令行接口
if (require.main === module) {
    const builder = new ExtensionBuilder();
    
    const args = process.argv.slice(2);
    
    if (args.includes('--install')) {
        builder.install();
    } else if (args.includes('--help')) {
        console.log('AI智切扩展构建工具');
        console.log('');
        console.log('用法:');
        console.log('  node 编译运行脚本.js          # 执行完整构建');
        console.log('  node 编译运行脚本.js --install # 安装扩展到VSCode');
        console.log('  node 编译运行脚本.js --help    # 显示帮助');
    } else {
        builder.build();
    }
}

module.exports = ExtensionBuilder;
