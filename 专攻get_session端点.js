/**
 * 专门攻击 /get_session 端点
 * 已确认端点存在，需要找到正确的认证头信息
 * 激活码: 90909420-c7f4-4bd6-8517-0bfc572ed3e1
 */

const https = require('https');
const crypto = require('crypto');
const fs = require('fs');

// 配置
const CONFIG = {
    activationCode: "90909420-c7f4-4bd6-8517-0bfc572ed3e1",
    baseUrl: "https://aug.202578.xyz",
    endpoint: "/get_session", // 已确认存在的端点
    timeout: 15000
};

// 生成机器ID
function generateMachineId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// HTTP请求函数
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: data ? JSON.parse(data) : null
                    });
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: null,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(CONFIG.timeout, () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

// 尝试不同的认证头组合
async function tryDifferentAuthHeaders() {
    console.log('🎯 专攻 /get_session 端点');
    console.log(`🌐 目标: ${CONFIG.baseUrl}${CONFIG.endpoint}`);
    console.log(`🔑 激活码: ${CONFIG.activationCode}`);
    console.log('');
    
    const machineId = generateMachineId();
    const timestamp = Date.now();
    const nonce = crypto.randomBytes(16).toString('hex');
    
    // 基础请求体
    const baseRequestBody = {
        activationCode: CONFIG.activationCode,
        clientVersion: "1.1.1",
        platform: "vscode",
        machineId: machineId,
        timestamp: timestamp
    };
    
    // 尝试不同的认证头组合
    const authHeaderCombinations = [
        // 组合1: 基础认证
        {
            name: "基础认证",
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'SmartShift-VSCode/1.1.1',
                'Accept': 'application/json',
                'Authorization': `Bearer ${CONFIG.activationCode}`
            }
        },
        
        // 组合2: API Key认证
        {
            name: "API Key认证",
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'SmartShift-VSCode/1.1.1',
                'Accept': 'application/json',
                'X-API-Key': CONFIG.activationCode,
                'X-Client-Version': '1.1.1'
            }
        },
        
        // 组合3: 完整扩展头部
        {
            name: "完整扩展头部",
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'SmartShift-VSCode/1.1.1',
                'Accept': 'application/json',
                'X-Client-Version': '1.1.1',
                'X-Platform': 'vscode',
                'X-Machine-ID': machineId,
                'X-Timestamp': timestamp.toString(),
                'X-Nonce': nonce,
                'Authorization': `Bearer ${CONFIG.activationCode}`
            }
        },
        
        // 组合4: 签名认证
        {
            name: "签名认证",
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'SmartShift-VSCode/1.1.1',
                'Accept': 'application/json',
                'X-Client-Version': '1.1.1',
                'X-Platform': 'vscode',
                'X-Machine-ID': machineId,
                'X-Timestamp': timestamp.toString(),
                'X-Signature': crypto.createHash('sha256').update(CONFIG.activationCode + timestamp + machineId).digest('hex'),
                'Authorization': `Bearer ${CONFIG.activationCode}`
            }
        },
        
        // 组合5: 会话认证
        {
            name: "会话认证",
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'SmartShift-VSCode/1.1.1',
                'Accept': 'application/json',
                'X-Session-Token': CONFIG.activationCode,
                'X-Client-ID': 'vscode-smartshift',
                'X-Machine-ID': machineId
            }
        },
        
        // 组合6: 自定义认证
        {
            name: "自定义认证",
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'SmartShift-VSCode/1.1.1',
                'Accept': 'application/json',
                'X-Auth-Token': CONFIG.activationCode,
                'X-Client-Secret': crypto.createHash('md5').update(CONFIG.activationCode + machineId).digest('hex'),
                'X-Request-ID': `req_${timestamp}_${nonce}`
            }
        },
        
        // 组合7: 基本HTTP认证
        {
            name: "基本HTTP认证",
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'SmartShift-VSCode/1.1.1',
                'Accept': 'application/json',
                'Authorization': `Basic ${Buffer.from(`vscode:${CONFIG.activationCode}`).toString('base64')}`
            }
        },
        
        // 组合8: 尝试GET请求而不是POST
        {
            name: "GET请求+查询参数",
            method: "GET",
            queryParams: `?activationCode=${CONFIG.activationCode}&machineId=${machineId}&timestamp=${timestamp}`,
            headers: {
                'User-Agent': 'SmartShift-VSCode/1.1.1',
                'Accept': 'application/json',
                'X-Client-Version': '1.1.1'
            }
        }
    ];
    
    for (const combo of authHeaderCombinations) {
        try {
            console.log(`🔍 尝试: ${combo.name}`);
            
            const method = combo.method || 'POST';
            const queryParams = combo.queryParams || '';
            const url = new URL(CONFIG.endpoint + queryParams, CONFIG.baseUrl);
            
            let payload = null;
            if (method === 'POST') {
                payload = JSON.stringify(baseRequestBody);
                combo.headers['Content-Length'] = Buffer.byteLength(payload);
            }
            
            const options = {
                hostname: url.hostname,
                port: url.port || 443,
                path: url.pathname + url.search,
                method: method,
                headers: combo.headers
            };
            
            console.log(`📡 ${method} ${CONFIG.baseUrl}${CONFIG.endpoint}${queryParams}`);
            console.log(`📋 认证头:`, JSON.stringify(combo.headers, null, 2));
            
            const response = await makeRequest(options, payload);
            
            console.log(`📊 状态码: ${response.statusCode}`);
            console.log(`📄 响应体: ${response.body}`);
            
            if (response.data) {
                console.log(`✅ JSON数据:`, JSON.stringify(response.data, null, 2));
            }
            
            // 检查是否成功
            if (response.statusCode === 200) {
                console.log('\n🎉 认证成功!');
                
                // 保存成功的认证信息
                const successFile = `get_session成功_${Date.now()}.json`;
                fs.writeFileSync(successFile, JSON.stringify({
                    authMethod: combo.name,
                    request: {
                        url: `${CONFIG.baseUrl}${CONFIG.endpoint}${queryParams}`,
                        method: method,
                        headers: combo.headers,
                        body: method === 'POST' ? baseRequestBody : null
                    },
                    response: {
                        statusCode: response.statusCode,
                        headers: response.headers,
                        body: response.body,
                        data: response.data
                    },
                    timestamp: new Date().toISOString()
                }, null, 2));
                
                console.log(`💾 成功信息已保存到: ${successFile}`);
                
                return response.data;
            } else if (response.statusCode !== 401) {
                console.log(`⚠️  非401错误，可能有进展: ${response.statusCode}`);
            }
            
            console.log('');
            
            // 延迟避免请求过快
            await new Promise(resolve => setTimeout(resolve, 1000));
            
        } catch (error) {
            console.log(`❌ ${combo.name} - 错误: ${error.message}`);
        }
    }
    
    return null;
}

// 主函数
async function main() {
    console.log('🚀 开始专攻 /get_session 端点');
    console.log('已确认端点存在，尝试不同的认证头组合');
    console.log('');
    
    const result = await tryDifferentAuthHeaders();
    
    if (result) {
        console.log('\n🏆 成功突破认证!');
        console.log('现在可以使用这个认证方式进行后续操作。');
    } else {
        console.log('\n❌ 所有认证方式都失败了');
        console.log('可能需要更复杂的认证机制或特殊的加密算法。');
    }
}

// 运行
main().catch(console.error);

/**
 * 使用方法:
 * node 专攻get_session端点.js
 * 
 * 功能:
 * 1. 专门针对已确认存在的 /get_session 端点
 * 2. 尝试8种不同的认证头组合
 * 3. 包括Bearer、API Key、签名、会话等多种认证方式
 * 4. 自动保存成功的认证信息
 * 5. 详细的调试输出
 */
