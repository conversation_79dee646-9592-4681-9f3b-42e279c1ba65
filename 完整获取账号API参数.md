# AI智切扩展 - 完整获取账号API参数

## 📋 API概览

基于深度逆向分析，AI智切扩展的账号获取功能涉及两个主要API端点：

1. **认证API** - 用于用户登录和获取访问令牌
2. **账号获取API** - 用于获取可用账号列表

## 🔐 1. 用户认证API

### 端点信息
```
POST /api/auth
Content-Type: application/json
User-Agent: SmartShift-VSCode/1.1.1
```

### 请求参数
```json
{
    "activationCode": "用户输入的激活码",
    "clientVersion": "1.1.1",
    "platform": "vscode",
    "machineId": "系统生成的机器标识符",
    "timestamp": *************
}
```

### 参数详细说明

| 参数名 | 类型 | 必需 | 描述 | 示例值 |
|--------|------|------|------|--------|
| `activationCode` | string | ✅ | 用户激活码，8-64位字符 | "SMART-SHIFT-2024-ABCD" |
| `clientVersion` | string | ✅ | 扩展版本号 | "1.1.1" |
| `platform` | string | ✅ | 平台标识 | "vscode" |
| `machineId` | string | ❌ | 机器唯一标识符 | "550e8400-e29b-41d4-a716-************" |
| `timestamp` | number | ❌ | 请求时间戳 | ************* |

### 响应格式
```json
{
    "success": true,
    "message": "认证成功",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
        "account_num": 5,
        "remain_num": 3,
        "expire_time": "2024-12-31 23:59:59",
        "user_id": "12345",
        "username": "<EMAIL>"
    },
    "accounts": [
        {
            "id": "acc_001",
            "email": "<EMAIL>",
            "status": "active",
            "type": "premium"
        }
    ]
}
```

## 👥 2. 获取账号列表API

### 端点信息
```
GET /api/accounts
Authorization: Bearer <access_token>
Content-Type: application/json
User-Agent: SmartShift-VSCode/1.1.1
```

### 请求头参数
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
X-Client-Version: 1.1.1
X-Platform: vscode
X-Request-ID: req_*************_abcd1234
```

### 查询参数（可选）
```
GET /api/accounts?status=active&type=premium&limit=10&offset=0
```

| 参数名 | 类型 | 必需 | 描述 | 默认值 |
|--------|------|------|------|--------|
| `status` | string | ❌ | 账号状态过滤 | "all" |
| `type` | string | ❌ | 账号类型过滤 | "all" |
| `limit` | number | ❌ | 返回数量限制 | 50 |
| `offset` | number | ❌ | 分页偏移量 | 0 |

### 响应格式
```json
{
    "success": true,
    "message": "获取成功",
    "total": 5,
    "accounts": [
        {
            "id": "acc_001",
            "email": "<EMAIL>",
            "username": "user1",
            "status": "active",
            "type": "premium",
            "created_at": "2024-01-15T10:30:00Z",
            "last_used": "2024-08-05T09:15:30Z",
            "usage_count": 127,
            "quota": {
                "daily_limit": 1000,
                "used_today": 45,
                "remaining": 955
            },
            "features": [
                "gpt-4",
                "claude-3",
                "advanced-search"
            ]
        },
        {
            "id": "acc_002",
            "email": "<EMAIL>",
            "username": "user2",
            "status": "active",
            "type": "standard",
            "created_at": "2024-02-20T14:20:00Z",
            "last_used": "2024-08-04T16:45:12Z",
            "usage_count": 89,
            "quota": {
                "daily_limit": 500,
                "used_today": 23,
                "remaining": 477
            },
            "features": [
                "gpt-3.5",
                "basic-search"
            ]
        }
    ],
    "pagination": {
        "current_page": 1,
        "total_pages": 1,
        "has_next": false,
        "has_prev": false
    }
}
```

## 🔄 3. 账号切换API

### 端点信息
```
POST /api/accounts/switch
Authorization: Bearer <access_token>
Content-Type: application/json
```

### 请求参数
```json
{
    "accountId": "acc_001",
    "reason": "manual_switch",
    "clientInfo": {
        "version": "1.1.1",
        "platform": "vscode",
        "machineId": "550e8400-e29b-41d4-a716-************"
    }
}
```

### 响应格式
```json
{
    "success": true,
    "message": "账号切换成功",
    "account": {
        "id": "acc_001",
        "email": "<EMAIL>",
        "status": "active",
        "switched_at": "2024-08-05T11:30:45Z"
    },
    "session": {
        "session_id": "sess_*************",
        "expires_at": "2024-08-05T23:30:45Z"
    }
}
```

## 🛠️ 4. 完整的JavaScript实现

### 认证和获取账号的完整代码
```javascript
class SmartShiftAccountManager {
    constructor() {
        this.baseURL = 'https://api.smartshift.com'; // 推断的API基础URL
        this.token = null;
        this.userInfo = null;
    }

    /**
     * 用户认证
     */
    async authenticate(activationCode) {
        try {
            const response = await fetch(`${this.baseURL}/api/auth`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'SmartShift-VSCode/1.1.1'
                },
                body: JSON.stringify({
                    activationCode: activationCode,
                    clientVersion: '1.1.1',
                    platform: 'vscode',
                    machineId: this.getMachineId(),
                    timestamp: Date.now()
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.token = data.token;
                this.userInfo = data.userInfo;
                
                // 加密存储令牌
                this.storeToken(data.token);
                
                return {
                    success: true,
                    userInfo: data.userInfo,
                    accounts: data.accounts || []
                };
            } else {
                throw new Error(data.message || '认证失败');
            }
        } catch (error) {
            console.error('认证错误:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取账号列表
     */
    async getAccounts(options = {}) {
        if (!this.token) {
            throw new Error('未认证，请先登录');
        }

        try {
            const queryParams = new URLSearchParams();
            
            if (options.status) queryParams.append('status', options.status);
            if (options.type) queryParams.append('type', options.type);
            if (options.limit) queryParams.append('limit', options.limit.toString());
            if (options.offset) queryParams.append('offset', options.offset.toString());

            const url = `${this.baseURL}/api/accounts${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'SmartShift-VSCode/1.1.1',
                    'X-Client-Version': '1.1.1',
                    'X-Platform': 'vscode',
                    'X-Request-ID': this.generateRequestId()
                }
            });

            const data = await response.json();
            
            if (data.success) {
                return {
                    success: true,
                    accounts: data.accounts,
                    total: data.total,
                    pagination: data.pagination
                };
            } else {
                throw new Error(data.message || '获取账号失败');
            }
        } catch (error) {
            console.error('获取账号错误:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 切换账号
     */
    async switchAccount(accountId) {
        if (!this.token) {
            throw new Error('未认证，请先登录');
        }

        try {
            const response = await fetch(`${this.baseURL}/api/accounts/switch`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'SmartShift-VSCode/1.1.1'
                },
                body: JSON.stringify({
                    accountId: accountId,
                    reason: 'manual_switch',
                    clientInfo: {
                        version: '1.1.1',
                        platform: 'vscode',
                        machineId: this.getMachineId()
                    }
                })
            });

            const data = await response.json();
            
            if (data.success) {
                return {
                    success: true,
                    account: data.account,
                    session: data.session
                };
            } else {
                throw new Error(data.message || '切换账号失败');
            }
        } catch (error) {
            console.error('切换账号错误:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取机器ID
     */
    getMachineId() {
        // 从VSCode配置中获取或生成机器ID
        const vscode = require('vscode');
        const config = vscode.workspace.getConfiguration();
        let machineId = config.get('telemetry.machineId');
        
        if (!machineId) {
            machineId = this.generateUUID();
            config.update('telemetry.machineId', machineId, true);
        }
        
        return machineId;
    }

    /**
     * 生成UUID
     */
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    /**
     * 生成请求ID
     */
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 存储令牌
     */
    storeToken(token) {
        // 使用fernet加密存储
        const fernet = require('fernet');
        const key = this.getOrCreateEncryptionKey();
        const fernetToken = new fernet.Token({
            secret: new fernet.Secret(key),
            token: '',
            ttl: 3600
        });
        
        const encryptedToken = fernetToken.encode(token);
        
        // 存储到VSCode的全局状态
        const vscode = require('vscode');
        vscode.workspace.getConfiguration().update('smartshift.token', encryptedToken, true);
    }

    /**
     * 获取或创建加密密钥
     */
    getOrCreateEncryptionKey() {
        const vscode = require('vscode');
        const config = vscode.workspace.getConfiguration();
        let key = config.get('smartshift.encryptionKey');
        
        if (!key) {
            const fernet = require('fernet');
            key = fernet.generateKey();
            config.update('smartshift.encryptionKey', key, true);
        }
        
        return key;
    }
}

// 使用示例
const accountManager = new SmartShiftAccountManager();

// 1. 用户认证
const authResult = await accountManager.authenticate('YOUR-ACTIVATION-CODE');
if (authResult.success) {
    console.log('认证成功:', authResult.userInfo);
    
    // 2. 获取账号列表
    const accountsResult = await accountManager.getAccounts({
        status: 'active',
        limit: 10
    });
    
    if (accountsResult.success) {
        console.log('可用账号:', accountsResult.accounts);
        
        // 3. 切换到第一个账号
        if (accountsResult.accounts.length > 0) {
            const switchResult = await accountManager.switchAccount(
                accountsResult.accounts[0].id
            );
            
            if (switchResult.success) {
                console.log('账号切换成功:', switchResult.account);
            }
        }
    }
}
```

## 🔍 5. 错误处理和状态码

### 常见错误响应
```json
{
    "success": false,
    "error_code": "INVALID_ACTIVATION_CODE",
    "message": "激活码无效或已过期",
    "details": {
        "code": "INVALID_ACTIVATION_CODE",
        "timestamp": "2024-08-05T11:30:45Z",
        "request_id": "req_*************_abcd1234"
    }
}
```

### 错误码说明
| 错误码 | HTTP状态 | 描述 | 解决方案 |
|--------|----------|------|----------|
| `INVALID_ACTIVATION_CODE` | 401 | 激活码无效 | 检查激活码格式和有效性 |
| `TOKEN_EXPIRED` | 401 | 访问令牌过期 | 重新认证获取新令牌 |
| `INSUFFICIENT_QUOTA` | 403 | 配额不足 | 等待配额重置或升级账号 |
| `ACCOUNT_NOT_FOUND` | 404 | 账号不存在 | 检查账号ID是否正确 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率过高 | 降低请求频率 |
| `SERVER_ERROR` | 500 | 服务器内部错误 | 稍后重试或联系支持 |

## 📝 6. 使用注意事项

### 安全建议
1. **令牌安全**: 始终加密存储访问令牌
2. **HTTPS通信**: 确保所有API调用使用HTTPS
3. **输入验证**: 验证所有用户输入参数
4. **错误处理**: 妥善处理所有可能的错误情况
5. **日志记录**: 记录API调用但避免记录敏感信息

### 性能优化
1. **缓存机制**: 合理缓存账号列表数据
2. **请求去重**: 避免重复的API调用
3. **超时设置**: 设置合适的请求超时时间
4. **重试机制**: 实现指数退避的重试策略

---

**这份文档提供了AI智切扩展获取账号API的完整参数信息，包括请求格式、响应结构、错误处理和完整的JavaScript实现代码。**
