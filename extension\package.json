{"name": "smartshift-manager", "displayName": "AI智切", "description": "适配≤0.516.3", "version": "1.1.1", "engines": {"vscode": "^1.74.0"}, "icon": "resources/logo.png", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/dockermen/SmartShift.git"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "smartshift-manager.openPanel", "title": "账号管理", "category": "智切"}, {"command": "smartshift-manager.openLogs", "title": "查看日志", "category": "智切"}]}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@types/node": "24.0.10", "@types/vscode": "^1.74.0", "@vscode/test-electron": "^2.5.2", "babel-loader": "^10.0.0", "eslint": "^9.30.1", "glob": "^11.0.3", "mocha": "^11.7.1", "typescript": "^5.8.3", "webpack-cli": "^6.0.1", "webpack-obfuscator": "^3.5.1"}, "dependencies": {"axios": "^1.6.0", "fernet": "^0.4.0", "uri-js": "^4.4.1"}, "scripts": {"webpack": "webpack-cli --config webpack.config.js", "package": "vsce package --no-dependencies", "build": "npm run webpack && npm run package", "vscode:prepublish": "echo Skipping prepublish (already handled by build)"}}