/**
 * AI智切扩展深度代码还原分析器
 * 专门针对webpack-obfuscator混淆的高级还原工具
 */

const fs = require('fs');
const path = require('path');
const vm = require('vm');
const crypto = require('crypto');

class DeepCodeAnalyzer {
    constructor(filePath) {
        this.filePath = filePath;
        this.obfuscatedCode = '';
        this.stringArray = [];
        this.decodedStrings = new Map();
        this.functionMappings = new Map();
        this.controlFlowMap = new Map();
        this.apiCalls = new Set();
        this.restoredCode = '';
        this.analysisResults = {
            functions: [],
            variables: [],
            apiCalls: [],
            controlFlow: [],
            dataFlow: []
        };
    }

    /**
     * 加载并预处理混淆代码
     */
    loadAndPreprocess() {
        console.log('🔍 加载并预处理混淆代码...');
        
        try {
            this.obfuscatedCode = fs.readFileSync(this.filePath, 'utf8');
            
            // 基本统计
            const stats = {
                size: this.obfuscatedCode.length,
                lines: this.obfuscatedCode.split('\n').length,
                functions: (this.obfuscatedCode.match(/function/g) || []).length,
                variables: (this.obfuscatedCode.match(/var\s+/g) || []).length
            };
            
            console.log('📊 代码统计:');
            console.log(`  文件大小: ${(stats.size / 1024).toFixed(2)} KB`);
            console.log(`  行数: ${stats.lines}`);
            console.log(`  函数数量: ${stats.functions}`);
            console.log(`  变量数量: ${stats.variables}`);
            
            return true;
        } catch (error) {
            console.error('❌ 加载失败:', error.message);
            return false;
        }
    }

    /**
     * 分析字符串数组和解码机制
     */
    analyzeStringDecoding() {
        console.log('\n🔍 分析字符串解码机制...');
        
        // 查找字符串数组定义
        const arrayPattern = /var\s+(_0x[a-f0-9]+)\s*=\s*\[([\s\S]*?)\];/;
        const arrayMatch = this.obfuscatedCode.match(arrayPattern);
        
        if (arrayMatch) {
            const arrayName = arrayMatch[1];
            const arrayContent = arrayMatch[2];
            
            console.log(`📊 找到字符串数组: ${arrayName}`);
            
            // 提取字符串
            const strings = this.extractStringsFromArray(arrayContent);
            this.stringArray = strings;
            
            console.log(`📊 提取到 ${strings.length} 个字符串`);
            
            // 查找解码函数
            this.findDecoderFunctions(arrayName);
            
            return true;
        }
        
        console.log('⚠️ 未找到字符串数组');
        return false;
    }

    /**
     * 从数组内容中提取字符串
     */
    extractStringsFromArray(arrayContent) {
        const strings = [];
        const stringPattern = /'([^'\\]|\\.)*'/g;
        let match;
        
        while ((match = stringPattern.exec(arrayContent)) !== null) {
            const str = match[0].slice(1, -1); // 移除引号
            strings.push(str);
        }
        
        return strings;
    }

    /**
     * 查找解码函数
     */
    findDecoderFunctions(arrayName) {
        console.log('\n🔍 查找解码函数...');
        
        // 查找主解码函数模式
        const decoderPattern = new RegExp(`function\\s+(\\w+)\\s*\\([^)]*\\)[^{]*{[^}]*${arrayName}[^}]*}`, 'g');
        const matches = [...this.obfuscatedCode.matchAll(decoderPattern)];
        
        matches.forEach((match, index) => {
            const funcName = match[1];
            console.log(`📊 找到解码函数 ${index + 1}: ${funcName}`);
            
            this.functionMappings.set(funcName, {
                type: 'decoder',
                originalName: funcName,
                purpose: 'String decoding',
                complexity: this.calculateComplexity(match[0])
            });
        });
        
        return matches.length;
    }

    /**
     * 计算函数复杂度
     */
    calculateComplexity(funcCode) {
        const indicators = {
            loops: (funcCode.match(/for\s*\(|while\s*\(/g) || []).length,
            conditions: (funcCode.match(/if\s*\(/g) || []).length,
            operations: (funcCode.match(/[+\-*/%]/g) || []).length,
            calls: (funcCode.match(/\w+\s*\(/g) || []).length
        };
        
        return indicators.loops * 3 + indicators.conditions * 2 + indicators.operations + indicators.calls;
    }

    /**
     * 识别关键函数和变量
     */
    identifyKeyElements() {
        console.log('\n🔍 识别关键函数和变量...');
        
        // 查找VSCode API调用
        const vscodePatterns = [
            /vscode\.commands\.registerCommand/g,
            /vscode\.window\.createWebviewPanel/g,
            /vscode\.window\.showInformationMessage/g,
            /vscode\.window\.showErrorMessage/g,
            /vscode\.workspace\.getConfiguration/g,
            /vscode\.StatusBarAlignment/g
        ];
        
        vscodePatterns.forEach(pattern => {
            const matches = [...this.obfuscatedCode.matchAll(pattern)];
            matches.forEach(match => {
                this.apiCalls.add({
                    type: 'VSCode API',
                    call: match[0],
                    position: match.index
                });
            });
        });
        
        // 查找网络相关调用
        const networkPatterns = [
            /axios\./g,
            /\.post\s*\(/g,
            /\.get\s*\(/g,
            /XMLHttpRequest/g,
            /fetch\s*\(/g
        ];
        
        networkPatterns.forEach(pattern => {
            const matches = [...this.obfuscatedCode.matchAll(pattern)];
            matches.forEach(match => {
                this.apiCalls.add({
                    type: 'Network',
                    call: match[0],
                    position: match.index
                });
            });
        });
        
        // 查找加密相关调用
        const cryptoPatterns = [
            /fernet/gi,
            /encrypt/gi,
            /decrypt/gi,
            /SHA256/g,
            /crypto\./g
        ];
        
        cryptoPatterns.forEach(pattern => {
            const matches = [...this.obfuscatedCode.matchAll(pattern)];
            matches.forEach(match => {
                this.apiCalls.add({
                    type: 'Crypto',
                    call: match[0],
                    position: match.index
                });
            });
        });
        
        console.log(`📊 识别到 ${this.apiCalls.size} 个关键API调用`);
        
        // 按类型分组显示
        const apiByType = {};
        this.apiCalls.forEach(api => {
            if (!apiByType[api.type]) apiByType[api.type] = [];
            apiByType[api.type].push(api.call);
        });
        
        Object.entries(apiByType).forEach(([type, calls]) => {
            console.log(`  ${type}: ${calls.length} 个调用`);
            calls.slice(0, 3).forEach(call => console.log(`    - ${call}`));
            if (calls.length > 3) console.log(`    ... 还有 ${calls.length - 3} 个`);
        });
    }

    /**
     * 分析控制流程
     */
    analyzeControlFlow() {
        console.log('\n🔍 分析控制流程...');
        
        // 查找主要的控制结构
        const controlStructures = [
            { type: 'if-else', pattern: /if\s*\([^)]+\)\s*{[^}]*}(?:\s*else\s*{[^}]*})?/g },
            { type: 'for-loop', pattern: /for\s*\([^)]*\)\s*{[^}]*}/g },
            { type: 'while-loop', pattern: /while\s*\([^)]+\)\s*{[^}]*}/g },
            { type: 'switch', pattern: /switch\s*\([^)]+\)\s*{[\s\S]*?}/g },
            { type: 'try-catch', pattern: /try\s*{[\s\S]*?}\s*catch\s*\([^)]*\)\s*{[\s\S]*?}/g }
        ];
        
        controlStructures.forEach(({ type, pattern }) => {
            const matches = [...this.obfuscatedCode.matchAll(pattern)];
            if (matches.length > 0) {
                console.log(`📊 ${type}: ${matches.length} 个`);
                this.controlFlowMap.set(type, matches.length);
            }
        });
        
        // 分析函数调用链
        this.analyzeFunctionCallChain();
    }

    /**
     * 分析函数调用链
     */
    analyzeFunctionCallChain() {
        console.log('\n🔍 分析函数调用链...');
        
        // 查找函数定义
        const functionPattern = /function\s+(\w+)\s*\([^)]*\)\s*{/g;
        const functions = [...this.obfuscatedCode.matchAll(functionPattern)];
        
        console.log(`📊 找到 ${functions.length} 个函数定义`);
        
        // 分析每个函数的调用关系
        functions.forEach(match => {
            const funcName = match[1];
            const callPattern = new RegExp(`${funcName}\\s*\\(`, 'g');
            const calls = [...this.obfuscatedCode.matchAll(callPattern)];
            
            if (calls.length > 1) { // 除了定义本身
                this.analysisResults.functions.push({
                    name: funcName,
                    callCount: calls.length - 1,
                    type: this.inferFunctionType(funcName)
                });
            }
        });
        
        // 按调用次数排序，找出核心函数
        this.analysisResults.functions.sort((a, b) => b.callCount - a.callCount);
        
        console.log('📊 核心函数（按调用频率）:');
        this.analysisResults.functions.slice(0, 10).forEach((func, index) => {
            console.log(`  ${index + 1}. ${func.name}: ${func.callCount} 次调用 (${func.type})`);
        });
    }

    /**
     * 推断函数类型
     */
    inferFunctionType(funcName) {
        if (funcName.includes('0x')) return 'obfuscated';
        if (funcName.toLowerCase().includes('decode')) return 'decoder';
        if (funcName.toLowerCase().includes('encrypt')) return 'crypto';
        if (funcName.toLowerCase().includes('request')) return 'network';
        if (funcName.toLowerCase().includes('panel')) return 'ui';
        return 'unknown';
    }

    /**
     * 尝试部分代码还原
     */
    attemptCodeRestoration() {
        console.log('\n🔍 尝试部分代码还原...');
        
        let restoredCode = this.obfuscatedCode;
        let restorations = 0;
        
        // 还原明显的字符串
        const obviousStrings = [
            { pattern: /'smartshift-manager'/g, replacement: '"smartshift-manager"' },
            { pattern: /'openPanel'/g, replacement: '"openPanel"' },
            { pattern: /'openLogs'/g, replacement: '"openLogs"' },
            { pattern: /'vscode'/g, replacement: '"vscode"' },
            { pattern: /'axios'/g, replacement: '"axios"' },
            { pattern: /'fernet'/g, replacement: '"fernet"' }
        ];
        
        obviousStrings.forEach(({ pattern, replacement }) => {
            const matches = restoredCode.match(pattern);
            if (matches) {
                restoredCode = restoredCode.replace(pattern, replacement);
                restorations += matches.length;
            }
        });
        
        // 还原函数名（部分）
        const functionRestorations = [
            { pattern: /function\s+a0_0x53e5/g, replacement: 'function decodeString' },
            { pattern: /function\s+a0_0x561e/g, replacement: 'function getStringArray' }
        ];
        
        functionRestorations.forEach(({ pattern, replacement }) => {
            if (pattern.test(restoredCode)) {
                restoredCode = restoredCode.replace(pattern, replacement);
                restorations++;
            }
        });
        
        this.restoredCode = restoredCode;
        console.log(`📊 完成 ${restorations} 处还原`);
        
        return restorations > 0;
    }

    /**
     * 生成分析报告
     */
    generateAnalysisReport() {
        console.log('\n📋 生成深度分析报告...');
        
        const report = {
            timestamp: new Date().toISOString(),
            fileInfo: {
                path: this.filePath,
                size: this.obfuscatedCode.length,
                complexity: 'Extremely High'
            },
            obfuscationAnalysis: {
                tool: 'webpack-obfuscator',
                techniques: [
                    'String Array Obfuscation',
                    'Function Name Mangling',
                    'Control Flow Flattening',
                    'Dead Code Injection'
                ],
                stringArraySize: this.stringArray.length,
                decoderFunctions: this.functionMappings.size
            },
            keyElements: {
                functions: this.analysisResults.functions.slice(0, 20),
                apiCalls: Array.from(this.apiCalls).slice(0, 50),
                controlFlow: Object.fromEntries(this.controlFlowMap)
            },
            restorationAttempt: {
                success: this.restoredCode !== this.obfuscatedCode,
                restoredSize: this.restoredCode.length,
                improvementRatio: ((this.restoredCode.length - this.obfuscatedCode.length) / this.obfuscatedCode.length * 100).toFixed(2) + '%'
            },
            recommendations: [
                '使用专业的JavaScript反混淆工具（如de4js）',
                '进行动态分析以获取运行时信息',
                '分析网络流量以理解通信协议',
                '在沙箱环境中运行以观察行为'
            ]
        };
        
        // 保存报告
        const reportPath = path.join(__dirname, '深度代码分析报告.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
        
        // 保存部分还原的代码
        if (this.restoredCode !== this.obfuscatedCode) {
            const restoredPath = path.join(__dirname, 'extension_partially_restored.js');
            fs.writeFileSync(restoredPath, this.restoredCode, 'utf8');
            console.log(`📄 部分还原代码已保存到: ${restoredPath}`);
        }
        
        console.log(`📄 深度分析报告已保存到: ${reportPath}`);
        
        return report;
    }

    /**
     * 执行完整的深度分析
     */
    analyze() {
        console.log('🚀 开始深度代码还原分析...\n');
        
        if (!this.loadAndPreprocess()) {
            return false;
        }
        
        this.analyzeStringDecoding();
        this.identifyKeyElements();
        this.analyzeControlFlow();
        this.attemptCodeRestoration();
        
        const report = this.generateAnalysisReport();
        
        console.log('\n✅ 深度分析完成!');
        console.log('\n📊 分析摘要:');
        console.log(`  混淆工具: ${report.obfuscationAnalysis.tool}`);
        console.log(`  字符串数组: ${report.obfuscationAnalysis.stringArraySize} 个`);
        console.log(`  解码函数: ${report.obfuscationAnalysis.decoderFunctions} 个`);
        console.log(`  核心函数: ${report.keyElements.functions.length} 个`);
        console.log(`  API调用: ${report.keyElements.apiCalls.length} 个`);
        console.log(`  还原成功: ${report.restorationAttempt.success ? '是' : '否'}`);
        
        return true;
    }
}

// 使用示例
if (require.main === module) {
    const analyzer = new DeepCodeAnalyzer('./extension/dist/extension.js');
    analyzer.analyze();
}

module.exports = DeepCodeAnalyzer;
