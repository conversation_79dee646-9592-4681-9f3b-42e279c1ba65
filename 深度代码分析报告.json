{"timestamp": "2025-08-05T10:36:58.831Z", "fileInfo": {"path": "./extension/dist/extension.js", "size": 213255, "complexity": "Extremely High"}, "obfuscationAnalysis": {"tool": "webpack-obfuscator", "techniques": ["String Array Obfuscation", "Function Name Mangling", "Control Flow Flattening", "Dead Code Injection"], "stringArraySize": 738, "decoderFunctions": 1}, "keyElements": {"functions": [{"name": "_0x120434", "callCount": 60, "type": "obfuscated"}, {"name": "_0x54ff70", "callCount": 36, "type": "obfuscated"}, {"name": "_0x1a775c", "callCount": 18, "type": "obfuscated"}, {"name": "_0x2e26a6", "callCount": 16, "type": "obfuscated"}, {"name": "_0x41355f", "callCount": 16, "type": "obfuscated"}, {"name": "_0x348388", "callCount": 16, "type": "obfuscated"}, {"name": "_0xa3ff0d", "callCount": 16, "type": "obfuscated"}, {"name": "_0x4064e8", "callCount": 12, "type": "obfuscated"}, {"name": "_0x21d8f0", "callCount": 6, "type": "obfuscated"}, {"name": "_0x2c46d6", "callCount": 4, "type": "obfuscated"}, {"name": "_0x488878", "callCount": 3, "type": "obfuscated"}, {"name": "a0_0x561e", "callCount": 2, "type": "obfuscated"}, {"name": "_0x43bb65", "callCount": 2, "type": "obfuscated"}, {"name": "_0x2df6bb", "callCount": 2, "type": "obfuscated"}, {"name": "_0x5111e6", "callCount": 2, "type": "obfuscated"}, {"name": "_0x19a691", "callCount": 2, "type": "obfuscated"}, {"name": "_0x298951", "callCount": 2, "type": "obfuscated"}, {"name": "_0x2fb07b", "callCount": 2, "type": "obfuscated"}, {"name": "_0x3e4832", "callCount": 2, "type": "obfuscated"}, {"name": "_0x2c8e0e", "callCount": 2, "type": "obfuscated"}], "apiCalls": [{"type": "Crypto", "call": "Encrypt", "position": 35710}, {"type": "Crypto", "call": "Encrypt", "position": 43009}, {"type": "Crypto", "call": "encrypt", "position": 45043}, {"type": "Crypto", "call": "Encrypt", "position": 45645}, {"type": "Crypto", "call": "encrypt", "position": 47351}, {"type": "Crypto", "call": "encrypt", "position": 51429}, {"type": "Crypto", "call": "Encrypt", "position": 51605}, {"type": "Crypto", "call": "encrypt", "position": 53134}, {"type": "Crypto", "call": "encrypt", "position": 53442}, {"type": "Crypto", "call": "Encrypt", "position": 78916}, {"type": "Crypto", "call": "encrypt", "position": 157368}, {"type": "Crypto", "call": "encrypt", "position": 159482}, {"type": "Crypto", "call": "encrypt", "position": 208498}, {"type": "Crypto", "call": "Decrypt", "position": 43210}, {"type": "Crypto", "call": "decrypt", "position": 45205}, {"type": "Crypto", "call": "Decrypt", "position": 45777}, {"type": "Crypto", "call": "decrypt", "position": 51980}, {"type": "Crypto", "call": "decrypt", "position": 53572}, {"type": "Crypto", "call": "decrypt", "position": 113212}, {"type": "Crypto", "call": "decrypt", "position": 133353}, {"type": "Crypto", "call": "decrypt", "position": 133526}, {"type": "Crypto", "call": "decrypt", "position": 159419}, {"type": "Crypto", "call": "decrypt", "position": 209295}, {"type": "Crypto", "call": "SHA256", "position": 25018}], "controlFlow": {"if-else": 23, "for-loop": 10, "while-loop": 1, "switch": 16, "try-catch": 13}}, "restorationAttempt": {"success": true, "restoredSize": 213263, "improvementRatio": "0.00%"}, "recommendations": ["使用专业的JavaScript反混淆工具（如de4js）", "进行动态分析以获取运行时信息", "分析网络流量以理解通信协议", "在沙箱环境中运行以观察行为"]}