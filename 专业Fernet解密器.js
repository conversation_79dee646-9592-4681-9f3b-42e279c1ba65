/**
 * 专业Fernet解密器 - 专门针对Fernet加密的API响应
 * 基于项目分析和常见密钥模式
 */

const crypto = require('crypto');
const fs = require('fs');

// 加密响应数据
const ENCRYPTED_RESPONSE = "gAAAAABokgF9FINboDqbhOVD39sUlQFWHr8Jmm1nfP8dOblpYVxlFfNqDqS52b0gHDtbp5jPeSdqPRXerwPlAF0IQF0hIvU9cigjALBE3yLCRAPAfk_ziJeQ7nxZMtDKeNVVkan_TptRRcLUzgcD12S3KFa2HUbxCvRlnPQ5TCHvraIEJl9jPCEo4Poc_nnrjWqQAFvaaoWDmNcumynZXZSdQHplvjQ4wdoVZyShhQxMTavwuPNJquQMHPASFMjO-oeQ3Lw4XMUFwIXV3uPrZKh5Hnf1hiLDmLKPXm00ZnrwdVjhhscCbfjb-7gY4LzTVyHl_St1Jd87V395duQrQklayUQLqp3RTA==";

// 扩展的密钥候选列表
const EXTENDED_KEYS = [
    // 基础密钥
    "90909420-c7f4-4bd6-8517-0bfc572ed3e1",
    "5e2d40637b44ca6e2b4fa420bad082bc6dedb5aef3cec494a7b6d83c4b50a8ef",
    
    // 时间戳相关
    "1754398997",
    "1754399101", // 从token中提取的时间戳
    
    // API相关
    "aug.202578.xyz",
    "get_user_info",
    "SunnyNet",
    
    // 项目相关
    "smartshift-secret-2024-v1.1.1-auth-key",
    "smartshift-manager",
    "AI智切",
    "SmartShift",
    
    // 常见模式
    "secret-key",
    "auth-key",
    "api-key",
    "encryption-key",
    
    // 组合密钥
    "90909420-c7f4-4bd6-8517-0bfc572ed3e1-secret",
    "aug.202578.xyz-key",
    "SunnyNet-secret",
    "smartshift-2024",
    
    // Base64编码的密钥
    Buffer.from("90909420-c7f4-4bd6-8517-0bfc572ed3e1").toString('base64'),
    Buffer.from("5e2d40637b44ca6e2b4fa420bad082bc6dedb5aef3cec494a7b6d83c4b50a8ef").toString('base64'),
    
    // 32字节密钥（Fernet标准）
    crypto.createHash('sha256').update("90909420-c7f4-4bd6-8517-0bfc572ed3e1").digest('base64'),
    crypto.createHash('sha256').update("5e2d40637b44ca6e2b4fa420bad082bc6dedb5aef3cec494a7b6d83c4b50a8ef").digest('base64'),
    crypto.createHash('sha256').update("aug.202578.xyz").digest('base64'),
    crypto.createHash('sha256').update("SunnyNet").digest('base64'),
    crypto.createHash('sha256').update("smartshift").digest('base64'),
];

// 生成Fernet密钥的标准方法
function generateFernetKey(password) {
    // Fernet需要32字节的URL-safe base64编码密钥
    const hash = crypto.createHash('sha256').update(password).digest();
    return hash.toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
}

// 尝试Fernet解密
async function tryFernetDecryptAdvanced(encryptedData, key) {
    try {
        const fernet = require('fernet');
        
        // 尝试原始密钥
        try {
            const secret = new fernet.Secret(key);
            const token = new fernet.Token({
                secret: secret,
                token: encryptedData,
                ttl: 0
            });
            return token.decode();
        } catch (e) {
            // 忽略错误，继续尝试其他方法
        }
        
        // 尝试生成的Fernet密钥
        try {
            const fernetKey = generateFernetKey(key);
            const secret = new fernet.Secret(fernetKey);
            const token = new fernet.Token({
                secret: secret,
                token: encryptedData,
                ttl: 0
            });
            return token.decode();
        } catch (e) {
            // 忽略错误
        }
        
        // 尝试直接使用SHA256哈希作为密钥
        try {
            const hashKey = crypto.createHash('sha256').update(key).digest('base64');
            const secret = new fernet.Secret(hashKey);
            const token = new fernet.Token({
                secret: secret,
                token: encryptedData,
                ttl: 0
            });
            return token.decode();
        } catch (e) {
            // 忽略错误
        }
        
        return null;
    } catch (error) {
        return null;
    }
}

// 暴力破解常见密钥模式
function generateCommonKeyPatterns() {
    const patterns = [];
    const base = "90909420-c7f4-4bd6-8517-0bfc572ed3e1";
    
    // 添加常见后缀
    const suffixes = ["", "-key", "-secret", "-auth", "-token", "-2024", "-v1", "-api"];
    const prefixes = ["", "key-", "secret-", "auth-", "api-"];
    
    for (const prefix of prefixes) {
        for (const suffix of suffixes) {
            patterns.push(prefix + base + suffix);
        }
    }
    
    // 添加截断版本
    patterns.push(base.substring(0, 32)); // 前32字符
    patterns.push(base.substring(8));     // 去掉前8字符
    
    return patterns;
}

// 主解密函数
async function advancedFernetDecrypt() {
    console.log('🔓 启动专业Fernet解密器...');
    console.log(`📦 目标数据: ${ENCRYPTED_RESPONSE.substring(0, 50)}...`);
    console.log('');
    
    // 生成所有可能的密钥
    const allKeys = [...EXTENDED_KEYS, ...generateCommonKeyPatterns()];
    console.log(`🔑 准备测试 ${allKeys.length} 个密钥...`);
    
    const results = [];
    let tested = 0;
    
    for (const key of allKeys) {
        tested++;
        if (tested % 10 === 0) {
            console.log(`⏳ 已测试 ${tested}/${allKeys.length} 个密钥...`);
        }
        
        const decrypted = await tryFernetDecryptAdvanced(ENCRYPTED_RESPONSE, key);
        if (decrypted) {
            console.log(`\n✅ 解密成功!`);
            console.log(`🔑 成功密钥: ${key}`);
            console.log(`📄 解密结果: ${decrypted}`);
            
            results.push({
                key: key,
                result: decrypted,
                keyType: 'fernet'
            });
            
            // 尝试解析JSON
            try {
                const jsonResult = JSON.parse(decrypted);
                console.log(`📊 JSON解析成功:`);
                console.log(JSON.stringify(jsonResult, null, 2));
                results[results.length - 1].jsonData = jsonResult;
            } catch (e) {
                console.log(`📝 非JSON格式数据`);
            }
            
            break; // 找到第一个成功的就停止
        }
    }
    
    if (results.length === 0) {
        console.log('\n❌ 所有密钥都失败了');
        console.log('💡 可能的原因:');
        console.log('   1. 使用了自定义的加密算法');
        console.log('   2. 密钥不在我们的候选列表中');
        console.log('   3. 需要额外的解密参数');
        
        // 尝试分析token结构寻找线索
        console.log('\n🔍 Token结构分析:');
        try {
            const data = Buffer.from(ENCRYPTED_RESPONSE, 'base64');
            console.log(`版本字节: 0x${data[0].toString(16)}`);
            
            if (data.length >= 9) {
                const timestamp = data.readUInt32BE(1) + (data.readUInt32BE(5) << 32);
                const date = new Date(timestamp * 1000);
                console.log(`时间戳: ${timestamp} (${date.toISOString()})`);
            }
            
            console.log(`总长度: ${data.length} 字节`);
            console.log(`预期密文长度: ${data.length - 57} 字节`);
        } catch (e) {
            console.log('Token结构分析失败');
        }
        
        return null;
    }
    
    // 保存结果
    const reportFile = `Fernet解密结果_${Date.now()}.json`;
    fs.writeFileSync(reportFile, JSON.stringify({
        encryptedData: ENCRYPTED_RESPONSE,
        successfulDecryptions: results,
        totalKeysTested: tested,
        timestamp: new Date().toISOString()
    }, null, 2));
    
    console.log(`\n💾 解密结果已保存: ${reportFile}`);
    return results[0];
}

// 启动解密
advancedFernetDecrypt().then(result => {
    if (result) {
        console.log('\n🎉 解密成功!');
        console.log('现在可以使用这个密钥创建高并发脚本了');
    } else {
        console.log('\n😞 解密失败，需要更多信息');
    }
}).catch(console.error);

module.exports = {
    advancedFernetDecrypt,
    generateFernetKey,
    ENCRYPTED_RESPONSE
};
