/**
 * 探测正确的API端点
 * 激活码: 90909420-c7f4-4bd6-8517-0bfc572ed3e1
 * 域名: https://aug.202578.xyz (已确认存在，但需要找到正确端点)
 */

const https = require('https');
const crypto = require('crypto');
const fs = require('fs');

// 配置
const CONFIG = {
    activationCode: "90909420-c7f4-4bd6-8517-0bfc572ed3e1",
    baseUrl: "https://aug.202578.xyz",
    timeout: 10000
};

// 生成机器ID
function generateMachineId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// HTTP请求函数
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: data ? JSON.parse(data) : null
                    });
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: null,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(CONFIG.timeout, () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

// 探测所有可能的端点
async function probeEndpoints() {
    console.log('🔍 开始探测正确的API端点...');
    console.log(`🌐 目标域名: ${CONFIG.baseUrl}`);
    console.log(`🔑 激活码: ${CONFIG.activationCode}`);
    console.log('');
    
    // 可能的端点列表
    const endpoints = [
        // 认证相关
        '/api/auth',
        '/api/login',
        '/api/v1/auth',
        '/api/v1/login',
        '/auth',
        '/login',
        '/api/user/auth',
        '/api/user/login',
        '/api/activate',
        '/activate',
        '/api/token',
        '/token',
        
        // 根路径和常见路径
        '/',
        '/api',
        '/api/v1',
        '/v1',
        '/health',
        '/status',
        '/ping',
        
        // 用户相关
        '/api/users',
        '/api/user',
        '/users',
        '/user',
        '/api/me',
        '/me',
        
        // 账号相关
        '/api/accounts',
        '/api/account',
        '/accounts',
        '/account',
        
        // 其他可能的路径
        '/api/session',
        '/session',
        '/api/oauth',
        '/oauth',
        '/api/verify',
        '/verify'
    ];
    
    const machineId = generateMachineId();
    const timestamp = Date.now();
    
    // 构造请求体
    const requestBody = {
        activationCode: CONFIG.activationCode,
        clientVersion: "1.1.1",
        platform: "vscode",
        machineId: machineId,
        timestamp: timestamp
    };
    
    const payload = JSON.stringify(requestBody);
    
    const results = [];
    
    for (const endpoint of endpoints) {
        try {
            console.log(`🔍 探测: ${endpoint}`);
            
            const url = new URL(endpoint, CONFIG.baseUrl);
            
            // 先尝试GET请求
            const getOptions = {
                hostname: url.hostname,
                port: url.port || 443,
                path: url.pathname,
                method: 'GET',
                headers: {
                    'User-Agent': 'SmartShift-VSCode/1.1.1',
                    'Accept': 'application/json'
                }
            };
            
            const getResponse = await makeRequest(getOptions);
            
            if (getResponse.statusCode !== 404) {
                console.log(`✅ GET ${endpoint} - 状态码: ${getResponse.statusCode}`);
                results.push({
                    endpoint: endpoint,
                    method: 'GET',
                    statusCode: getResponse.statusCode,
                    response: getResponse
                });
                
                // 如果GET成功，也尝试POST
                if (getResponse.statusCode === 200 || getResponse.statusCode === 405) {
                    const postOptions = {
                        hostname: url.hostname,
                        port: url.port || 443,
                        path: url.pathname,
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Content-Length': Buffer.byteLength(payload),
                            'User-Agent': 'SmartShift-VSCode/1.1.1',
                            'Accept': 'application/json'
                        }
                    };
                    
                    const postResponse = await makeRequest(postOptions, payload);
                    console.log(`✅ POST ${endpoint} - 状态码: ${postResponse.statusCode}`);
                    
                    results.push({
                        endpoint: endpoint,
                        method: 'POST',
                        statusCode: postResponse.statusCode,
                        response: postResponse
                    });
                }
            }
            
            // 小延迟避免过快请求
            await new Promise(resolve => setTimeout(resolve, 100));
            
        } catch (error) {
            console.log(`❌ ${endpoint} - 错误: ${error.message}`);
        }
    }
    
    console.log('\n📊 探测结果汇总:');
    console.log('═'.repeat(60));
    
    if (results.length === 0) {
        console.log('❌ 没有发现任何有效端点');
    } else {
        console.log(`✅ 发现 ${results.length} 个有效端点:`);
        
        results.forEach(result => {
            console.log(`${result.method} ${result.endpoint} - 状态码: ${result.statusCode}`);
            
            if (result.response.body && result.response.body.length < 500) {
                console.log(`   响应: ${result.response.body}`);
            }
            
            if (result.response.data) {
                console.log(`   JSON数据: ${JSON.stringify(result.response.data)}`);
            }
            console.log('');
        });
    }
    
    // 保存探测结果
    const resultFile = `端点探测结果_${Date.now()}.json`;
    fs.writeFileSync(resultFile, JSON.stringify({
        baseUrl: CONFIG.baseUrl,
        activationCode: CONFIG.activationCode,
        totalEndpoints: endpoints.length,
        validEndpoints: results.length,
        results: results,
        timestamp: new Date().toISOString()
    }, null, 2));
    
    console.log(`💾 完整探测结果已保存到: ${resultFile}`);
    
    // 分析最有希望的端点
    const successfulEndpoints = results.filter(r => r.statusCode === 200);
    const authEndpoints = results.filter(r => 
        r.endpoint.includes('auth') || 
        r.endpoint.includes('login') || 
        r.endpoint.includes('activate')
    );
    
    if (successfulEndpoints.length > 0) {
        console.log('\n🎯 状态码200的端点:');
        successfulEndpoints.forEach(r => {
            console.log(`${r.method} ${r.endpoint}`);
        });
    }
    
    if (authEndpoints.length > 0) {
        console.log('\n🔑 认证相关的端点:');
        authEndpoints.forEach(r => {
            console.log(`${r.method} ${r.endpoint} - 状态码: ${r.statusCode}`);
        });
    }
    
    return results;
}

// 主函数
async function main() {
    const results = await probeEndpoints();
    
    console.log('\n🎉 探测完成!');
    console.log('现在您可以查看探测结果，找到正确的API端点进行登录。');
}

// 运行
main().catch(console.error);

/**
 * 使用方法:
 * node 探测正确端点.js
 * 
 * 功能:
 * 1. 探测所有可能的API端点
 * 2. 尝试GET和POST请求
 * 3. 记录所有非404的响应
 * 4. 保存完整的探测结果
 * 5. 分析最有希望的认证端点
 */
