{"timestamp": "2025-08-05T10:49:49.213Z", "project": {"name": "AI智切 VSCode扩展", "version": "1.1.1", "buildEnvironment": "完整编译运行环境"}, "environment": {"success": true, "environment": {"Node.js": "v24.3.0", "npm": "11.4.2", "VSCode CLI": "1.102.2"}}, "validation": {"files": {"extension.vsixmanifest": {"exists": true, "size": 2451, "modified": "2025-08-04T07:54:54.000Z"}, "extension/package.json": {"exists": true, "size": 1547, "modified": "2025-08-04T05:19:42.000Z"}, "extension/dist/extension.js": {"exists": true, "size": 214543, "modified": "2025-08-04T07:54:52.000Z"}, "extension/LICENSE.md": {"exists": true, "size": 1090, "modified": "2025-06-25T05:38:04.000Z"}, "extension/README.md": {"exists": true, "size": 1914, "modified": "2025-08-04T07:54:54.000Z"}}, "configuration": {"packageJson": {"valid": true, "missingFields": [], "dependencies": ["axios", "fernet", "uri-js"], "commands": 2}}, "code": {"mainFile": {"size": 213255, "lines": 1, "functions": 320, "obfuscated": true}}}, "build": {"outputPath": "C:\\Users\\<USER>\\Desktop\\smartshift-manager-1.1.1\\build", "demoPath": "C:\\Users\\<USER>\\Desktop\\smartshift-manager-1.1.1\\demo", "logFile": "C:\\Users\\<USER>\\Desktop\\smartshift-manager-1.1.1\\build.log"}, "demo": {"configPath": "C:\\Users\\<USER>\\Desktop\\smartshift-manager-1.1.1\\demo\\demo-config.json", "htmlPath": "C:\\Users\\<USER>\\Desktop\\smartshift-manager-1.1.1\\demo\\demo.html"}, "security": {"sandboxEnabled": true, "testingCompleted": true, "vulnerabilitiesFound": 24, "riskLevel": "HIGH"}, "recommendations": ["在沙箱环境中测试扩展功能", "定期运行安全测试脚本", "监控网络通信和文件访问", "使用演示版本进行功能展示", "保持开发环境配置更新"]}