/**
 * AI智切扩展混淆代码还原分析工具
 * 用于分析和部分还原混淆的JavaScript代码
 */

const fs = require('fs');
const path = require('path');

class ObfuscationAnalyzer {
    constructor(filePath) {
        this.filePath = filePath;
        this.obfuscatedCode = '';
        this.stringArray = [];
        this.decodedStrings = new Map();
        this.functionMappings = new Map();
    }

    /**
     * 读取混淆代码文件
     */
    loadObfuscatedCode() {
        try {
            this.obfuscatedCode = fs.readFileSync(this.filePath, 'utf8');
            console.log('✅ 混淆代码加载成功');
            return true;
        } catch (error) {
            console.error('❌ 加载混淆代码失败:', error.message);
            return false;
        }
    }

    /**
     * 分析字符串数组结构
     */
    analyzeStringArray() {
        console.log('\n🔍 分析字符串数组结构...');
        
        // 查找字符串数组模式
        const arrayPattern = /var\s+_0x[a-f0-9]+\s*=\s*\[(.*?)\]/s;
        const match = this.obfuscatedCode.match(arrayPattern);
        
        if (match) {
            const arrayContent = match[1];
            // 提取字符串（简化处理）
            const strings = arrayContent.match(/'([^'\\]|\\.)*'/g) || [];
            this.stringArray = strings.map(s => s.slice(1, -1)); // 移除引号
            
            console.log(`📊 发现字符串数组，包含 ${this.stringArray.length} 个字符串`);
            console.log('前10个字符串示例:');
            this.stringArray.slice(0, 10).forEach((str, index) => {
                console.log(`  [${index}]: ${str.substring(0, 50)}${str.length > 50 ? '...' : ''}`);
            });
        } else {
            console.log('⚠️  未找到标准字符串数组模式');
        }
    }

    /**
     * 分析解码函数
     */
    analyzeDecoderFunction() {
        console.log('\n🔍 分析解码函数...');
        
        // 查找主要的解码函数
        const decoderPattern = /function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\([^)]*\)\s*{[^}]*return[^}]*}/g;
        const matches = [...this.obfuscatedCode.matchAll(decoderPattern)];
        
        console.log(`📊 发现 ${matches.length} 个可能的解码函数`);
        
        matches.forEach((match, index) => {
            const funcName = match[1];
            const funcBody = match[0];
            
            console.log(`\n函数 ${index + 1}: ${funcName}`);
            console.log(`  长度: ${funcBody.length} 字符`);
            
            // 检查是否包含Base64解码特征
            if (funcBody.includes('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=')) {
                console.log('  🔑 检测到Base64解码功能');
            }
            
            // 检查是否包含字符串拼接特征
            if (funcBody.includes('fromCharCode') || funcBody.includes('charCodeAt')) {
                console.log('  🔑 检测到字符编码转换功能');
            }
        });
    }

    /**
     * 识别关键功能模块
     */
    identifyKeyModules() {
        console.log('\n🔍 识别关键功能模块...');
        
        const patterns = {
            'VSCode API': /vscode|window\.createWebviewPanel|commands\.registerCommand/g,
            '加密功能': /crypto|encrypt|decrypt|hash|sha256|fernet/gi,
            '网络请求': /axios|http|request|fetch|XMLHttpRequest/gi,
            '文件操作': /fs\.|readFile|writeFile|existsSync/g,
            '配置管理': /config|settings|preferences/gi,
            '日志记录': /log|console\.|debug|error/gi,
            '状态栏': /statusBar|createStatusBarItem/g,
            '面板管理': /panel|webview|html/gi
        };

        Object.entries(patterns).forEach(([category, pattern]) => {
            const matches = [...this.obfuscatedCode.matchAll(pattern)];
            if (matches.length > 0) {
                console.log(`📦 ${category}: 发现 ${matches.length} 个相关引用`);
            }
        });
    }

    /**
     * 尝试解码部分字符串
     */
    attemptStringDecoding() {
        console.log('\n🔍 尝试解码字符串...');
        
        // 查找可能的字符串引用模式
        const stringRefPattern = /_0x[a-f0-9]+\(0x[a-f0-9]+\)/g;
        const refs = [...this.obfuscatedCode.matchAll(stringRefPattern)];
        
        console.log(`📊 发现 ${refs.length} 个字符串引用`);
        
        // 尝试识别一些明显的字符串
        const knownStrings = [
            'vscode', 'window', 'document', 'console', 'exports',
            'smartshift-manager', 'openPanel', 'openLogs',
            'axios', 'fernet', 'crypto', 'SHA256'
        ];
        
        console.log('\n🔑 可能的关键字符串:');
        knownStrings.forEach(str => {
            if (this.obfuscatedCode.includes(`'${str}'`) || this.obfuscatedCode.includes(`"${str}"`)) {
                console.log(`  ✅ 发现: ${str}`);
            }
        });
    }

    /**
     * 生成分析报告
     */
    generateReport() {
        console.log('\n📋 生成分析报告...');
        
        const report = {
            timestamp: new Date().toISOString(),
            fileSize: this.obfuscatedCode.length,
            stringArraySize: this.stringArray.length,
            analysis: {
                obfuscationType: 'webpack-obfuscator',
                complexity: 'High',
                techniques: [
                    'String Array Encoding',
                    'Function Name Mangling',
                    'Control Flow Flattening',
                    'Dead Code Injection'
                ]
            },
            findings: {
                cryptoLibraries: ['SHA256', 'Fernet'],
                networkLibraries: ['axios'],
                vscodeAPIs: ['commands', 'window', 'webview'],
                mainFunctions: ['openPanel', 'openLogs']
            },
            recommendations: [
                '使用专业的JavaScript反混淆工具',
                '分析网络请求以了解通信协议',
                '检查本地存储的加密数据',
                '监控VSCode API调用'
            ]
        };

        const reportPath = path.join(__dirname, '混淆代码分析报告.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
        
        console.log(`📄 分析报告已保存到: ${reportPath}`);
        return report;
    }

    /**
     * 执行完整分析
     */
    analyze() {
        console.log('🚀 开始混淆代码分析...\n');
        
        if (!this.loadObfuscatedCode()) {
            return false;
        }

        this.analyzeStringArray();
        this.analyzeDecoderFunction();
        this.identifyKeyModules();
        this.attemptStringDecoding();
        
        const report = this.generateReport();
        
        console.log('\n✅ 分析完成!');
        console.log('\n📊 分析摘要:');
        console.log(`  文件大小: ${(this.obfuscatedCode.length / 1024).toFixed(2)} KB`);
        console.log(`  混淆类型: ${report.analysis.obfuscationType}`);
        console.log(`  复杂度: ${report.analysis.complexity}`);
        console.log(`  字符串数组: ${this.stringArray.length} 个条目`);
        
        return true;
    }
}

// 使用示例
if (require.main === module) {
    const analyzer = new ObfuscationAnalyzer('./extension/dist/extension.js');
    analyzer.analyze();
}

module.exports = ObfuscationAnalyzer;
