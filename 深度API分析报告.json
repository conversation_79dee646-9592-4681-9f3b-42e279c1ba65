{"timestamp": "2025-08-05T11:02:16.293Z", "summary": {"totalStrings": 738, "decodedStrings": 0, "totalAPIs": 11}, "decodedStrings": [{"index": 0, "encoded": "C2HVD1rLEhreB2n1BwvUDa", "decoded": "C2HVD1rLEhreB2n1BwvUDa"}, {"index": 1, "encoded": "y2LWAgvYDgv4Da", "decoded": "y2LWAgvYDgv4Da"}, {"index": 2, "encoded": "w29IAMvJDcbhzw5LCMf0B3jD", "decoded": "w29IAMvJDcbhzw5LCMf0B3jD"}, {"index": 3, "encoded": "x2zPBMrbBMrqCM9JzxnZu3rHDgveyKzPBgvZ", "decoded": "x2zPBMrbBMrqCM9JzxnZu3rHDgveyKzPBgvZ"}, {"index": 4, "encoded": "z2XVyMfSu3rVCMfNzq", "decoded": "z2XVyMfSu3rVCMfNzq"}, {"index": 5, "encoded": "x2LUDM9Rzq", "decoded": "x2LUDM9Rzq"}, {"index": 6, "encoded": "5l+U5Ps55P2d6zMq5AsX6lsLoIa", "decoded": "5l+U5Ps55P2d6zMq5AsX6lsLoIa"}, {"index": 7, "encoded": "DvfguK0", "decoded": "DvfguK0"}, {"index": 8, "encoded": "DuLrBvu", "decoded": "DuLrBvu"}, {"index": 9, "encoded": "DgLTzq", "decoded": "DgLTzq"}, {"index": 10, "encoded": "ANLYuKO", "decoded": "ANLYuKO"}, {"index": 11, "encoded": "y29TChv0zq", "decoded": "y29TChv0zq"}, {"index": 12, "encoded": "uhPQCfO", "decoded": "uhPQCfO"}, {"index": 13, "encoded": "uKHpwgS", "decoded": "uKHpwgS"}, {"index": 14, "encoded": "zxHPC3rZu3LUyW", "decoded": "zxHPC3rZu3LUyW"}, {"index": 15, "encoded": "yMLUza", "decoded": "yMLUza"}, {"index": 16, "encoded": "CgvYzM9YBvvWBg9HzcGKmsL7CMv0DxjUifbYB21PC2uUCMvZB2X2zsGPoW", "decoded": "CgvYzM9YBvvWBg9HzcGKmsL7CMv0DxjUifbYB21PC2uUCMvZB2X2zsGPoW"}, {"index": 17, "encoded": "r3P5vMW", "decoded": "r3P5vMW"}, {"index": 18, "encoded": "lL9HDxrOu2vZC2LVBI5ZyxzLu2vZC2LVBI5HChbSEsG", "decoded": "lL9HDxrOu2vZC2LVBI5ZyxzLu2vZC2LVBI5HChbSEsG"}, {"index": 19, "encoded": "ugf0AcbKB2vZig5VDcbLEgLZDdOG", "decoded": "ugf0AcbKB2vZig5VDcbLEgLZDdOG"}, {"index": 20, "encoded": "zgvJCNLWDe1LC3nHz2u", "decoded": "zgvJCNLWDe1LC3nHz2u"}, {"index": 21, "encoded": "5PEL5B+x5PAh5lU25lIn5A2y5zYO", "decoded": "5PEL5B+x5PAh5lU25lIn5A2y5zYO"}, {"index": 22, "encoded": "AgfZt3DUuhjVCgvYDhK", "decoded": "AgfZt3DUuhjVCgvYDhK"}, {"index": 23, "encoded": "6ycc6ywn5A6m5OIq77Yb", "decoded": "6ycc6ywn5A6m5OIq77Yb"}, {"index": 24, "encoded": "C3vIC2nYAxb0Aw9UCW", "decoded": "C3vIC2nYAxb0Aw9UCW"}, {"index": 25, "encoded": "Bg9NBY5WBMC", "decoded": "Bg9NBY5WBMC"}, {"index": 26, "encoded": "Dg9tDhjPBMDuywC", "decoded": "Dg9tDhjPBMDuywC"}, {"index": 27, "encoded": "BgvUz3rO", "decoded": "BgvUz3rO"}, {"index": 28, "encoded": "uxrpCfa", "decoded": "uxrpCfa"}, {"index": 29, "encoded": "yxvNBwvUDe1HBMfNzxjby3rPDMf0Aw9Uq29Kzq", "decoded": "yxvNBwvUDe1HBMfNzxjby3rPDMf0Aw9Uq29Kzq"}, {"index": 30, "encoded": "x3vWzgf0zvn0B3jHz2vkC29U", "decoded": "x3vWzgf0zvn0B3jHz2vkC29U"}, {"index": 31, "encoded": "6i635y+w5A2y5ykO5l2n572U5AsX6lsLoIa", "decoded": "6i635y+w5A2y5ykO5l2n572U5AsX6lsLoIa"}, {"index": 32, "encoded": "x2rVq3j5ChrcBg9JAW", "decoded": "x2rVq3j5ChrcBg9JAW"}, {"index": 33, "encoded": "Dg9mB3DLCKnHC2u", "decoded": "Dg9mB3DLCKnHC2u"}, {"index": 34, "encoded": "5P2d6zMq5lIn6lAZ77Ym6k+35lUL566H55cg5zgy6lQR5lU96l+q6kgmvLndB2rL", "decoded": "5P2d6zMq5lIn6lAZ77Ym6k+35lUL566H55cg5zgy6lQR5lU96l+q6kgmvLndB2rL"}, {"index": 35, "encoded": "5B2t5yMn6lsM5y+3oIa", "decoded": "5B2t5yMn6lsM5y+3oIa"}, {"index": 36, "encoded": "yKzlv1y", "decoded": "yKzlv1y"}, {"index": 37, "encoded": "y2HTB2rtEw5J", "decoded": "y2HTB2rtEw5J"}, {"index": 38, "encoded": "mZu1ndm0m2zJz0XhrG", "decoded": "mZu1ndm0m2zJz0XhrG"}, {"index": 39, "encoded": "C2v0svy", "decoded": "C2v0svy"}, {"index": 40, "encoded": "zgvJB2rLnJr0B0HLEa", "decoded": "zgvJB2rLnJr0B0HLEa"}, {"index": 41, "encoded": "z2v0uhjVDg90ExbLt2y", "decoded": "z2v0uhjVDg90ExbLt2y"}, {"index": 42, "encoded": "z2v0rxH0zw5ZAw9U", "decoded": "z2v0rxH0zw5ZAw9U"}, {"index": 43, "encoded": "u2vJCMv0ig5VDcbZzxq", "decoded": "u2vJCMv0ig5VDcbZzxq"}, {"index": 44, "encoded": "vxrMoa", "decoded": "vxrMoa"}, {"index": 45, "encoded": "zgLZCgXHEu5HBwu", "decoded": "zgLZCgXHEu5HBwu"}, {"index": 46, "encoded": "ios4QUMHUEEBRIWG5AsX6lsLia", "decoded": "ios4QUMHUEEBRIWG5AsX6lsLia"}, {"index": 47, "encoded": "C3LTyM9S", "decoded": "C3LTyM9S"}, {"index": 48, "encoded": "B21rr08", "decoded": "B21rr08"}, {"index": 49, "encoded": "zw1HAwW", "decoded": "zw1HAwW"}], "apis": {"vscodeCommands": [], "webviewMessages": [], "httpEndpoints": [], "fileOperations": ["路径: /", "路径: /", "路径: /", "路径: /get_session", "路径: /", "路径: /", "路径: /"], "cryptoOperations": [], "systemCalls": [], "configKeys": ["extension.js", "telemetry.machineId", "state.vscdb"], "errorMessages": ["Malformed\\x20UTF-8\\x20data"], "uiElements": []}, "analysis": {"coreFeatures": {"hasWebView": false, "hasNetworking": false, "hasFileAccess": true, "hasCrypto": false, "hasCommands": false}, "securityConcerns": [], "functionalityMap": {"authentication": false, "accountManagement": false, "pluginIntegration": false, "userInterface": false, "dataStorage": false}, "recommendations": ["对所有网络通信实施加密和验证", "避免在代码中硬编码敏感信息", "实施适当的错误处理和日志记录", "定期审查和更新安全措施", "使用安全的数据存储方法"]}}