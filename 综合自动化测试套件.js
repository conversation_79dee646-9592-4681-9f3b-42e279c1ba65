/**
 * AI智切扩展综合自动化测试套件
 * 包含网络通信、加密解密、用户交互模拟和安全性渗透测试
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const axios = require('axios');
const { spawn } = require('child_process');

class ComprehensiveTestSuite {
    constructor() {
        this.testResults = [];
        this.config = {
            timeout: 30000,
            retries: 3,
            logLevel: 'info'
        };
        this.mockServer = null;
        this.testData = {
            validActivationCode: 'TEST-CODE-12345',
            invalidActivationCode: 'INVALID',
            testAccounts: [
                { id: 'acc1', username: '<EMAIL>', token: 'token1' },
                { id: 'acc2', username: '<EMAIL>', token: 'token2' }
            ]
        };
    }

    /**
     * 记录测试结果
     */
    logTest(category, testName, status, message, details = null) {
        const result = {
            category,
            testName,
            status, // 'PASS', 'FAIL', 'SKIP', 'WARN'
            message,
            details,
            timestamp: new Date().toISOString(),
            duration: 0
        };
        
        this.testResults.push(result);
        
        const statusIcon = {
            'PASS': '✅',
            'FAIL': '❌',
            'SKIP': '⏭️',
            'WARN': '⚠️'
        };
        
        console.log(`${statusIcon[status]} [${category}] ${testName}: ${message}`);
        if (details && this.config.logLevel === 'debug') {
            console.log(`   详情: ${JSON.stringify(details, null, 2)}`);
        }
    }

    /**
     * 启动模拟服务器
     */
    async startMockServer() {
        console.log('\n🚀 启动模拟服务器...');
        
        try {
            const express = require('express');
            const app = express();
            app.use(express.json());
            
            // 模拟认证接口
            app.post('/api/auth', (req, res) => {
                const { activationCode } = req.body;
                
                if (activationCode === this.testData.validActivationCode) {
                    res.json({
                        success: true,
                        accounts: this.testData.testAccounts,
                        token: 'mock-jwt-token'
                    });
                } else {
                    res.status(401).json({
                        success: false,
                        error: 'Invalid activation code'
                    });
                }
            });
            
            // 模拟账号列表接口
            app.get('/api/accounts', (req, res) => {
                const authHeader = req.headers.authorization;
                if (authHeader && authHeader.startsWith('Bearer ')) {
                    res.json({
                        accounts: this.testData.testAccounts
                    });
                } else {
                    res.status(401).json({ error: 'Unauthorized' });
                }
            });
            
            // 模拟账号切换接口
            app.post('/api/accounts/switch', (req, res) => {
                const { accountId } = req.body;
                const account = this.testData.testAccounts.find(acc => acc.id === accountId);
                
                if (account) {
                    res.json({
                        success: true,
                        account: account
                    });
                } else {
                    res.status(404).json({
                        success: false,
                        error: 'Account not found'
                    });
                }
            });
            
            this.mockServer = app.listen(3000, () => {
                console.log('📡 模拟服务器启动在端口 3000');
            });
            
            // 等待服务器启动
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            return true;
        } catch (error) {
            console.error('❌ 模拟服务器启动失败:', error.message);
            return false;
        }
    }

    /**
     * 停止模拟服务器
     */
    stopMockServer() {
        if (this.mockServer) {
            this.mockServer.close();
            console.log('🛑 模拟服务器已停止');
        }
    }

    /**
     * 网络通信测试
     */
    async testNetworkCommunication() {
        console.log('\n🌐 开始网络通信测试...');
        
        const baseURL = 'http://localhost:3000';
        
        // 测试1: 有效认证请求
        try {
            const response = await axios.post(`${baseURL}/api/auth`, {
                activationCode: this.testData.validActivationCode
            }, { timeout: 5000 });
            
            if (response.status === 200 && response.data.success) {
                this.logTest('Network', '有效认证请求', 'PASS', '认证成功', {
                    status: response.status,
                    accounts: response.data.accounts.length
                });
            } else {
                this.logTest('Network', '有效认证请求', 'FAIL', '认证响应异常');
            }
        } catch (error) {
            this.logTest('Network', '有效认证请求', 'FAIL', `请求失败: ${error.message}`);
        }
        
        // 测试2: 无效认证请求
        try {
            const response = await axios.post(`${baseURL}/api/auth`, {
                activationCode: this.testData.invalidActivationCode
            }, { timeout: 5000 });
            
            this.logTest('Network', '无效认证请求', 'FAIL', '应该返回401错误');
        } catch (error) {
            if (error.response && error.response.status === 401) {
                this.logTest('Network', '无效认证请求', 'PASS', '正确返回401错误');
            } else {
                this.logTest('Network', '无效认证请求', 'FAIL', `意外错误: ${error.message}`);
            }
        }
        
        // 测试3: 账号列表请求
        try {
            const response = await axios.get(`${baseURL}/api/accounts`, {
                headers: { 'Authorization': 'Bearer mock-jwt-token' },
                timeout: 5000
            });
            
            if (response.status === 200 && response.data.accounts) {
                this.logTest('Network', '账号列表请求', 'PASS', '获取账号列表成功', {
                    accountCount: response.data.accounts.length
                });
            } else {
                this.logTest('Network', '账号列表请求', 'FAIL', '响应格式异常');
            }
        } catch (error) {
            this.logTest('Network', '账号列表请求', 'FAIL', `请求失败: ${error.message}`);
        }
        
        // 测试4: 账号切换请求
        try {
            const response = await axios.post(`${baseURL}/api/accounts/switch`, {
                accountId: 'acc1'
            }, {
                headers: { 'Authorization': 'Bearer mock-jwt-token' },
                timeout: 5000
            });
            
            if (response.status === 200 && response.data.success) {
                this.logTest('Network', '账号切换请求', 'PASS', '账号切换成功');
            } else {
                this.logTest('Network', '账号切换请求', 'FAIL', '切换响应异常');
            }
        } catch (error) {
            this.logTest('Network', '账号切换请求', 'FAIL', `请求失败: ${error.message}`);
        }
        
        // 测试5: 网络超时测试
        try {
            await axios.get(`${baseURL}/api/timeout-test`, { timeout: 1000 });
            this.logTest('Network', '网络超时测试', 'FAIL', '应该超时');
        } catch (error) {
            if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
                this.logTest('Network', '网络超时测试', 'PASS', '正确处理超时');
            } else {
                this.logTest('Network', '网络超时测试', 'WARN', `其他错误: ${error.message}`);
            }
        }
    }

    /**
     * 加密解密测试
     */
    async testEncryptionDecryption() {
        console.log('\n🔐 开始加密解密测试...');
        
        // 测试1: 基本加密解密
        try {
            const testData = {
                username: '<EMAIL>',
                token: 'secret-token-123',
                timestamp: Date.now()
            };
            
            const key = crypto.randomBytes(32);
            
            // 使用Node.js内置crypto进行测试
            const cipher = crypto.createCipher('aes256', key);
            let encrypted = cipher.update(JSON.stringify(testData), 'utf8', 'hex');
            encrypted += cipher.final('hex');
            
            const decipher = crypto.createDecipher('aes256', key);
            let decrypted = decipher.update(encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            
            const decryptedData = JSON.parse(decrypted);
            
            if (JSON.stringify(testData) === JSON.stringify(decryptedData)) {
                this.logTest('Crypto', '基本加密解密', 'PASS', '加密解密成功');
            } else {
                this.logTest('Crypto', '基本加密解密', 'FAIL', '数据不匹配');
            }
        } catch (error) {
            this.logTest('Crypto', '基本加密解密', 'FAIL', `加密失败: ${error.message}`);
        }
        
        // 测试2: 密钥强度测试
        const weakKeys = ['123', 'password', ''];
        const strongKey = crypto.randomBytes(32).toString('hex');
        
        weakKeys.forEach((key, index) => {
            if (key.length < 8) {
                this.logTest('Crypto', `弱密钥测试${index + 1}`, 'PASS', '正确识别弱密钥');
            } else {
                this.logTest('Crypto', `弱密钥测试${index + 1}`, 'WARN', '密钥强度不足');
            }
        });
        
        if (strongKey.length >= 32) {
            this.logTest('Crypto', '强密钥测试', 'PASS', '密钥强度足够');
        }
        
        // 测试3: 数据完整性测试
        try {
            const originalData = 'sensitive-information';
            const hash1 = crypto.createHash('sha256').update(originalData).digest('hex');
            const hash2 = crypto.createHash('sha256').update(originalData).digest('hex');
            const hash3 = crypto.createHash('sha256').update(originalData + 'x').digest('hex');
            
            if (hash1 === hash2 && hash1 !== hash3) {
                this.logTest('Crypto', '数据完整性测试', 'PASS', 'SHA256哈希正常');
            } else {
                this.logTest('Crypto', '数据完整性测试', 'FAIL', '哈希计算异常');
            }
        } catch (error) {
            this.logTest('Crypto', '数据完整性测试', 'FAIL', `哈希失败: ${error.message}`);
        }
        
        // 测试4: 加密性能测试
        const startTime = Date.now();
        const largeData = 'x'.repeat(10000); // 10KB数据
        
        try {
            for (let i = 0; i < 100; i++) {
                const cipher = crypto.createCipher('aes256', 'test-key');
                let encrypted = cipher.update(largeData, 'utf8', 'hex');
                encrypted += cipher.final('hex');
            }
            
            const duration = Date.now() - startTime;
            
            if (duration < 1000) {
                this.logTest('Crypto', '加密性能测试', 'PASS', `100次加密耗时${duration}ms`);
            } else {
                this.logTest('Crypto', '加密性能测试', 'WARN', `性能较慢: ${duration}ms`);
            }
        } catch (error) {
            this.logTest('Crypto', '加密性能测试', 'FAIL', `性能测试失败: ${error.message}`);
        }
    }

    /**
     * 用户交互模拟测试
     */
    async testUserInteractionSimulation() {
        console.log('\n👤 开始用户交互模拟测试...');
        
        // 模拟VSCode命令执行
        const mockVSCodeCommands = {
            'smartshift-manager.openPanel': () => {
                return { success: true, panel: 'created' };
            },
            'smartshift-manager.openLogs': () => {
                return { success: true, logs: 'displayed' };
            }
        };
        
        // 测试1: 命令注册测试
        Object.keys(mockVSCodeCommands).forEach(command => {
            try {
                const result = mockVSCodeCommands[command]();
                if (result.success) {
                    this.logTest('UI', `命令测试: ${command}`, 'PASS', '命令执行成功');
                } else {
                    this.logTest('UI', `命令测试: ${command}`, 'FAIL', '命令执行失败');
                }
            } catch (error) {
                this.logTest('UI', `命令测试: ${command}`, 'FAIL', `命令错误: ${error.message}`);
            }
        });
        
        // 测试2: WebView消息处理测试
        const mockWebViewMessages = [
            { type: 'authenticate', data: { activationCode: 'TEST-CODE' } },
            { type: 'switchAccount', data: { accountId: 'acc1' } },
            { type: 'getLogs', data: {} },
            { type: 'invalid', data: {} }
        ];
        
        mockWebViewMessages.forEach(message => {
            try {
                const isValid = ['authenticate', 'switchAccount', 'getLogs'].includes(message.type);
                
                if (isValid) {
                    this.logTest('UI', `WebView消息: ${message.type}`, 'PASS', '消息格式有效');
                } else {
                    this.logTest('UI', `WebView消息: ${message.type}`, 'WARN', '未知消息类型');
                }
            } catch (error) {
                this.logTest('UI', `WebView消息: ${message.type}`, 'FAIL', `消息处理错误: ${error.message}`);
            }
        });
        
        // 测试3: 状态栏交互测试
        try {
            const mockStatusBar = {
                text: "$(sync) AI智切",
                tooltip: "点击打开AI智切面板",
                command: 'smartshift-manager.openPanel',
                show: () => true,
                hide: () => true
            };
            
            if (mockStatusBar.text && mockStatusBar.command) {
                this.logTest('UI', '状态栏交互测试', 'PASS', '状态栏配置正确');
            } else {
                this.logTest('UI', '状态栏交互测试', 'FAIL', '状态栏配置缺失');
            }
        } catch (error) {
            this.logTest('UI', '状态栏交互测试', 'FAIL', `状态栏错误: ${error.message}`);
        }
        
        // 测试4: 用户输入验证测试
        const testInputs = [
            { input: 'VALID-CODE-123', expected: 'valid' },
            { input: '', expected: 'invalid' },
            { input: 'x'.repeat(100), expected: 'invalid' },
            { input: 'CODE<script>', expected: 'invalid' }
        ];
        
        testInputs.forEach(({ input, expected }) => {
            try {
                const isValid = input.length >= 8 && input.length <= 64 && /^[a-zA-Z0-9\-_]+$/.test(input);
                const result = isValid ? 'valid' : 'invalid';
                
                if (result === expected) {
                    this.logTest('UI', `输入验证: ${input.substring(0, 10)}...`, 'PASS', '验证结果正确');
                } else {
                    this.logTest('UI', `输入验证: ${input.substring(0, 10)}...`, 'FAIL', '验证结果错误');
                }
            } catch (error) {
                this.logTest('UI', `输入验证: ${input.substring(0, 10)}...`, 'FAIL', `验证错误: ${error.message}`);
            }
        });
    }

    /**
     * 生成测试报告
     */
    generateTestReport() {
        console.log('\n📋 生成测试报告...');
        
        const summary = {
            totalTests: this.testResults.length,
            passed: this.testResults.filter(r => r.status === 'PASS').length,
            failed: this.testResults.filter(r => r.status === 'FAIL').length,
            warnings: this.testResults.filter(r => r.status === 'WARN').length,
            skipped: this.testResults.filter(r => r.status === 'SKIP').length
        };
        
        const report = {
            timestamp: new Date().toISOString(),
            summary: summary,
            categories: this.groupResultsByCategory(),
            results: this.testResults,
            recommendations: this.generateRecommendations()
        };
        
        const reportPath = path.join(__dirname, '综合测试报告.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
        
        console.log(`📄 测试报告已保存到: ${reportPath}`);
        
        // 打印摘要
        console.log('\n📊 测试摘要:');
        console.log(`  总测试数: ${summary.totalTests}`);
        console.log(`  通过: ${summary.passed} (${(summary.passed/summary.totalTests*100).toFixed(1)}%)`);
        console.log(`  失败: ${summary.failed} (${(summary.failed/summary.totalTests*100).toFixed(1)}%)`);
        console.log(`  警告: ${summary.warnings} (${(summary.warnings/summary.totalTests*100).toFixed(1)}%)`);
        console.log(`  跳过: ${summary.skipped} (${(summary.skipped/summary.totalTests*100).toFixed(1)}%)`);
        
        return report;
    }

    /**
     * 按类别分组测试结果
     */
    groupResultsByCategory() {
        const categories = {};
        
        this.testResults.forEach(result => {
            if (!categories[result.category]) {
                categories[result.category] = {
                    total: 0,
                    passed: 0,
                    failed: 0,
                    warnings: 0,
                    skipped: 0
                };
            }
            
            categories[result.category].total++;
            categories[result.category][result.status.toLowerCase()]++;
        });
        
        return categories;
    }

    /**
     * 生成改进建议
     */
    generateRecommendations() {
        const failedTests = this.testResults.filter(r => r.status === 'FAIL');
        const recommendations = [];
        
        if (failedTests.some(t => t.category === 'Network')) {
            recommendations.push('检查网络连接和API端点配置');
        }
        
        if (failedTests.some(t => t.category === 'Crypto')) {
            recommendations.push('验证加密库版本和密钥管理机制');
        }
        
        if (failedTests.some(t => t.category === 'UI')) {
            recommendations.push('检查VSCode API兼容性和用户界面逻辑');
        }
        
        if (failedTests.length > this.testResults.length * 0.2) {
            recommendations.push('考虑进行全面的代码审查和重构');
        }
        
        return recommendations;
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('🚀 开始综合自动化测试...\n');
        
        const startTime = Date.now();
        
        try {
            // 启动模拟服务器
            const serverStarted = await this.startMockServer();
            if (!serverStarted) {
                console.log('⚠️ 模拟服务器启动失败，跳过网络测试');
            }
            
            // 运行各类测试
            if (serverStarted) {
                await this.testNetworkCommunication();
            }
            await this.testEncryptionDecryption();
            await this.testUserInteractionSimulation();
            
            // 生成报告
            const report = this.generateTestReport();
            
            const duration = Date.now() - startTime;
            console.log(`\n✅ 所有测试完成! 总耗时: ${duration}ms`);
            
            return report;
            
        } finally {
            // 清理资源
            this.stopMockServer();
        }
    }
}

// 使用示例
if (require.main === module) {
    const testSuite = new ComprehensiveTestSuite();
    testSuite.runAllTests().catch(console.error);
}

module.exports = ComprehensiveTestSuite;
