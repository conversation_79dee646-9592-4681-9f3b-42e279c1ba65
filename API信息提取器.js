/**
 * AI智切扩展API信息提取器
 * 专门用于从混淆代码中提取所有API调用信息
 */

const fs = require('fs');
const path = require('path');

class APIExtractor {
    constructor() {
        this.apis = {
            vscode: [],
            http: [],
            crypto: [],
            filesystem: [],
            system: [],
            external: []
        };
        this.patterns = {
            // VSCode API模式
            vscode: [
                /vscode\.(\w+)\.(\w+)/g,
                /window\.(\w+)/g,
                /workspace\.(\w+)/g,
                /commands\.(\w+)/g,
                /extensions\.(\w+)/g,
                /env\.(\w+)/g
            ],
            // HTTP API模式
            http: [
                /axios\.(\w+)/g,
                /fetch\(/g,
                /XMLHttpRequest/g,
                /\.get\(/g,
                /\.post\(/g,
                /\.put\(/g,
                /\.delete\(/g,
                /https?:\/\/[^\s'"]+/g
            ],
            // 加密API模式
            crypto: [
                /crypto\.(\w+)/g,
                /CryptoJS\.(\w+)/g,
                /encrypt/g,
                /decrypt/g,
                /hash/g,
                /SHA256/g,
                /AES/g,
                /fernet/g
            ],
            // 文件系统API模式
            filesystem: [
                /fs\.(\w+)/g,
                /readFile/g,
                /writeFile/g,
                /existsSync/g,
                /mkdirSync/g,
                /path\.(\w+)/g
            ],
            // 系统API模式
            system: [
                /process\.(\w+)/g,
                /os\.(\w+)/g,
                /require\(['"]([^'"]+)['"]\)/g,
                /module\.exports/g
            ]
        };
    }

    /**
     * 从混淆代码中提取API信息
     */
    extractFromObfuscatedCode(filePath) {
        console.log('🔍 开始提取API信息...');
        
        try {
            const code = fs.readFileSync(filePath, 'utf8');
            
            // 1. 基础API模式匹配
            this.extractBasicPatterns(code);
            
            // 2. 字符串解码分析
            this.extractFromStrings(code);
            
            // 3. 函数调用分析
            this.extractFunctionCalls(code);
            
            // 4. URL和端点提取
            this.extractURLsAndEndpoints(code);
            
            // 5. 配置和常量提取
            this.extractConfigAndConstants(code);
            
            console.log('✅ API信息提取完成');
            return this.apis;
            
        } catch (error) {
            console.error('❌ API提取失败:', error.message);
            return null;
        }
    }

    /**
     * 基础模式匹配
     */
    extractBasicPatterns(code) {
        console.log('📋 执行基础模式匹配...');
        
        Object.keys(this.patterns).forEach(category => {
            this.patterns[category].forEach(pattern => {
                const matches = code.match(pattern) || [];
                matches.forEach(match => {
                    if (!this.apis[category].includes(match)) {
                        this.apis[category].push(match);
                    }
                });
            });
        });
    }

    /**
     * 从字符串中提取API信息
     */
    extractFromStrings(code) {
        console.log('🔤 分析字符串内容...');
        
        // 提取所有字符串字面量
        const stringPatterns = [
            /'([^'\\]|\\.)*'/g,
            /"([^"\\]|\\.)*"/g,
            /`([^`\\]|\\.)*`/g
        ];
        
        const allStrings = [];
        stringPatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            allStrings.push(...matches);
        });
        
        // 分析字符串内容
        allStrings.forEach(str => {
            const cleanStr = str.slice(1, -1); // 移除引号
            
            // 检查是否为API相关字符串
            if (this.isAPIString(cleanStr)) {
                this.categorizeAPIString(cleanStr);
            }
        });
    }

    /**
     * 判断是否为API相关字符串
     */
    isAPIString(str) {
        const apiKeywords = [
            'vscode', 'window', 'workspace', 'commands',
            'http', 'https', 'api', 'endpoint',
            'encrypt', 'decrypt', 'crypto', 'hash',
            'file', 'path', 'fs', 'read', 'write',
            'process', 'require', 'module'
        ];
        
        return apiKeywords.some(keyword => 
            str.toLowerCase().includes(keyword)
        );
    }

    /**
     * 分类API字符串
     */
    categorizeAPIString(str) {
        if (str.includes('vscode') || str.includes('window') || str.includes('workspace')) {
            this.apis.vscode.push(str);
        } else if (str.includes('http') || str.includes('api') || str.includes('endpoint')) {
            this.apis.http.push(str);
        } else if (str.includes('encrypt') || str.includes('crypto') || str.includes('hash')) {
            this.apis.crypto.push(str);
        } else if (str.includes('file') || str.includes('path') || str.includes('fs')) {
            this.apis.filesystem.push(str);
        } else if (str.includes('process') || str.includes('require') || str.includes('module')) {
            this.apis.system.push(str);
        } else {
            this.apis.external.push(str);
        }
    }

    /**
     * 提取函数调用
     */
    extractFunctionCalls(code) {
        console.log('🔧 分析函数调用...');
        
        // 常见的API函数调用模式
        const functionPatterns = [
            /(\w+)\.(\w+)\(/g,
            /new\s+(\w+)\(/g,
            /require\(['"]([^'"]+)['"]\)/g,
            /import\s+.*\s+from\s+['"]([^'"]+)['"]/g
        ];
        
        functionPatterns.forEach(pattern => {
            const matches = [...code.matchAll(pattern)];
            matches.forEach(match => {
                const fullMatch = match[0];
                if (this.isAPICall(fullMatch)) {
                    this.categorizeAPICall(fullMatch);
                }
            });
        });
    }

    /**
     * 判断是否为API调用
     */
    isAPICall(call) {
        const apiObjects = [
            'vscode', 'window', 'workspace', 'commands', 'extensions',
            'axios', 'fetch', 'XMLHttpRequest',
            'crypto', 'CryptoJS', 'fernet',
            'fs', 'path', 'process', 'os'
        ];
        
        return apiObjects.some(obj => call.includes(obj));
    }

    /**
     * 分类API调用
     */
    categorizeAPICall(call) {
        if (call.includes('vscode') || call.includes('window') || call.includes('workspace') || call.includes('commands')) {
            this.apis.vscode.push(call);
        } else if (call.includes('axios') || call.includes('fetch') || call.includes('XMLHttpRequest')) {
            this.apis.http.push(call);
        } else if (call.includes('crypto') || call.includes('CryptoJS') || call.includes('fernet')) {
            this.apis.crypto.push(call);
        } else if (call.includes('fs') || call.includes('path')) {
            this.apis.filesystem.push(call);
        } else if (call.includes('process') || call.includes('os') || call.includes('require')) {
            this.apis.system.push(call);
        }
    }

    /**
     * 提取URL和端点
     */
    extractURLsAndEndpoints(code) {
        console.log('🌐 提取URL和API端点...');
        
        const urlPatterns = [
            /https?:\/\/[^\s'"`,)}\]]+/g,
            /\/api\/[^\s'"`,)}\]]+/g,
            /\/v\d+\/[^\s'"`,)}\]]+/g,
            /api\.[^\s'"`,)}\]]+/g
        ];
        
        urlPatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            matches.forEach(url => {
                if (!this.apis.http.includes(url)) {
                    this.apis.http.push(url);
                }
            });
        });
    }

    /**
     * 提取配置和常量
     */
    extractConfigAndConstants(code) {
        console.log('⚙️ 提取配置和常量...');
        
        // 查找配置对象
        const configPatterns = [
            /config\s*[:=]\s*{[^}]+}/g,
            /settings\s*[:=]\s*{[^}]+}/g,
            /options\s*[:=]\s*{[^}]+}/g,
            /const\s+\w+\s*=\s*{[^}]+}/g
        ];
        
        configPatterns.forEach(pattern => {
            const matches = code.match(pattern) || [];
            matches.forEach(config => {
                this.apis.external.push(config);
            });
        });
    }

    /**
     * 生成API报告
     */
    generateAPIReport() {
        console.log('📋 生成API报告...');
        
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalAPIs: Object.values(this.apis).reduce((sum, arr) => sum + arr.length, 0),
                categories: Object.keys(this.apis).map(key => ({
                    category: key,
                    count: this.apis[key].length
                }))
            },
            details: this.apis,
            analysis: this.analyzeAPIs()
        };
        
        const reportPath = path.join(__dirname, 'API提取报告.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log(`📄 API报告已保存到: ${reportPath}`);
        
        // 生成可读性报告
        this.generateReadableReport(report);
        
        return report;
    }

    /**
     * 分析API使用情况
     */
    analyzeAPIs() {
        const analysis = {
            vscodeIntegration: this.apis.vscode.length > 0,
            networkCommunication: this.apis.http.length > 0,
            cryptographicOperations: this.apis.crypto.length > 0,
            fileSystemAccess: this.apis.filesystem.length > 0,
            systemInteraction: this.apis.system.length > 0,
            externalDependencies: this.apis.external.length > 0,
            securityConcerns: [],
            recommendations: []
        };
        
        // 安全性分析
        if (analysis.networkCommunication) {
            analysis.securityConcerns.push('网络通信可能存在数据泄露风险');
        }
        
        if (analysis.cryptographicOperations) {
            analysis.securityConcerns.push('加密操作需要验证算法安全性');
        }
        
        if (analysis.fileSystemAccess) {
            analysis.securityConcerns.push('文件系统访问需要权限控制');
        }
        
        // 建议
        analysis.recommendations.push('定期审查API使用情况');
        analysis.recommendations.push('监控网络通信和数据传输');
        analysis.recommendations.push('验证加密实现的安全性');
        
        return analysis;
    }

    /**
     * 生成可读性报告
     */
    generateReadableReport(report) {
        const readableReport = `# AI智切扩展API分析报告

## 📊 概览
- **生成时间**: ${report.timestamp}
- **总API数量**: ${report.summary.totalAPIs}

## 📋 分类统计
${report.summary.categories.map(cat => 
    `- **${cat.category}**: ${cat.count}个API`
).join('\n')}

## 🔍 详细分析

### VSCode API (${this.apis.vscode.length}个)
${this.apis.vscode.slice(0, 10).map(api => `- \`${api}\``).join('\n')}
${this.apis.vscode.length > 10 ? `\n... 还有${this.apis.vscode.length - 10}个` : ''}

### HTTP API (${this.apis.http.length}个)
${this.apis.http.slice(0, 10).map(api => `- \`${api}\``).join('\n')}
${this.apis.http.length > 10 ? `\n... 还有${this.apis.http.length - 10}个` : ''}

### 加密API (${this.apis.crypto.length}个)
${this.apis.crypto.slice(0, 10).map(api => `- \`${api}\``).join('\n')}
${this.apis.crypto.length > 10 ? `\n... 还有${this.apis.crypto.length - 10}个` : ''}

### 文件系统API (${this.apis.filesystem.length}个)
${this.apis.filesystem.slice(0, 10).map(api => `- \`${api}\``).join('\n')}
${this.apis.filesystem.length > 10 ? `\n... 还有${this.apis.filesystem.length - 10}个` : ''}

### 系统API (${this.apis.system.length}个)
${this.apis.system.slice(0, 10).map(api => `- \`${api}\``).join('\n')}
${this.apis.system.length > 10 ? `\n... 还有${this.apis.system.length - 10}个` : ''}

## 🛡️ 安全分析
${report.analysis.securityConcerns.map(concern => `- ⚠️ ${concern}`).join('\n')}

## 💡 建议
${report.analysis.recommendations.map(rec => `- 📌 ${rec}`).join('\n')}

---
*此报告由AI智切扩展API提取器自动生成*
`;

        const readablePath = path.join(__dirname, 'API分析报告.md');
        fs.writeFileSync(readablePath, readableReport);
        
        console.log(`📄 可读性报告已保存到: ${readablePath}`);
    }

    /**
     * 运行完整的API提取流程
     */
    run(filePath = 'extension/dist/extension.js') {
        console.log('🚀 启动API信息提取器...\n');
        
        const startTime = Date.now();
        
        try {
            // 提取API信息
            const apis = this.extractFromObfuscatedCode(filePath);
            
            if (apis) {
                // 生成报告
                const report = this.generateAPIReport();
                
                const duration = Date.now() - startTime;
                
                console.log('\n✅ API提取完成！');
                console.log(`⏱️ 总耗时: ${duration}ms`);
                console.log('\n📊 提取结果:');
                console.log(`  VSCode API: ${this.apis.vscode.length}个`);
                console.log(`  HTTP API: ${this.apis.http.length}个`);
                console.log(`  加密API: ${this.apis.crypto.length}个`);
                console.log(`  文件系统API: ${this.apis.filesystem.length}个`);
                console.log(`  系统API: ${this.apis.system.length}个`);
                console.log(`  外部API: ${this.apis.external.length}个`);
                
                return report;
            } else {
                console.error('❌ API提取失败');
                return null;
            }
            
        } catch (error) {
            console.error('❌ 执行过程中发生错误:', error.message);
            return null;
        }
    }
}

// 使用示例
if (require.main === module) {
    const extractor = new APIExtractor();
    extractor.run();
}

module.exports = APIExtractor;
