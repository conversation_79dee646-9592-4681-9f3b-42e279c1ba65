{"timestamp": "2025-08-05T10:22:55.368Z", "originalSize": 213255, "restoredSize": 270050, "stringArraySize": 738, "analysis": {"obfuscationType": "webpack-obfuscator", "decodingMethods": ["Base64", "URI解码", "字符编码转换"], "restorationSuccess": true}, "extractedStrings": ["C2HVD1rLEhreB2n1BwvUDa", "y2LWAgvYDgv4Da", "�oH\u0000��\r���\u000eK\b��\u0007x�", "�l�\u0004��\u0004��\b�I�\u0019ٻz�\u000e\u000b�Ȭ�\u0006\u000b�", "z2XVyMfSu3rVCMfNzq", "x2LUDM9Rzq", "5l+U5Ps55P2d6zMq5AsX6lsLoIa", "DvfguK0", "DuLrBvu", "DgLTzq", "ANLYuKO", "y29TChv0zq", "uhPQCfO", "uKHpwgS", "zxHPC3rZu3LUyW", "yMLUza", "CgvYzM9YBvvWBg9HzcGKmsL7CMv0DxjUifbYB21PC2uUCMvZB2X2zsGPoW", "r3P5vMW", "lL9HDxrOu2vZC2LVBI5ZyxzLu2vZC2LVBI5HChbSEsG", "�\u0007�\u0001��\u0007kي\u000eU\r��\u0012\u0002�\rӆ"], "modules": {"VSCode扩展API": 53, "加密功能": 38, "网络请求": 2, "文件操作": 3, "配置管理": 6, "面板管理": 39, "状态栏": 2, "命令处理": 1}}