/**
 * AI智切扩展安全渗透测试脚本
 * 专门用于安全漏洞检测和渗透测试
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const http = require('http');
const https = require('https');

class SecurityPenetrationTester {
    constructor() {
        this.vulnerabilities = [];
        this.securityTests = [];
        this.config = {
            timeout: 10000,
            maxRetries: 3,
            userAgents: [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'SmartShift-VSCode/1.1.1',
                '<script>alert("xss")</script>',
                'sqlmap/1.0'
            ]
        };
    }

    /**
     * 记录安全测试结果
     */
    logSecurityTest(category, testName, severity, status, description, details = null) {
        const test = {
            category,
            testName,
            severity, // 'CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFO'
            status,   // 'VULNERABLE', 'SECURE', 'UNKNOWN'
            description,
            details,
            timestamp: new Date().toISOString()
        };
        
        this.securityTests.push(test);
        
        if (status === 'VULNERABLE') {
            this.vulnerabilities.push(test);
        }
        
        const severityIcon = {
            'CRITICAL': '🔴',
            'HIGH': '🟠',
            'MEDIUM': '🟡',
            'LOW': '🟢',
            'INFO': 'ℹ️'
        };
        
        const statusIcon = {
            'VULNERABLE': '⚠️',
            'SECURE': '✅',
            'UNKNOWN': '❓'
        };
        
        console.log(`${severityIcon[severity]} ${statusIcon[status]} [${category}] ${testName}: ${description}`);
        
        if (details && status === 'VULNERABLE') {
            console.log(`   详情: ${JSON.stringify(details, null, 2)}`);
        }
    }

    /**
     * 输入验证安全测试
     */
    testInputValidation() {
        console.log('\n🔍 开始输入验证安全测试...');
        
        const maliciousInputs = [
            // XSS攻击载荷
            { input: '<script>alert("xss")</script>', type: 'XSS' },
            { input: 'javascript:alert("xss")', type: 'XSS' },
            { input: '"><script>alert("xss")</script>', type: 'XSS' },
            { input: 'onload=alert("xss")', type: 'XSS' },
            
            // SQL注入载荷
            { input: "'; DROP TABLE users; --", type: 'SQL Injection' },
            { input: "' OR '1'='1", type: 'SQL Injection' },
            { input: "1' UNION SELECT * FROM users --", type: 'SQL Injection' },
            
            // 命令注入载荷
            { input: '; cat /etc/passwd', type: 'Command Injection' },
            { input: '| whoami', type: 'Command Injection' },
            { input: '`id`', type: 'Command Injection' },
            
            // 路径遍历载荷
            { input: '../../../etc/passwd', type: 'Path Traversal' },
            { input: '..\\..\\..\\windows\\system32\\config\\sam', type: 'Path Traversal' },
            
            // 缓冲区溢出载荷
            { input: 'A'.repeat(10000), type: 'Buffer Overflow' },
            { input: 'A'.repeat(100000), type: 'Buffer Overflow' },
            
            // 格式字符串攻击
            { input: '%x%x%x%x', type: 'Format String' },
            { input: '%s%s%s%s', type: 'Format String' }
        ];
        
        maliciousInputs.forEach(({ input, type }) => {
            try {
                // 模拟输入验证函数
                const isValid = this.validateInput(input);
                
                if (isValid) {
                    this.logSecurityTest(
                        'Input Validation',
                        `${type}载荷测试`,
                        'HIGH',
                        'VULNERABLE',
                        '恶意输入未被正确过滤',
                        { input: input.substring(0, 50) + '...' }
                    );
                } else {
                    this.logSecurityTest(
                        'Input Validation',
                        `${type}载荷测试`,
                        'INFO',
                        'SECURE',
                        '恶意输入被正确拒绝'
                    );
                }
            } catch (error) {
                this.logSecurityTest(
                    'Input Validation',
                    `${type}载荷测试`,
                    'MEDIUM',
                    'UNKNOWN',
                    `输入处理异常: ${error.message}`
                );
            }
        });
    }

    /**
     * 模拟输入验证函数
     */
    validateInput(input) {
        // 基本的输入验证逻辑
        if (!input || typeof input !== 'string') return false;
        if (input.length > 1000) return false;
        if (/<script|javascript:|onload=|DROP TABLE|UNION SELECT/i.test(input)) return false;
        if (/[<>\"'&]/.test(input)) return false;
        if (/\.\.[\/\\]/.test(input)) return false;
        if (/%[sx]/.test(input)) return false;
        
        return true;
    }

    /**
     * 网络安全测试
     */
    async testNetworkSecurity() {
        console.log('\n🌐 开始网络安全测试...');

        // 测试1: URL验证
        const maliciousUrls = [
            'javascript:alert("xss")',
            'data:text/html,<script>alert("xss")</script>',
            'file:///etc/passwd',
            'ftp://malicious.com/file',
            'http://127.0.0.1:22/ssh'
        ];

        maliciousUrls.forEach(url => {
            const isSecure = this.validateUrl(url);

            if (!isSecure) {
                this.logSecurityTest(
                    'Network Security',
                    'URL验证测试',
                    'INFO',
                    'SECURE',
                    `恶意URL被正确拒绝: ${url.substring(0, 30)}...`
                );
            } else {
                this.logSecurityTest(
                    'Network Security',
                    'URL验证测试',
                    'HIGH',
                    'VULNERABLE',
                    `恶意URL未被拒绝: ${url.substring(0, 30)}...`
                );
            }
        });

        // 测试2: 请求头验证
        const maliciousHeaders = [
            'X-Forwarded-For: 127.0.0.1\r\nX-Injected: malicious',
            'User-Agent: Normal\r\nX-Injected: header',
            'Referer: http://evil.com\r\nX-Injected: value'
        ];

        maliciousHeaders.forEach(header => {
            const isSecure = !header.includes('\r\n');

            if (isSecure) {
                this.logSecurityTest(
                    'Network Security',
                    '请求头注入测试',
                    'INFO',
                    'SECURE',
                    '请求头格式正常'
                );
            } else {
                this.logSecurityTest(
                    'Network Security',
                    '请求头注入测试',
                    'HIGH',
                    'VULNERABLE',
                    '检测到请求头注入尝试'
                );
            }
        });

        // 测试3: 端口扫描检测
        const suspiciousPorts = [22, 23, 135, 139, 445, 1433, 3389];

        suspiciousPorts.forEach(port => {
            // 模拟端口访问检测
            const isBlocked = port !== 80 && port !== 443;

            if (isBlocked) {
                this.logSecurityTest(
                    'Network Security',
                    `端口访问测试: ${port}`,
                    'INFO',
                    'SECURE',
                    '非标准端口访问被阻止'
                );
            } else {
                this.logSecurityTest(
                    'Network Security',
                    `端口访问测试: ${port}`,
                    'MEDIUM',
                    'VULNERABLE',
                    '标准端口可访问'
                );
            }
        });
    }

    /**
     * URL验证函数
     */
    validateUrl(url) {
        try {
            const urlObj = new URL(url);
            const allowedProtocols = ['http:', 'https:'];
            return allowedProtocols.includes(urlObj.protocol);
        } catch {
            return false;
        }
    }

    /**
     * 加密安全测试
     */
    testCryptographicSecurity() {
        console.log('\n🔐 开始加密安全测试...');
        
        // 测试1: 弱密钥检测
        const weakKeys = [
            '123456',
            'password',
            'admin',
            '000000',
            'qwerty',
            '12345678',
            'a'.repeat(8)
        ];
        
        weakKeys.forEach(key => {
            const strength = this.assessKeyStrength(key);
            
            if (strength < 3) {
                this.logSecurityTest(
                    'Cryptographic Security',
                    '弱密钥检测',
                    'HIGH',
                    'VULNERABLE',
                    `检测到弱密钥: ${key}`,
                    { keyStrength: strength }
                );
            } else {
                this.logSecurityTest(
                    'Cryptographic Security',
                    '弱密钥检测',
                    'INFO',
                    'SECURE',
                    '密钥强度足够'
                );
            }
        });
        
        // 测试2: 随机数质量测试
        const randomValues = [];
        for (let i = 0; i < 1000; i++) {
            randomValues.push(Math.random());
        }
        
        const entropy = this.calculateEntropy(randomValues);
        
        if (entropy < 0.9) {
            this.logSecurityTest(
                'Cryptographic Security',
                '随机数质量测试',
                'MEDIUM',
                'VULNERABLE',
                '随机数熵值过低',
                { entropy: entropy.toFixed(4) }
            );
        } else {
            this.logSecurityTest(
                'Cryptographic Security',
                '随机数质量测试',
                'INFO',
                'SECURE',
                '随机数质量良好'
            );
        }
        
        // 测试3: 哈希碰撞测试
        const testStrings = ['test1', 'test2', 'test3', 'test1']; // 包含重复
        const hashes = testStrings.map(str => 
            crypto.createHash('md5').update(str).digest('hex')
        );
        
        const uniqueHashes = new Set(hashes);
        
        if (hashes.length !== uniqueHashes.size) {
            this.logSecurityTest(
                'Cryptographic Security',
                '哈希碰撞测试',
                'LOW',
                'VULNERABLE',
                '检测到哈希碰撞（预期行为）'
            );
        } else {
            this.logSecurityTest(
                'Cryptographic Security',
                '哈希碰撞测试',
                'INFO',
                'SECURE',
                '无意外哈希碰撞'
            );
        }
        
        // 测试4: 时序攻击测试
        const correctPassword = 'correct-password';
        const testPasswords = [
            'wrong-password',
            'correct-password',
            'c',
            'co',
            'cor',
            'corr'
        ];
        
        testPasswords.forEach(password => {
            const startTime = process.hrtime.bigint();
            const isValid = this.constantTimeCompare(password, correctPassword);
            const endTime = process.hrtime.bigint();
            const duration = Number(endTime - startTime) / 1000000; // 转换为毫秒
            
            // 如果时间差异过大，可能存在时序攻击风险
            if (Math.abs(duration - 0.1) > 0.05 && password !== correctPassword) {
                this.logSecurityTest(
                    'Cryptographic Security',
                    '时序攻击测试',
                    'MEDIUM',
                    'VULNERABLE',
                    '检测到时序差异',
                    { password: password.substring(0, 3) + '...', duration }
                );
            }
        });
    }

    /**
     * 评估密钥强度
     */
    assessKeyStrength(key) {
        let strength = 0;
        
        if (key.length >= 8) strength++;
        if (key.length >= 12) strength++;
        if (/[a-z]/.test(key)) strength++;
        if (/[A-Z]/.test(key)) strength++;
        if (/[0-9]/.test(key)) strength++;
        if (/[^a-zA-Z0-9]/.test(key)) strength++;
        
        return strength;
    }

    /**
     * 计算熵值
     */
    calculateEntropy(values) {
        const counts = {};
        values.forEach(value => {
            const bucket = Math.floor(value * 10);
            counts[bucket] = (counts[bucket] || 0) + 1;
        });
        
        const total = values.length;
        let entropy = 0;
        
        Object.values(counts).forEach(count => {
            const probability = count / total;
            entropy -= probability * Math.log2(probability);
        });
        
        return entropy / Math.log2(10); // 标准化到0-1
    }

    /**
     * 常量时间比较（模拟）
     */
    constantTimeCompare(a, b) {
        // 简单的常量时间比较实现
        const maxLength = Math.max(a.length, b.length);
        let result = 0;
        
        for (let i = 0; i < maxLength; i++) {
            const charA = i < a.length ? a.charCodeAt(i) : 0;
            const charB = i < b.length ? b.charCodeAt(i) : 0;
            result |= charA ^ charB;
        }
        
        return result === 0;
    }

    /**
     * 文件系统安全测试
     */
    testFileSystemSecurity() {
        console.log('\n📁 开始文件系统安全测试...');
        
        // 测试1: 路径遍历攻击
        const maliciousPaths = [
            '../../../etc/passwd',
            '..\\..\\..\\windows\\system32\\config\\sam',
            '/etc/passwd',
            'C:\\Windows\\System32\\config\\SAM',
            '....//....//....//etc/passwd',
            '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd'
        ];
        
        maliciousPaths.forEach(maliciousPath => {
            try {
                const normalizedPath = path.normalize(maliciousPath);
                const isSecure = !normalizedPath.includes('..') && 
                                !normalizedPath.startsWith('/etc') &&
                                !normalizedPath.includes('system32');
                
                if (!isSecure) {
                    this.logSecurityTest(
                        'File System Security',
                        '路径遍历测试',
                        'HIGH',
                        'VULNERABLE',
                        '路径遍历攻击可能成功',
                        { path: maliciousPath, normalized: normalizedPath }
                    );
                } else {
                    this.logSecurityTest(
                        'File System Security',
                        '路径遍历测试',
                        'INFO',
                        'SECURE',
                        '路径遍历被正确阻止'
                    );
                }
            } catch (error) {
                this.logSecurityTest(
                    'File System Security',
                    '路径遍历测试',
                    'INFO',
                    'SECURE',
                    '恶意路径被拒绝'
                );
            }
        });
        
        // 测试2: 文件权限检查
        const testFiles = [
            './package.json',
            './extension/dist/extension.js',
            './不存在的文件.txt'
        ];
        
        testFiles.forEach(filePath => {
            try {
                if (fs.existsSync(filePath)) {
                    const stats = fs.statSync(filePath);
                    const mode = stats.mode.toString(8);
                    
                    // 检查是否有过于宽松的权限
                    if (mode.endsWith('777') || mode.endsWith('666')) {
                        this.logSecurityTest(
                            'File System Security',
                            '文件权限检查',
                            'MEDIUM',
                            'VULNERABLE',
                            '文件权限过于宽松',
                            { file: filePath, permissions: mode }
                        );
                    } else {
                        this.logSecurityTest(
                            'File System Security',
                            '文件权限检查',
                            'INFO',
                            'SECURE',
                            '文件权限设置合理'
                        );
                    }
                }
            } catch (error) {
                this.logSecurityTest(
                    'File System Security',
                    '文件权限检查',
                    'LOW',
                    'UNKNOWN',
                    `无法检查文件权限: ${error.message}`
                );
            }
        });
    }

    /**
     * 生成安全测试报告
     */
    generateSecurityReport() {
        console.log('\n📋 生成安全测试报告...');
        
        const severityCounts = {
            CRITICAL: this.vulnerabilities.filter(v => v.severity === 'CRITICAL').length,
            HIGH: this.vulnerabilities.filter(v => v.severity === 'HIGH').length,
            MEDIUM: this.vulnerabilities.filter(v => v.severity === 'MEDIUM').length,
            LOW: this.vulnerabilities.filter(v => v.severity === 'LOW').length
        };
        
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalTests: this.securityTests.length,
                vulnerabilities: this.vulnerabilities.length,
                severityBreakdown: severityCounts,
                riskScore: this.calculateRiskScore(severityCounts)
            },
            vulnerabilities: this.vulnerabilities,
            allTests: this.securityTests,
            recommendations: this.generateSecurityRecommendations()
        };
        
        const reportPath = path.join(__dirname, '安全渗透测试报告.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
        
        console.log(`📄 安全测试报告已保存到: ${reportPath}`);
        
        // 打印安全摘要
        console.log('\n🛡️ 安全测试摘要:');
        console.log(`  总测试数: ${report.summary.totalTests}`);
        console.log(`  发现漏洞: ${report.summary.vulnerabilities}`);
        console.log(`  风险评分: ${report.summary.riskScore}/100`);
        console.log(`  严重程度分布:`);
        console.log(`    🔴 严重: ${severityCounts.CRITICAL}`);
        console.log(`    🟠 高危: ${severityCounts.HIGH}`);
        console.log(`    🟡 中危: ${severityCounts.MEDIUM}`);
        console.log(`    🟢 低危: ${severityCounts.LOW}`);
        
        return report;
    }

    /**
     * 计算风险评分
     */
    calculateRiskScore(severityCounts) {
        const weights = { CRITICAL: 40, HIGH: 20, MEDIUM: 10, LOW: 5 };
        let score = 0;
        
        Object.entries(severityCounts).forEach(([severity, count]) => {
            score += count * weights[severity];
        });
        
        return Math.min(score, 100); // 最高100分
    }

    /**
     * 生成安全建议
     */
    generateSecurityRecommendations() {
        const recommendations = [];
        
        if (this.vulnerabilities.some(v => v.category === 'Input Validation')) {
            recommendations.push('实施严格的输入验证和输出编码');
            recommendations.push('使用参数化查询防止SQL注入');
            recommendations.push('实施XSS防护机制');
        }
        
        if (this.vulnerabilities.some(v => v.category === 'Network Security')) {
            recommendations.push('强制使用HTTPS通信');
            recommendations.push('实施请求头验证和清理');
            recommendations.push('配置适当的CORS策略');
        }
        
        if (this.vulnerabilities.some(v => v.category === 'Cryptographic Security')) {
            recommendations.push('使用强密钥和安全的随机数生成器');
            recommendations.push('实施常量时间比较防止时序攻击');
            recommendations.push('定期轮换加密密钥');
        }
        
        if (this.vulnerabilities.some(v => v.category === 'File System Security')) {
            recommendations.push('实施严格的文件路径验证');
            recommendations.push('设置适当的文件权限');
            recommendations.push('使用沙箱环境限制文件访问');
        }
        
        return recommendations;
    }

    /**
     * 运行所有安全测试
     */
    async runAllSecurityTests() {
        console.log('🛡️ 开始安全渗透测试...\n');
        
        const startTime = Date.now();
        
        try {
            this.testInputValidation();
            await this.testNetworkSecurity();
            this.testCryptographicSecurity();
            this.testFileSystemSecurity();
            
            const report = this.generateSecurityReport();
            
            const duration = Date.now() - startTime;
            console.log(`\n✅ 安全测试完成! 总耗时: ${duration}ms`);
            
            return report;
            
        } catch (error) {
            console.error('❌ 安全测试过程中发生错误:', error);
            throw error;
        }
    }
}

// 使用示例
if (require.main === module) {
    const securityTester = new SecurityPenetrationTester();
    securityTester.runAllSecurityTests().catch(console.error);
}

module.exports = SecurityPenetrationTester;
