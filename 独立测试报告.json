{"timestamp": "2025-08-05T10:46:33.488Z", "summary": {"totalTests": 27, "passed": 23, "failed": 2, "warnings": 2, "skipped": 0}, "categories": {"FileSystem": {"total": 6, "passed": 0, "failed": 0, "warnings": 0, "skipped": 0, "pass": null}, "Configuration": {"total": 4, "passed": 0, "failed": 0, "warnings": 0, "skipped": 0, "pass": null, "fail": null}, "CodeQuality": {"total": 6, "passed": 0, "failed": 0, "warnings": 0, "skipped": 0, "pass": null, "warn": null}, "Cryptography": {"total": 3, "passed": 0, "failed": 0, "warnings": 0, "skipped": 0, "pass": null, "fail": null}, "InputValidation": {"total": 6, "passed": 0, "failed": 0, "warnings": 0, "skipped": 0, "pass": null}, "Performance": {"total": 2, "passed": 0, "failed": 0, "warnings": 0, "skipped": 0, "pass": null}}, "results": [{"category": "FileSystem", "testName": "文件存在性: extension.vsixmanifest", "status": "PASS", "message": "文件存在，大小: 2451 bytes", "details": null, "timestamp": "2025-08-05T10:46:33.473Z"}, {"category": "FileSystem", "testName": "文件存在性: extension/package.json", "status": "PASS", "message": "文件存在，大小: 1547 bytes", "details": null, "timestamp": "2025-08-05T10:46:33.474Z"}, {"category": "FileSystem", "testName": "文件存在性: extension/dist/extension.js", "status": "PASS", "message": "文件存在，大小: 214543 bytes", "details": null, "timestamp": "2025-08-05T10:46:33.474Z"}, {"category": "FileSystem", "testName": "文件存在性: extension/LICENSE.md", "status": "PASS", "message": "文件存在，大小: 1090 bytes", "details": null, "timestamp": "2025-08-05T10:46:33.474Z"}, {"category": "FileSystem", "testName": "文件存在性: extension/README.md", "status": "PASS", "message": "文件存在，大小: 1914 bytes", "details": null, "timestamp": "2025-08-05T10:46:33.475Z"}, {"category": "FileSystem", "testName": "文件权限检查", "status": "PASS", "message": "主程序文件可读", "details": null, "timestamp": "2025-08-05T10:46:33.475Z"}, {"category": "Configuration", "testName": "package.json结构", "status": "PASS", "message": "所有必需字段存在", "details": null, "timestamp": "2025-08-05T10:46:33.475Z"}, {"category": "Configuration", "testName": "依赖项检查", "status": "PASS", "message": "发现 3 个依赖项: axios, fernet, uri-js", "details": null, "timestamp": "2025-08-05T10:46:33.475Z"}, {"category": "Configuration", "testName": "VSCode命令配置", "status": "PASS", "message": "配置了 2 个命令", "details": null, "timestamp": "2025-08-05T10:46:33.475Z"}, {"category": "Configuration", "testName": "VSIX清单结构", "status": "FAIL", "message": "清单文件结构不完整", "details": null, "timestamp": "2025-08-05T10:46:33.475Z"}, {"category": "CodeQuality", "testName": "代码统计", "status": "PASS", "message": "大小: 208.26KB, 函数: 320, 变量: 525", "details": null, "timestamp": "2025-08-05T10:46:33.477Z"}, {"category": "CodeQuality", "testName": "代码混淆检测", "status": "WARN", "message": "检测到高度混淆 (评分: 768)", "details": null, "timestamp": "2025-08-05T10:46:33.477Z"}, {"category": "CodeQuality", "testName": "安全检查: 动态代码执行", "status": "PASS", "message": "未发现风险", "details": null, "timestamp": "2025-08-05T10:46:33.477Z"}, {"category": "CodeQuality", "testName": "安全检查: 动态函数创建", "status": "PASS", "message": "未发现风险", "details": null, "timestamp": "2025-08-05T10:46:33.477Z"}, {"category": "CodeQuality", "testName": "安全检查: HTML注入风险", "status": "WARN", "message": "发现 2 个匹配项", "details": null, "timestamp": "2025-08-05T10:46:33.478Z"}, {"category": "CodeQuality", "testName": "安全检查: 文档写入", "status": "PASS", "message": "未发现风险", "details": null, "timestamp": "2025-08-05T10:46:33.478Z"}, {"category": "Cryptography", "testName": "SHA256哈希测试", "status": "PASS", "message": "哈希功能正常", "details": null, "timestamp": "2025-08-05T10:46:33.479Z"}, {"category": "Cryptography", "testName": "随机数生成测试", "status": "PASS", "message": "随机数生成正常", "details": null, "timestamp": "2025-08-05T10:46:33.479Z"}, {"category": "Cryptography", "testName": "AES-GCM加密测试", "status": "FAIL", "message": "加密测试失败: crypto.createCipherGCM is not a function", "details": null, "timestamp": "2025-08-05T10:46:33.479Z"}, {"category": "InputValidation", "testName": "输入验证: 有效激活码", "status": "PASS", "message": "验证结果正确: true", "details": null, "timestamp": "2025-08-05T10:46:33.480Z"}, {"category": "InputValidation", "testName": "输入验证: 空字符串", "status": "PASS", "message": "验证结果正确: false", "details": null, "timestamp": "2025-08-05T10:46:33.480Z"}, {"category": "InputValidation", "testName": "输入验证: 过长字符串", "status": "PASS", "message": "验证结果正确: false", "details": null, "timestamp": "2025-08-05T10:46:33.480Z"}, {"category": "InputValidation", "testName": "输入验证: XSS攻击载荷", "status": "PASS", "message": "验证结果正确: false", "details": null, "timestamp": "2025-08-05T10:46:33.480Z"}, {"category": "InputValidation", "testName": "输入验证: SQL注入载荷", "status": "PASS", "message": "验证结果正确: false", "details": null, "timestamp": "2025-08-05T10:46:33.480Z"}, {"category": "InputValidation", "testName": "输入验证: 路径遍历载荷", "status": "PASS", "message": "验证结果正确: false", "details": null, "timestamp": "2025-08-05T10:46:33.480Z"}, {"category": "Performance", "testName": "文件读取性能", "status": "PASS", "message": "10次读取耗时: 7ms", "details": null, "timestamp": "2025-08-05T10:46:33.487Z"}, {"category": "Performance", "testName": "加密性能测试", "status": "PASS", "message": "100次SHA256耗时: 0ms", "details": null, "timestamp": "2025-08-05T10:46:33.487Z"}], "recommendations": ["定期更新依赖项以修复安全漏洞", "实施更严格的输入验证机制", "考虑使用更安全的加密算法", "添加更多的单元测试覆盖", "监控扩展的运行时性能"]}