/**
 * AI智切扩展专业字符串解码器
 * 专门用于解码混淆代码中的字符串和提取API信息
 */

const fs = require('fs');
const path = require('path');

class ProfessionalStringDecoder {
    constructor() {
        this.stringArray = [];
        this.decodedStrings = [];
        this.apiCalls = {
            vscodeAPIs: [],
            httpRequests: [],
            fileOperations: [],
            cryptoOperations: [],
            webviewMessages: [],
            systemCalls: [],
            configKeys: [],
            errorMessages: []
        };
        this.decodingFunction = null;
    }

    /**
     * 分析混淆代码并提取所有API信息
     */
    analyzeCode(filePath) {
        console.log('🔍 开始专业字符串解码分析...');
        
        try {
            const code = fs.readFileSync(filePath, 'utf8');
            
            // 1. 提取字符串数组
            this.extractStringArray(code);
            
            // 2. 分析解码函数
            this.analyzeDecodingFunction(code);
            
            // 3. 手动解码关键字符串
            this.manualDecodeStrings();
            
            // 4. 从解码字符串中提取API信息
            this.extractAPIFromDecodedStrings();
            
            // 5. 直接从代码中提取明文API
            this.extractPlaintextAPIs(code);
            
            // 6. 分析WebView HTML内容
            this.analyzeWebViewHTML(code);
            
            console.log('✅ 专业解码分析完成');
            return this.generateComprehensiveReport();
            
        } catch (error) {
            console.error('❌ 解码分析失败:', error.message);
            return null;
        }
    }

    /**
     * 提取字符串数组
     */
    extractStringArray(code) {
        console.log('📋 提取字符串数组...');
        
        // 查找字符串数组的多种模式
        const patterns = [
            /var\s+_0x[a-f0-9]+\s*=\s*\[(.*?)\]/s,
            /const\s+_0x[a-f0-9]+\s*=\s*\[(.*?)\]/s,
            /\[(['"'][^'"]*['"][,\s]*){10,}\]/s
        ];
        
        for (const pattern of patterns) {
            const match = code.match(pattern);
            if (match) {
                const arrayContent = match[1];
                const stringPattern = /'([^'\\]|\\.)*'|"([^"\\]|\\.)*"/g;
                const strings = arrayContent.match(stringPattern) || [];
                
                this.stringArray = strings.map(str => str.slice(1, -1)); // 移除引号
                console.log(`📊 提取到 ${this.stringArray.length} 个字符串`);
                break;
            }
        }
    }

    /**
     * 分析解码函数
     */
    analyzeDecodingFunction(code) {
        console.log('🔧 分析解码函数...');
        
        // 查找解码函数的多种模式
        const patterns = [
            /function\s+a0_0x53e5\([^)]*\)\s*{([^{}]*(?:{[^{}]*}[^{}]*)*)}/,
            /function\s+_0x[a-f0-9]+\([^)]*\)\s*{([^{}]*(?:{[^{}]*}[^{}]*)*)}/,
            /var\s+_0x[a-f0-9]+\s*=\s*function\([^)]*\)\s*{([^{}]*(?:{[^{}]*}[^{}]*)*)}/
        ];
        
        for (const pattern of patterns) {
            const match = code.match(pattern);
            if (match) {
                this.decodingFunction = match[1];
                console.log('📋 找到解码函数');
                
                // 分析解码逻辑
                if (this.decodingFunction.includes('decodeURIComponent')) {
                    console.log('  - 使用URI解码');
                }
                if (this.decodingFunction.includes('atob')) {
                    console.log('  - 使用Base64解码');
                }
                if (this.decodingFunction.includes('fromCharCode')) {
                    console.log('  - 使用字符码转换');
                }
                break;
            }
        }
    }

    /**
     * 手动解码字符串
     */
    manualDecodeStrings() {
        console.log('🔓 手动解码字符串...');
        
        let decodedCount = 0;
        
        this.stringArray.forEach((str, index) => {
            const decoded = this.attemptMultipleDecoding(str);
            
            this.decodedStrings.push({
                index: index,
                original: str,
                decoded: decoded,
                isDecoded: decoded !== str
            });
            
            if (decoded !== str) {
                decodedCount++;
            }
        });
        
        console.log(`📊 成功解码 ${decodedCount}/${this.stringArray.length} 个字符串`);
    }

    /**
     * 尝试多种解码方法
     */
    attemptMultipleDecoding(str) {
        const methods = [
            this.decodeBase64.bind(this),
            this.decodeURI.bind(this),
            this.decodeHex.bind(this),
            this.decodeROT13.bind(this),
            this.decodeCustom.bind(this)
        ];
        
        for (const method of methods) {
            try {
                const result = method(str);
                if (result !== str && this.isValidString(result)) {
                    return result;
                }
            } catch (e) {
                // 继续尝试下一种方法
            }
        }
        
        return str; // 无法解码
    }

    /**
     * Base64解码
     */
    decodeBase64(str) {
        try {
            if (/^[A-Za-z0-9+/]*={0,2}$/.test(str) && str.length % 4 === 0) {
                return Buffer.from(str, 'base64').toString('utf8');
            }
        } catch (e) {}
        return str;
    }

    /**
     * URI解码
     */
    decodeURI(str) {
        try {
            const decoded = decodeURIComponent(str);
            return decoded !== str ? decoded : str;
        } catch (e) {
            return str;
        }
    }

    /**
     * 十六进制解码
     */
    decodeHex(str) {
        try {
            if (/^[0-9a-fA-F]+$/.test(str) && str.length % 2 === 0) {
                return Buffer.from(str, 'hex').toString('utf8');
            }
        } catch (e) {}
        return str;
    }

    /**
     * ROT13解码
     */
    decodeROT13(str) {
        return str.replace(/[a-zA-Z]/g, function(c) {
            return String.fromCharCode(
                (c <= 'Z' ? 90 : 122) >= (c = c.charCodeAt(0) + 13) ? c : c - 26
            );
        });
    }

    /**
     * 自定义解码（基于观察到的模式）
     */
    decodeCustom(str) {
        // 尝试反转字符串
        const reversed = str.split('').reverse().join('');
        if (this.isValidString(reversed)) {
            return reversed;
        }
        
        // 尝试字符偏移
        const shifted = str.split('').map(c => 
            String.fromCharCode(c.charCodeAt(0) - 1)
        ).join('');
        if (this.isValidString(shifted)) {
            return shifted;
        }
        
        return str;
    }

    /**
     * 验证字符串是否有效
     */
    isValidString(str) {
        // 检查是否包含可打印字符
        if (!/^[\x20-\x7E\s]*$/.test(str)) return false;
        
        // 检查是否包含常见的API关键词
        const apiKeywords = [
            'vscode', 'window', 'command', 'message', 'http', 'https',
            'api', 'get', 'post', 'file', 'path', 'config', 'error',
            'success', 'login', 'account', 'user', 'token', 'auth'
        ];
        
        const lowerStr = str.toLowerCase();
        return apiKeywords.some(keyword => lowerStr.includes(keyword)) ||
               str.length > 3 && /^[a-zA-Z][a-zA-Z0-9._-]*$/.test(str);
    }

    /**
     * 从解码字符串中提取API信息
     */
    extractAPIFromDecodedStrings() {
        console.log('🔍 从解码字符串提取API信息...');
        
        this.decodedStrings.forEach(item => {
            if (item.isDecoded) {
                const str = item.decoded;
                
                // VSCode API
                if (str.includes('vscode') || str.includes('window') || str.includes('workspace')) {
                    this.apiCalls.vscodeAPIs.push(str);
                }
                
                // HTTP请求
                if (str.includes('http') || str.includes('api') || str.includes('endpoint')) {
                    this.apiCalls.httpRequests.push(str);
                }
                
                // 文件操作
                if (str.includes('file') || str.includes('path') || str.includes('read') || str.includes('write')) {
                    this.apiCalls.fileOperations.push(str);
                }
                
                // 加密操作
                if (str.includes('encrypt') || str.includes('decrypt') || str.includes('hash') || str.includes('crypto')) {
                    this.apiCalls.cryptoOperations.push(str);
                }
                
                // WebView消息
                if (str.includes('command') || str.includes('message') || str.includes('post')) {
                    this.apiCalls.webviewMessages.push(str);
                }
                
                // 配置键
                if (str.includes('.') && /^[a-zA-Z][a-zA-Z0-9.]*$/.test(str)) {
                    this.apiCalls.configKeys.push(str);
                }
                
                // 错误消息
                if (str.includes('error') || str.includes('fail') || str.includes('invalid')) {
                    this.apiCalls.errorMessages.push(str);
                }
            }
        });
    }

    /**
     * 提取明文API
     */
    extractPlaintextAPIs(code) {
        console.log('📝 提取明文API...');
        
        // VSCode API调用
        const vscodePatterns = [
            /vscode\.commands\.registerCommand\(['"]([^'"]+)['"]/g,
            /vscode\.window\.createWebviewPanel/g,
            /vscode\.window\.showInformationMessage/g,
            /vscode\.window\.showErrorMessage/g,
            /vscode\.workspace\.getConfiguration/g
        ];
        
        vscodePatterns.forEach(pattern => {
            const matches = [...code.matchAll(pattern)];
            matches.forEach(match => {
                this.apiCalls.vscodeAPIs.push(match[1] || match[0]);
            });
        });
        
        // HTTP URL
        const urlPattern = /https?:\/\/[^\s'"`,)}\]]+/g;
        const urls = code.match(urlPattern) || [];
        this.apiCalls.httpRequests.push(...urls);
        
        // 文件路径
        const pathPattern = /['"]([a-zA-Z]:[\\\/][^'"]*|\/[^'"]*)['"]/g;
        const paths = [...code.matchAll(pathPattern)];
        paths.forEach(match => {
            this.apiCalls.fileOperations.push(match[1]);
        });
    }

    /**
     * 分析WebView HTML内容
     */
    analyzeWebViewHTML(code) {
        console.log('🌐 分析WebView HTML内容...');
        
        // 查找HTML内容
        const htmlPattern = /<!DOCTYPE html>.*?<\/html>/s;
        const htmlMatch = code.match(htmlPattern);
        
        if (htmlMatch) {
            const html = htmlMatch[0];
            
            // 提取JavaScript函数
            const functionPattern = /function\s+(\w+)\s*\(/g;
            const functions = [...html.matchAll(functionPattern)];
            functions.forEach(match => {
                this.apiCalls.webviewMessages.push(`函数: ${match[1]}`);
            });
            
            // 提取事件处理
            const eventPattern = /on\w+\s*=\s*['"]([^'"]+)['"]/g;
            const events = [...html.matchAll(eventPattern)];
            events.forEach(match => {
                this.apiCalls.webviewMessages.push(`事件: ${match[1]}`);
            });
            
            // 提取消息类型
            const messagePattern = /command\s*:\s*['"]([^'"]+)['"]/g;
            const messages = [...html.matchAll(messagePattern)];
            messages.forEach(match => {
                this.apiCalls.webviewMessages.push(`命令: ${match[1]}`);
            });
            
            // 提取ID和类名
            const idPattern = /id\s*=\s*['"]([^'"]+)['"]/g;
            const ids = [...html.matchAll(idPattern)];
            ids.forEach(match => {
                this.apiCalls.webviewMessages.push(`元素ID: ${match[1]}`);
            });
        }
    }

    /**
     * 生成综合报告
     */
    generateComprehensiveReport() {
        console.log('📋 生成综合报告...');
        
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalStrings: this.stringArray.length,
                decodedStrings: this.decodedStrings.filter(s => s.isDecoded).length,
                totalAPIs: Object.values(this.apiCalls).reduce((sum, arr) => sum + arr.length, 0)
            },
            decodedStrings: this.decodedStrings.filter(s => s.isDecoded).slice(0, 20),
            apiCalls: this.apiCalls,
            analysis: {
                decodingSuccess: (this.decodedStrings.filter(s => s.isDecoded).length / this.stringArray.length * 100).toFixed(1),
                mainFeatures: this.identifyMainFeatures(),
                securityRisks: this.identifySecurityRisks(),
                recommendations: this.generateRecommendations()
            }
        };
        
        // 保存报告
        const reportPath = path.join(__dirname, '专业字符串解码报告.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        // 生成可读报告
        this.generateReadableReport(report);
        
        console.log(`📄 报告已保存到: ${reportPath}`);
        return report;
    }

    /**
     * 识别主要功能
     */
    identifyMainFeatures() {
        const features = [];
        
        if (this.apiCalls.vscodeAPIs.length > 0) {
            features.push('VSCode扩展集成');
        }
        if (this.apiCalls.httpRequests.length > 0) {
            features.push('网络通信');
        }
        if (this.apiCalls.fileOperations.length > 0) {
            features.push('文件系统访问');
        }
        if (this.apiCalls.cryptoOperations.length > 0) {
            features.push('加密操作');
        }
        if (this.apiCalls.webviewMessages.length > 0) {
            features.push('用户界面');
        }
        
        return features;
    }

    /**
     * 识别安全风险
     */
    identifySecurityRisks() {
        const risks = [];
        
        if (this.apiCalls.httpRequests.some(req => req.includes('http://'))) {
            risks.push('使用不安全的HTTP协议');
        }
        if (this.apiCalls.fileOperations.some(op => op.includes('write'))) {
            risks.push('文件写入操作可能存在风险');
        }
        if (this.decodedStrings.some(s => s.decoded.includes('password') || s.decoded.includes('token'))) {
            risks.push('可能包含硬编码的敏感信息');
        }
        
        return risks;
    }

    /**
     * 生成建议
     */
    generateRecommendations() {
        return [
            '对所有网络通信使用HTTPS',
            '避免硬编码敏感信息',
            '实施适当的输入验证',
            '使用安全的数据存储方法',
            '定期进行安全审计'
        ];
    }

    /**
     * 生成可读报告
     */
    generateReadableReport(report) {
        const readableReport = `# AI智切扩展专业字符串解码报告

## 📊 解码概览
- **分析时间**: ${report.timestamp}
- **总字符串数**: ${report.summary.totalStrings}
- **成功解码**: ${report.summary.decodedStrings} (${report.analysis.decodingSuccess}%)
- **总API数量**: ${report.summary.totalAPIs}

## 🔓 成功解码的字符串 (前20个)
${report.decodedStrings.map((item, i) => 
    `${i + 1}. \`${item.original.substring(0, 30)}...\` → \`${item.decoded}\``
).join('\n') || '- 无成功解码的字符串'}

## 🔧 VSCode API (${this.apiCalls.vscodeAPIs.length}个)
${this.apiCalls.vscodeAPIs.slice(0, 10).map(api => `- \`${api}\``).join('\n') || '- 无'}
${this.apiCalls.vscodeAPIs.length > 10 ? `\n... 还有${this.apiCalls.vscodeAPIs.length - 10}个` : ''}

## 🌐 HTTP请求 (${this.apiCalls.httpRequests.length}个)
${this.apiCalls.httpRequests.slice(0, 10).map(req => `- \`${req}\``).join('\n') || '- 无'}
${this.apiCalls.httpRequests.length > 10 ? `\n... 还有${this.apiCalls.httpRequests.length - 10}个` : ''}

## 📁 文件操作 (${this.apiCalls.fileOperations.length}个)
${this.apiCalls.fileOperations.slice(0, 10).map(op => `- \`${op}\``).join('\n') || '- 无'}
${this.apiCalls.fileOperations.length > 10 ? `\n... 还有${this.apiCalls.fileOperations.length - 10}个` : ''}

## 🔐 加密操作 (${this.apiCalls.cryptoOperations.length}个)
${this.apiCalls.cryptoOperations.slice(0, 10).map(crypto => `- \`${crypto}\``).join('\n') || '- 无'}
${this.apiCalls.cryptoOperations.length > 10 ? `\n... 还有${this.apiCalls.cryptoOperations.length - 10}个` : ''}

## 💬 WebView消息 (${this.apiCalls.webviewMessages.length}个)
${this.apiCalls.webviewMessages.slice(0, 10).map(msg => `- ${msg}`).join('\n') || '- 无'}
${this.apiCalls.webviewMessages.length > 10 ? `\n... 还有${this.apiCalls.webviewMessages.length - 10}个` : ''}

## ⚙️ 配置键 (${this.apiCalls.configKeys.length}个)
${this.apiCalls.configKeys.slice(0, 10).map(key => `- \`${key}\``).join('\n') || '- 无'}
${this.apiCalls.configKeys.length > 10 ? `\n... 还有${this.apiCalls.configKeys.length - 10}个` : ''}

## 🚨 错误消息 (${this.apiCalls.errorMessages.length}个)
${this.apiCalls.errorMessages.slice(0, 5).map(err => `- "${err}"`).join('\n') || '- 无'}
${this.apiCalls.errorMessages.length > 5 ? `\n... 还有${this.apiCalls.errorMessages.length - 5}个` : ''}

## 🎯 主要功能
${report.analysis.mainFeatures.map(feature => `- ✅ ${feature}`).join('\n') || '- 无法识别主要功能'}

## 🛡️ 安全风险
${report.analysis.securityRisks.map(risk => `- ⚠️ ${risk}`).join('\n') || '- 未发现明显安全风险'}

## 💡 建议
${report.analysis.recommendations.map(rec => `- 📌 ${rec}`).join('\n')}

---
*此报告由AI智切扩展专业字符串解码器自动生成*
`;

        const readablePath = path.join(__dirname, '专业字符串解码报告.md');
        fs.writeFileSync(readablePath, readableReport);
        
        console.log(`📄 可读报告已保存到: ${readablePath}`);
    }

    /**
     * 运行解码分析
     */
    run(filePath = 'extension/dist/extension.js') {
        console.log('🚀 启动专业字符串解码器...\n');
        
        const startTime = Date.now();
        
        try {
            const report = this.analyzeCode(filePath);
            
            if (report) {
                const duration = Date.now() - startTime;
                
                console.log('\n✅ 专业解码分析完成！');
                console.log(`⏱️ 总耗时: ${duration}ms`);
                console.log('\n📊 解码结果:');
                console.log(`  字符串解码: ${report.summary.decodedStrings}/${report.summary.totalStrings} (${report.analysis.decodingSuccess}%)`);
                console.log(`  VSCode API: ${this.apiCalls.vscodeAPIs.length}个`);
                console.log(`  HTTP请求: ${this.apiCalls.httpRequests.length}个`);
                console.log(`  文件操作: ${this.apiCalls.fileOperations.length}个`);
                console.log(`  WebView消息: ${this.apiCalls.webviewMessages.length}个`);
                console.log(`  配置键: ${this.apiCalls.configKeys.length}个`);
                
                return report;
            } else {
                console.error('❌ 解码分析失败');
                return null;
            }
            
        } catch (error) {
            console.error('❌ 执行过程中发生错误:', error.message);
            return null;
        }
    }
}

// 使用示例
if (require.main === module) {
    const decoder = new ProfessionalStringDecoder();
    decoder.run();
}

module.exports = ProfessionalStringDecoder;
