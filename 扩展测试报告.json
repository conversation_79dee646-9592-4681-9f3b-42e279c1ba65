{"timestamp": "2025-08-05T10:16:44.507Z", "summary": {"totalTests": 26, "passed": 17, "failed": 1, "warnings": 2, "info": 6}, "results": [{"test": "文件存在性检查: package.json", "status": "PASS", "message": "文件存在，大小: 1547 bytes", "details": null, "timestamp": "2025-08-05T10:16:44.500Z"}, {"test": "文件存在性检查: dist/extension.js", "status": "PASS", "message": "文件存在，大小: 214543 bytes", "details": null, "timestamp": "2025-08-05T10:16:44.501Z"}, {"test": "文件存在性检查: resources/logo.png", "status": "PASS", "message": "文件存在，大小: 139038 bytes", "details": null, "timestamp": "2025-08-05T10:16:44.501Z"}, {"test": "文件存在性检查: LICENSE.md", "status": "PASS", "message": "文件存在，大小: 1090 bytes", "details": null, "timestamp": "2025-08-05T10:16:44.501Z"}, {"test": "文件存在性检查: README.md", "status": "PASS", "message": "文件存在，大小: 1914 bytes", "details": null, "timestamp": "2025-08-05T10:16:44.501Z"}, {"test": "package.json字段: name", "status": "PASS", "message": "字段存在: \"smartshift-manager\"", "details": null, "timestamp": "2025-08-05T10:16:44.502Z"}, {"test": "package.json字段: version", "status": "PASS", "message": "字段存在: \"1.1.1\"", "details": null, "timestamp": "2025-08-05T10:16:44.502Z"}, {"test": "package.json字段: engines", "status": "PASS", "message": "字段存在: {\"vscode\":\"^1.74.0\"}", "details": null, "timestamp": "2025-08-05T10:16:44.502Z"}, {"test": "package.json字段: main", "status": "PASS", "message": "字段存在: \"./dist/extension.js\"", "details": null, "timestamp": "2025-08-05T10:16:44.502Z"}, {"test": "package.json字段: contributes", "status": "PASS", "message": "字段存在: {\"commands\":[{\"command\":\"smartshift-manager.openPanel\",\"title\":\"账号管理\",\"category\":\"智切\"},{\"command\":\"smartshift-manager.openLogs\",\"title\":\"查看日志\",\"category\":\"智切\"}]}", "details": null, "timestamp": "2025-08-05T10:16:44.502Z"}, {"test": "package.json依赖项", "status": "INFO", "message": "发现 3 个依赖项", "details": {"axios": "^1.6.0", "fernet": "^0.4.0", "uri-js": "^4.4.1"}, "timestamp": "2025-08-05T10:16:44.502Z"}, {"test": "VSCode命令配置", "status": "PASS", "message": "配置了 2 个命令", "details": [{"command": "smartshift-manager.openPanel", "title": "账号管理", "category": "智切"}, {"command": "smartshift-manager.openLogs", "title": "查看日志", "category": "智切"}], "timestamp": "2025-08-05T10:16:44.502Z"}, {"test": "安全检查: 动态代码执行", "status": "PASS", "message": "未发现风险", "details": null, "timestamp": "2025-08-05T10:16:44.504Z"}, {"test": "安全检查: 动态函数创建", "status": "PASS", "message": "未发现风险", "details": null, "timestamp": "2025-08-05T10:16:44.504Z"}, {"test": "安全检查: 子进程执行", "status": "PASS", "message": "未发现风险", "details": null, "timestamp": "2025-08-05T10:16:44.504Z"}, {"test": "安全检查: 命令执行", "status": "PASS", "message": "未发现风险", "details": null, "timestamp": "2025-08-05T10:16:44.504Z"}, {"test": "安全检查: 进程启动", "status": "PASS", "message": "未发现风险", "details": null, "timestamp": "2025-08-05T10:16:44.504Z"}, {"test": "安全检查: 文件系统访问", "status": "WARN", "message": "发现 1 个匹配项，风险级别: MEDIUM", "details": null, "timestamp": "2025-08-05T10:16:44.504Z"}, {"test": "安全检查: 网络请求", "status": "PASS", "message": "未发现风险", "details": null, "timestamp": "2025-08-05T10:16:44.505Z"}, {"test": "代码混淆程度", "status": "INFO", "message": "混淆评分: 2862，高度混淆", "details": null, "timestamp": "2025-08-05T10:16:44.506Z"}, {"test": "网络端点测试: https://api.example.com/auth", "status": "INFO", "message": "模拟测试 - 未实际发送请求", "details": null, "timestamp": "2025-08-05T10:16:44.506Z"}, {"test": "网络端点测试: https://api.example.com/accounts", "status": "INFO", "message": "模拟测试 - 未实际发送请求", "details": null, "timestamp": "2025-08-05T10:16:44.506Z"}, {"test": "网络端点测试: https://api.example.com/switch", "status": "INFO", "message": "模拟测试 - 未实际发送请求", "details": null, "timestamp": "2025-08-05T10:16:44.507Z"}, {"test": "网络库检测", "status": "WARN", "message": "发现网络库: axios", "details": null, "timestamp": "2025-08-05T10:16:44.507Z"}, {"test": "加密功能测试", "status": "FAIL", "message": "加密测试失败: crypto.createCipher is not a function", "details": null, "timestamp": "2025-08-05T10:16:44.507Z"}, {"test": "Fernet加密库", "status": "INFO", "message": "Fernet版本: ^0.4.0", "details": null, "timestamp": "2025-08-05T10:16:44.507Z"}], "recommendations": ["定期更新依赖项以修复安全漏洞", "实施代码签名以确保完整性", "添加更多的单元测试覆盖", "考虑使用更安全的加密实现", "监控网络通信以检测异常行为"]}