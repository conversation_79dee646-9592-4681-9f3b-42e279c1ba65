# AI智切扩展 - 切换账号和重置机器码详细分析

## 🔄 切换账号功能详细分析

### 1. 切换账号的完整流程

#### 前端触发流程
```javascript
// 用户点击账号切换按钮
function switchAccount(accountId) {
    // 1. 显示加载状态
    showStatus('accountStatus', '正在切换账号...', 'loading');
    
    // 2. 发送切换请求到扩展
    vscode.postMessage({
        command: 'switchAccount',
        accountId: accountId,
        timestamp: Date.now()
    });
}
```

#### 扩展后端处理流程
```javascript
// 扩展接收到切换账号消息
async function handleSwitchAccount(message) {
    const { accountId } = message;
    
    try {
        // 步骤1: 验证当前用户状态
        const currentUser = await getCurrentUserInfo();
        if (!currentUser || !currentUser.token) {
            throw new Error('用户未登录');
        }
        
        // 步骤2: 验证目标账号
        const targetAccount = await validateTargetAccount(accountId);
        if (!targetAccount) {
            throw new Error('目标账号不存在或无权限');
        }
        
        // 步骤3: 调用服务器切换API
        const switchResult = await callSwitchAccountAPI(accountId, currentUser.token);
        
        // 步骤4: 更新本地配置
        await updateLocalConfiguration(switchResult);
        
        // 步骤5: 更新Augment插件配置
        await updateAugmentPluginConfig(switchResult.account);
        
        // 步骤6: 清理旧会话数据
        await cleanupOldSessionData();
        
        // 步骤7: 建立新会话
        await establishNewSession(switchResult.session);
        
        // 步骤8: 通知前端切换成功
        panel.webview.postMessage({
            command: 'switchAccountResult',
            success: true,
            account: switchResult.account,
            message: '账号切换成功'
        });
        
        // 步骤9: 更新状态栏显示
        updateStatusBarDisplay(switchResult.account);
        
        // 步骤10: 记录切换日志
        logAccountSwitch(accountId, switchResult);
        
    } catch (error) {
        // 错误处理
        panel.webview.postMessage({
            command: 'switchAccountResult',
            success: false,
            error: error.message
        });
        
        logError('账号切换失败', error);
    }
}
```

### 2. 切换账号的具体操作

#### 2.1 服务器API调用
```javascript
async function callSwitchAccountAPI(accountId, token) {
    const response = await fetch('https://api.smartshift.com/api/accounts/switch', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'User-Agent': 'SmartShift-VSCode/1.1.1',
            'X-Client-Version': '1.1.1',
            'X-Platform': 'vscode',
            'X-Machine-Id': getMachineId()
        },
        body: JSON.stringify({
            accountId: accountId,
            reason: 'manual_switch',
            clientInfo: {
                version: '1.1.1',
                platform: 'vscode',
                machineId: getMachineId(),
                timestamp: Date.now()
            }
        })
    });
    
    const result = await response.json();
    if (!result.success) {
        throw new Error(result.message || '切换失败');
    }
    
    return result;
}
```

#### 2.2 更新本地配置
```javascript
async function updateLocalConfiguration(switchResult) {
    const vscode = require('vscode');
    const config = vscode.workspace.getConfiguration();
    
    // 更新当前账号信息
    await config.update('smartshift.currentAccount', {
        id: switchResult.account.id,
        email: switchResult.account.email,
        type: switchResult.account.type,
        switchedAt: new Date().toISOString()
    }, vscode.ConfigurationTarget.Global);
    
    // 更新会话信息
    await config.update('smartshift.currentSession', {
        sessionId: switchResult.session.session_id,
        expiresAt: switchResult.session.expires_at,
        createdAt: new Date().toISOString()
    }, vscode.ConfigurationTarget.Global);
    
    // 加密存储敏感信息
    const encryptedAccountData = encryptData({
        accountToken: switchResult.account.token,
        refreshToken: switchResult.account.refresh_token,
        apiKeys: switchResult.account.api_keys
    });
    
    await config.update('smartshift.encryptedAccountData', encryptedAccountData, vscode.ConfigurationTarget.Global);
}
```

#### 2.3 更新Augment插件配置
```javascript
async function updateAugmentPluginConfig(accountInfo) {
    try {
        // 检查Augment插件是否安装
        const augmentExtension = vscode.extensions.getExtension('augmentcode.augment');
        
        if (augmentExtension && augmentExtension.isActive) {
            // 获取Augment插件的配置
            const augmentConfig = vscode.workspace.getConfiguration('augment');
            
            // 更新API密钥
            if (accountInfo.api_keys && accountInfo.api_keys.augment) {
                await augmentConfig.update('apiKey', accountInfo.api_keys.augment, vscode.ConfigurationTarget.Global);
            }
            
            // 更新用户信息
            await augmentConfig.update('userInfo', {
                email: accountInfo.email,
                userId: accountInfo.id,
                plan: accountInfo.type,
                updatedBy: 'smartshift-manager'
            }, vscode.ConfigurationTarget.Global);
            
            // 触发Augment插件重新加载配置
            await vscode.commands.executeCommand('augment.reloadConfiguration');
            
            console.log('Augment插件配置已更新');
        } else {
            console.log('Augment插件未安装或未激活');
        }
    } catch (error) {
        console.error('更新Augment插件配置失败:', error);
    }
}
```

#### 2.4 清理和建立会话
```javascript
async function cleanupOldSessionData() {
    // 清理旧的缓存数据
    const fs = require('fs');
    const path = require('path');
    const os = require('os');
    
    const cacheDir = path.join(os.homedir(), '.smartshift', 'cache');
    
    try {
        if (fs.existsSync(cacheDir)) {
            const files = fs.readdirSync(cacheDir);
            files.forEach(file => {
                if (file.startsWith('session_') || file.startsWith('account_')) {
                    fs.unlinkSync(path.join(cacheDir, file));
                }
            });
        }
    } catch (error) {
        console.error('清理缓存失败:', error);
    }
}

async function establishNewSession(sessionInfo) {
    // 创建新的会话文件
    const fs = require('fs');
    const path = require('path');
    const os = require('os');
    
    const sessionDir = path.join(os.homedir(), '.smartshift', 'sessions');
    
    if (!fs.existsSync(sessionDir)) {
        fs.mkdirSync(sessionDir, { recursive: true });
    }
    
    const sessionFile = path.join(sessionDir, `session_${sessionInfo.session_id}.json`);
    
    const sessionData = {
        sessionId: sessionInfo.session_id,
        createdAt: new Date().toISOString(),
        expiresAt: sessionInfo.expires_at,
        isActive: true
    };
    
    fs.writeFileSync(sessionFile, JSON.stringify(sessionData, null, 2));
}
```

## 🔄 重置机器码功能详细分析

### 1. 重置机器码的完整流程

#### 前端触发流程
```javascript
// 用户点击重置机器码按钮
function resetMachineId() {
    // 1. 确认对话框
    if (!confirm('重置机器码将清除所有本地数据，确定继续吗？')) {
        return;
    }
    
    // 2. 显示加载状态
    showStatus('resetStatus', '正在重置机器码...', 'loading');
    
    // 3. 发送重置请求
    vscode.postMessage({
        command: 'resetid',
        timestamp: Date.now()
    });
}
```

#### 扩展后端处理流程
```javascript
async function handleResetMachineId(message) {
    try {
        // 步骤1: 备份当前配置
        await backupCurrentConfiguration();
        
        // 步骤2: 生成新的机器码
        const newMachineId = generateNewMachineId();
        
        // 步骤3: 更新VSCode配置中的机器码
        await updateVSCodeMachineId(newMachineId);
        
        // 步骤4: 清理所有本地数据
        await clearAllLocalData();
        
        // 步骤5: 重置扩展状态
        await resetExtensionState();
        
        // 步骤6: 清理缓存和临时文件
        await clearCacheAndTempFiles();
        
        // 步骤7: 重置网络会话
        await resetNetworkSessions();
        
        // 步骤8: 通知服务器机器码变更
        await notifyServerMachineIdChange(newMachineId);
        
        // 步骤9: 重新初始化扩展
        await reinitializeExtension();
        
        // 步骤10: 通知前端重置成功
        panel.webview.postMessage({
            command: 'resetidResult',
            success: true,
            newMachineId: newMachineId,
            message: '机器码重置成功，请重新登录'
        });
        
        // 步骤11: 重启WebView
        await restartWebView();
        
    } catch (error) {
        // 错误处理和回滚
        await rollbackConfiguration();
        
        panel.webview.postMessage({
            command: 'resetidResult',
            success: false,
            error: error.message
        });
        
        logError('机器码重置失败', error);
    }
}
```

### 2. 重置机器码的具体操作

#### 2.1 生成新机器码
```javascript
function generateNewMachineId() {
    const crypto = require('crypto');
    const os = require('os');
    
    // 方法1: 基于系统信息生成
    const systemInfo = {
        hostname: os.hostname(),
        platform: os.platform(),
        arch: os.arch(),
        cpus: os.cpus().length,
        timestamp: Date.now(),
        random: crypto.randomBytes(16).toString('hex')
    };
    
    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify(systemInfo));
    const machineId = hash.digest('hex').substring(0, 32);
    
    // 方法2: UUID格式
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
    
    // 返回格式化的机器码
    return `${machineId}-${uuid}`;
}
```

#### 2.2 更新VSCode配置
```javascript
async function updateVSCodeMachineId(newMachineId) {
    const vscode = require('vscode');
    const config = vscode.workspace.getConfiguration();
    
    // 更新telemetry.machineId
    await config.update('telemetry.machineId', newMachineId, vscode.ConfigurationTarget.Global);
    
    // 更新扩展专用的机器码
    await config.update('smartshift.machineId', newMachineId, vscode.ConfigurationTarget.Global);
    
    // 记录重置时间
    await config.update('smartshift.machineIdResetAt', new Date().toISOString(), vscode.ConfigurationTarget.Global);
    
    console.log(`机器码已更新为: ${newMachineId}`);
}
```

#### 2.3 清理所有本地数据
```javascript
async function clearAllLocalData() {
    const vscode = require('vscode');
    const fs = require('fs');
    const path = require('path');
    const os = require('os');
    
    // 1. 清理VSCode配置
    const config = vscode.workspace.getConfiguration();
    const keysToRemove = [
        'smartshift.token',
        'smartshift.encryptedAccountData',
        'smartshift.currentAccount',
        'smartshift.currentSession',
        'smartshift.userInfo',
        'smartshift.accountList',
        'smartshift.lastLoginTime'
    ];
    
    for (const key of keysToRemove) {
        await config.update(key, undefined, vscode.ConfigurationTarget.Global);
    }
    
    // 2. 清理本地文件
    const dataDirectories = [
        path.join(os.homedir(), '.smartshift'),
        path.join(os.homedir(), '.vscode', 'smartshift'),
        path.join(os.tmpdir(), 'smartshift')
    ];
    
    for (const dir of dataDirectories) {
        try {
            if (fs.existsSync(dir)) {
                fs.rmSync(dir, { recursive: true, force: true });
                console.log(`已清理目录: ${dir}`);
            }
        } catch (error) {
            console.error(`清理目录失败 ${dir}:`, error);
        }
    }
    
    // 3. 清理状态数据库
    const stateDbPath = path.join(os.homedir(), '.vscode', 'state.vscdb');
    try {
        if (fs.existsSync(stateDbPath)) {
            // 读取数据库，只删除smartshift相关的条目
            const stateData = JSON.parse(fs.readFileSync(stateDbPath, 'utf8'));
            
            Object.keys(stateData).forEach(key => {
                if (key.includes('smartshift') || key.includes('SmartShift')) {
                    delete stateData[key];
                }
            });
            
            fs.writeFileSync(stateDbPath, JSON.stringify(stateData, null, 2));
            console.log('已清理状态数据库中的smartshift数据');
        }
    } catch (error) {
        console.error('清理状态数据库失败:', error);
    }
}
```

#### 2.4 通知服务器机器码变更
```javascript
async function notifyServerMachineIdChange(newMachineId) {
    try {
        // 如果用户已登录，通知服务器机器码变更
        const currentToken = getCurrentToken();
        
        if (currentToken) {
            const response = await fetch('https://api.smartshift.com/api/machine/reset', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${currentToken}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'SmartShift-VSCode/1.1.1'
                },
                body: JSON.stringify({
                    oldMachineId: getOldMachineId(),
                    newMachineId: newMachineId,
                    reason: 'user_reset',
                    timestamp: Date.now()
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                console.log('服务器已确认机器码变更');
            } else {
                console.warn('服务器机器码变更通知失败:', result.message);
            }
        }
    } catch (error) {
        console.error('通知服务器机器码变更失败:', error);
        // 不抛出错误，因为这不是关键操作
    }
}
```

#### 2.5 重新初始化扩展
```javascript
async function reinitializeExtension() {
    // 1. 重置内部状态
    resetInternalState();
    
    // 2. 重新创建状态栏
    createStatusBarItem();
    
    // 3. 重新注册命令
    registerCommands();
    
    // 4. 重新初始化HTTP客户端
    initializeHttpClient();
    
    // 5. 重新创建加密密钥
    generateNewEncryptionKey();
    
    console.log('扩展已重新初始化');
}

function resetInternalState() {
    // 重置所有内部变量
    this.currentUser = null;
    this.currentAccount = null;
    this.currentSession = null;
    this.accountList = [];
    this.isLoggedIn = false;
    this.lastLoginTime = null;
}
```

## 📊 操作对比总结

| 操作类型 | 切换账号 | 重置机器码 |
|----------|----------|------------|
| **影响范围** | 当前账号信息 | 整个扩展状态 |
| **数据保留** | 保留用户登录状态 | 清除所有数据 |
| **需要重新登录** | 否 | 是 |
| **服务器通知** | 切换账号API | 机器码变更API |
| **配置更新** | 账号相关配置 | 所有配置重置 |
| **缓存处理** | 清理账号缓存 | 清理所有缓存 |
| **会话管理** | 更新会话信息 | 重置所有会话 |
| **插件集成** | 更新Augment配置 | 重置插件配置 |
| **风险级别** | 低 | 高 |
| **可回滚性** | 容易 | 困难 |

## 🔍 关键发现

### 切换账号的核心操作：
1. **API调用** - 调用服务器切换账号接口
2. **配置更新** - 更新本地账号配置
3. **插件同步** - 同步Augment插件配置
4. **会话管理** - 建立新的用户会话
5. **状态更新** - 更新UI和状态栏显示

### 重置机器码的核心操作：
1. **生成新ID** - 基于系统信息生成新机器码
2. **全面清理** - 删除所有本地数据和配置
3. **状态重置** - 重置扩展到初始状态
4. **服务器通知** - 通知服务器机器码变更
5. **重新初始化** - 完全重新初始化扩展

**这两个功能都是AI智切扩展的核心功能，切换账号用于账号管理，重置机器码用于解决授权问题或重新绑定设备。**
