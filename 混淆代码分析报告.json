{"timestamp": "2025-08-05T10:16:52.294Z", "fileSize": 213255, "stringArraySize": 738, "analysis": {"obfuscationType": "webpack-obfuscator", "complexity": "High", "techniques": ["String Array Encoding", "Function Name Mangling", "Control Flow Flattening", "Dead Code Injection"]}, "findings": {"cryptoLibraries": ["SHA256", "<PERSON><PERSON><PERSON>"], "networkLibraries": ["axios"], "vscodeAPIs": ["commands", "window", "webview"], "mainFunctions": ["openPanel", "openLogs"]}, "recommendations": ["使用专业的JavaScript反混淆工具", "分析网络请求以了解通信协议", "检查本地存储的加密数据", "监控VSCode API调用"]}