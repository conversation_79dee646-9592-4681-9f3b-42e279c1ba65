/**
 * AI智切扩展智能字符串解码器
 * 尝试多种解码方法来还原混淆的字符串
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class IntelligentStringDecoder {
    constructor() {
        this.encodedStrings = new Map();
        this.decodedStrings = new Map();
        this.decodingMethods = [];
    }

    /**
     * 加载字符串映射表
     */
    loadStringMappings(filePath) {
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            const lines = content.split('\n');
            
            lines.forEach(line => {
                const match = line.match(/^(0x[a-f0-9]+):\s*"(.*)"/);
                if (match) {
                    const [, index, encodedStr] = match;
                    this.encodedStrings.set(index, encodedStr);
                }
            });
            
            console.log(`✅ 加载了 ${this.encodedStrings.size} 个编码字符串`);
            return true;
        } catch (error) {
            console.error('❌ 加载字符串映射失败:', error.message);
            return false;
        }
    }

    /**
     * Base64解码
     */
    tryBase64Decode(str) {
        try {
            const decoded = Buffer.from(str, 'base64').toString('utf8');
            if (this.isValidString(decoded)) {
                return decoded;
            }
        } catch (error) {
            // 忽略错误
        }
        return null;
    }

    /**
     * 自定义Base64解码（可能使用了不同的字符集）
     */
    tryCustomBase64Decode(str) {
        try {
            // 尝试不同的Base64字符集
            const customAlphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
            const standardAlphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
            
            // 如果字符串包含非标准Base64字符，尝试映射
            let mappedStr = str;
            for (let i = 0; i < customAlphabet.length; i++) {
                mappedStr = mappedStr.replace(new RegExp(customAlphabet[i], 'g'), standardAlphabet[i]);
            }
            
            const decoded = Buffer.from(mappedStr, 'base64').toString('utf8');
            if (this.isValidString(decoded)) {
                return decoded;
            }
        } catch (error) {
            // 忽略错误
        }
        return null;
    }

    /**
     * ROT13/Caesar密码解码
     */
    tryRotDecode(str, shift = 13) {
        try {
            let decoded = '';
            for (let i = 0; i < str.length; i++) {
                let char = str[i];
                if (char >= 'A' && char <= 'Z') {
                    decoded += String.fromCharCode((char.charCodeAt(0) - 65 - shift + 26) % 26 + 65);
                } else if (char >= 'a' && char <= 'z') {
                    decoded += String.fromCharCode((char.charCodeAt(0) - 97 - shift + 26) % 26 + 97);
                } else {
                    decoded += char;
                }
            }
            
            if (this.isValidString(decoded)) {
                return decoded;
            }
        } catch (error) {
            // 忽略错误
        }
        return null;
    }

    /**
     * 十六进制解码
     */
    tryHexDecode(str) {
        try {
            if (str.length % 2 === 0 && /^[0-9a-fA-F]+$/.test(str)) {
                const decoded = Buffer.from(str, 'hex').toString('utf8');
                if (this.isValidString(decoded)) {
                    return decoded;
                }
            }
        } catch (error) {
            // 忽略错误
        }
        return null;
    }

    /**
     * URL解码
     */
    tryUrlDecode(str) {
        try {
            const decoded = decodeURIComponent(str);
            if (decoded !== str && this.isValidString(decoded)) {
                return decoded;
            }
        } catch (error) {
            // 忽略错误
        }
        return null;
    }

    /**
     * 反向字符串
     */
    tryReverse(str) {
        const reversed = str.split('').reverse().join('');
        if (this.isValidString(reversed)) {
            return reversed;
        }
        return null;
    }

    /**
     * 尝试识别为JavaScript标识符或关键字
     */
    tryJavaScriptKeywords(str) {
        const jsKeywords = [
            'function', 'var', 'let', 'const', 'if', 'else', 'for', 'while',
            'return', 'true', 'false', 'null', 'undefined', 'this', 'new',
            'typeof', 'instanceof', 'in', 'delete', 'void', 'try', 'catch',
            'finally', 'throw', 'class', 'extends', 'super', 'static',
            'import', 'export', 'default', 'from', 'as', 'async', 'await'
        ];
        
        const lowerStr = str.toLowerCase();
        if (jsKeywords.includes(lowerStr)) {
            return lowerStr;
        }
        
        return null;
    }

    /**
     * 检查字符串是否有效（包含可读字符）
     */
    isValidString(str) {
        if (!str || str.length === 0) return false;
        
        // 检查是否包含可打印字符
        const printableRatio = (str.match(/[\x20-\x7E]/g) || []).length / str.length;
        
        // 检查是否包含常见的编程关键字或模式
        const patterns = [
            /vscode/i, /window/i, /document/i, /console/i, /function/i,
            /require/i, /module/i, /exports/i, /import/i, /export/i,
            /axios/i, /crypto/i, /hash/i, /encrypt/i, /decrypt/i,
            /panel/i, /webview/i, /command/i, /register/i, /activate/i,
            /smartshift/i, /manager/i, /account/i, /login/i, /auth/i
        ];
        
        const hasKeywords = patterns.some(pattern => pattern.test(str));
        
        return printableRatio > 0.7 || hasKeywords || str.length < 10;
    }

    /**
     * 尝试所有解码方法
     */
    tryAllDecodingMethods(encodedStr) {
        const methods = [
            { name: 'Base64', method: this.tryBase64Decode.bind(this) },
            { name: 'Custom Base64', method: this.tryCustomBase64Decode.bind(this) },
            { name: 'Hex', method: this.tryHexDecode.bind(this) },
            { name: 'URL', method: this.tryUrlDecode.bind(this) },
            { name: 'ROT13', method: (str) => this.tryRotDecode(str, 13) },
            { name: 'ROT1', method: (str) => this.tryRotDecode(str, 1) },
            { name: 'ROT25', method: (str) => this.tryRotDecode(str, 25) },
            { name: 'Reverse', method: this.tryReverse.bind(this) },
            { name: 'JS Keywords', method: this.tryJavaScriptKeywords.bind(this) }
        ];

        for (const { name, method } of methods) {
            const result = method(encodedStr);
            if (result) {
                return { decoded: result, method: name };
            }
        }

        return null;
    }

    /**
     * 解码所有字符串
     */
    decodeAllStrings() {
        console.log('\n🔍 开始智能解码...');
        
        let successCount = 0;
        let methodStats = {};

        for (const [index, encodedStr] of this.encodedStrings) {
            const result = this.tryAllDecodingMethods(encodedStr);
            
            if (result) {
                this.decodedStrings.set(index, {
                    original: encodedStr,
                    decoded: result.decoded,
                    method: result.method
                });
                
                methodStats[result.method] = (methodStats[result.method] || 0) + 1;
                successCount++;
                
                // 显示前20个成功解码的字符串
                if (successCount <= 20) {
                    console.log(`  [${index}] ${result.method}: "${encodedStr}" -> "${result.decoded}"`);
                }
            }
        }

        console.log(`\n📊 解码统计:`);
        console.log(`  成功解码: ${successCount}/${this.encodedStrings.size}`);
        console.log(`  解码方法统计:`);
        Object.entries(methodStats).forEach(([method, count]) => {
            console.log(`    ${method}: ${count} 个`);
        });

        return successCount;
    }

    /**
     * 查找关键字符串
     */
    findKeyStrings() {
        console.log('\n🔍 查找关键字符串...');
        
        const keyPatterns = [
            { name: 'VSCode API', pattern: /vscode|window|document|console/i },
            { name: '扩展相关', pattern: /smartshift|manager|panel|webview|command/i },
            { name: '加密相关', pattern: /crypto|hash|encrypt|decrypt|sha|fernet/i },
            { name: '网络相关', pattern: /axios|http|request|fetch|api/i },
            { name: '认证相关', pattern: /auth|login|token|key|account/i },
            { name: 'JavaScript', pattern: /function|require|module|exports|import/i }
        ];

        const foundKeys = {};
        keyPatterns.forEach(({ name }) => foundKeys[name] = []);

        for (const [index, data] of this.decodedStrings) {
            const str = data.decoded;
            
            keyPatterns.forEach(({ name, pattern }) => {
                if (pattern.test(str)) {
                    foundKeys[name].push({
                        index,
                        original: data.original,
                        decoded: str,
                        method: data.method
                    });
                }
            });
        }

        Object.entries(foundKeys).forEach(([category, items]) => {
            if (items.length > 0) {
                console.log(`\n📦 ${category} (${items.length} 个):`);
                items.slice(0, 5).forEach(item => {
                    console.log(`  [${item.index}] "${item.decoded}" (${item.method})`);
                });
                if (items.length > 5) {
                    console.log(`  ... 还有 ${items.length - 5} 个`);
                }
            }
        });

        return foundKeys;
    }

    /**
     * 生成解码报告
     */
    generateReport() {
        console.log('\n📋 生成解码报告...');
        
        const keyStrings = this.findKeyStrings();
        
        const report = {
            timestamp: new Date().toISOString(),
            totalStrings: this.encodedStrings.size,
            decodedStrings: this.decodedStrings.size,
            decodingRate: (this.decodedStrings.size / this.encodedStrings.size * 100).toFixed(2) + '%',
            keyStrings: keyStrings,
            allDecodedStrings: Array.from(this.decodedStrings.entries()).map(([index, data]) => ({
                index,
                original: data.original,
                decoded: data.decoded,
                method: data.method
            }))
        };

        // 保存解码报告
        const reportPath = path.join(__dirname, '智能解码报告.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
        
        // 生成可读的解码结果
        const decodedPath = path.join(__dirname, '解码结果.txt');
        let content = '# AI智切扩展字符串解码结果\n\n';
        
        for (const [index, data] of this.decodedStrings) {
            content += `${index}: "${data.decoded}" (${data.method}) [原文: "${data.original}"]\n`;
        }
        
        fs.writeFileSync(decodedPath, content, 'utf8');
        
        console.log(`📄 解码报告已保存到: ${reportPath}`);
        console.log(`📄 解码结果已保存到: ${decodedPath}`);
        
        return report;
    }

    /**
     * 执行完整的解码流程
     */
    decode(mappingFilePath) {
        console.log('🚀 开始智能字符串解码...\n');
        
        if (!this.loadStringMappings(mappingFilePath)) {
            return false;
        }

        const decodedCount = this.decodeAllStrings();
        
        if (decodedCount > 0) {
            const report = this.generateReport();
            
            console.log('\n✅ 智能解码完成!');
            console.log(`\n📊 解码摘要:`);
            console.log(`  总字符串: ${this.encodedStrings.size}`);
            console.log(`  成功解码: ${decodedCount}`);
            console.log(`  解码率: ${report.decodingRate}`);
            
            return true;
        } else {
            console.log('❌ 未能解码任何字符串');
            return false;
        }
    }
}

// 使用示例
if (require.main === module) {
    const decoder = new IntelligentStringDecoder();
    decoder.decode('./字符串映射表.txt');
}

module.exports = IntelligentStringDecoder;
