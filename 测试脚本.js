/**
 * AI智切扩展功能测试脚本
 * 用于测试扩展的各项功能和安全性
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class ExtensionTester {
    constructor() {
        this.testResults = [];
        this.extensionPath = './extension';
        this.packageJson = null;
    }

    /**
     * 记录测试结果
     */
    logTest(testName, status, message, details = null) {
        const result = {
            test: testName,
            status: status, // 'PASS', 'FAIL', 'WARN', 'INFO'
            message: message,
            details: details,
            timestamp: new Date().toISOString()
        };
        
        this.testResults.push(result);
        
        const statusIcon = {
            'PASS': '✅',
            'FAIL': '❌',
            'WARN': '⚠️',
            'INFO': 'ℹ️'
        };
        
        console.log(`${statusIcon[status]} ${testName}: ${message}`);
        if (details) {
            console.log(`   详情: ${JSON.stringify(details, null, 2)}`);
        }
    }

    /**
     * 测试文件结构完整性
     */
    testFileStructure() {
        console.log('\n🔍 测试文件结构完整性...');
        
        const requiredFiles = [
            'package.json',
            'dist/extension.js',
            'resources/logo.png',
            'LICENSE.md',
            'README.md'
        ];

        requiredFiles.forEach(file => {
            const filePath = path.join(this.extensionPath, file);
            if (fs.existsSync(filePath)) {
                const stats = fs.statSync(filePath);
                this.logTest(
                    `文件存在性检查: ${file}`,
                    'PASS',
                    `文件存在，大小: ${stats.size} bytes`
                );
            } else {
                this.logTest(
                    `文件存在性检查: ${file}`,
                    'FAIL',
                    '文件不存在'
                );
            }
        });
    }

    /**
     * 测试package.json配置
     */
    testPackageJson() {
        console.log('\n🔍 测试package.json配置...');
        
        try {
            const packagePath = path.join(this.extensionPath, 'package.json');
            this.packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            
            // 检查必要字段
            const requiredFields = ['name', 'version', 'engines', 'main', 'contributes'];
            requiredFields.forEach(field => {
                if (this.packageJson[field]) {
                    this.logTest(
                        `package.json字段: ${field}`,
                        'PASS',
                        `字段存在: ${JSON.stringify(this.packageJson[field])}`
                    );
                } else {
                    this.logTest(
                        `package.json字段: ${field}`,
                        'FAIL',
                        '必要字段缺失'
                    );
                }
            });

            // 检查依赖项
            if (this.packageJson.dependencies) {
                this.logTest(
                    'package.json依赖项',
                    'INFO',
                    `发现 ${Object.keys(this.packageJson.dependencies).length} 个依赖项`,
                    this.packageJson.dependencies
                );
            }

            // 检查命令配置
            if (this.packageJson.contributes && this.packageJson.contributes.commands) {
                this.logTest(
                    'VSCode命令配置',
                    'PASS',
                    `配置了 ${this.packageJson.contributes.commands.length} 个命令`,
                    this.packageJson.contributes.commands
                );
            }

        } catch (error) {
            this.logTest(
                'package.json解析',
                'FAIL',
                `解析失败: ${error.message}`
            );
        }
    }

    /**
     * 测试扩展代码安全性
     */
    testCodeSecurity() {
        console.log('\n🔍 测试代码安全性...');
        
        try {
            const codePath = path.join(this.extensionPath, 'dist/extension.js');
            const code = fs.readFileSync(codePath, 'utf8');
            
            // 检查危险函数调用
            const dangerousPatterns = [
                { pattern: /eval\s*\(/g, risk: 'HIGH', desc: '动态代码执行' },
                { pattern: /Function\s*\(/g, risk: 'HIGH', desc: '动态函数创建' },
                { pattern: /child_process/g, risk: 'HIGH', desc: '子进程执行' },
                { pattern: /exec\s*\(/g, risk: 'HIGH', desc: '命令执行' },
                { pattern: /spawn\s*\(/g, risk: 'MEDIUM', desc: '进程启动' },
                { pattern: /require\s*\(\s*['"]fs['"]\s*\)/g, risk: 'MEDIUM', desc: '文件系统访问' },
                { pattern: /XMLHttpRequest|fetch|axios/g, risk: 'MEDIUM', desc: '网络请求' }
            ];

            dangerousPatterns.forEach(({ pattern, risk, desc }) => {
                const matches = [...code.matchAll(pattern)];
                if (matches.length > 0) {
                    this.logTest(
                        `安全检查: ${desc}`,
                        risk === 'HIGH' ? 'FAIL' : 'WARN',
                        `发现 ${matches.length} 个匹配项，风险级别: ${risk}`
                    );
                } else {
                    this.logTest(
                        `安全检查: ${desc}`,
                        'PASS',
                        '未发现风险'
                    );
                }
            });

            // 检查混淆程度
            const obfuscationIndicators = [
                /function\s+[a-zA-Z_$][a-zA-Z0-9_$]*\s*\([^)]*\)\s*{[^}]*}/g,
                /var\s+_0x[a-f0-9]+/g,
                /'[^']*'/g
            ];

            let obfuscationScore = 0;
            obfuscationIndicators.forEach(pattern => {
                const matches = [...code.matchAll(pattern)];
                obfuscationScore += matches.length;
            });

            this.logTest(
                '代码混淆程度',
                'INFO',
                `混淆评分: ${obfuscationScore}，${obfuscationScore > 1000 ? '高度混淆' : '轻度混淆'}`
            );

        } catch (error) {
            this.logTest(
                '代码安全性检查',
                'FAIL',
                `检查失败: ${error.message}`
            );
        }
    }

    /**
     * 测试网络通信模拟
     */
    testNetworkCommunication() {
        console.log('\n🔍 测试网络通信模拟...');
        
        // 模拟网络请求测试
        const mockEndpoints = [
            'https://api.example.com/auth',
            'https://api.example.com/accounts',
            'https://api.example.com/switch'
        ];

        mockEndpoints.forEach(endpoint => {
            // 这里只是模拟，实际不发送请求
            this.logTest(
                `网络端点测试: ${endpoint}`,
                'INFO',
                '模拟测试 - 未实际发送请求'
            );
        });

        // 检查可能的API密钥或令牌
        if (this.packageJson && this.packageJson.dependencies) {
            const networkLibs = ['axios', 'request', 'node-fetch', 'http'];
            const foundNetworkLibs = networkLibs.filter(lib => 
                this.packageJson.dependencies[lib] || 
                (this.packageJson.devDependencies && this.packageJson.devDependencies[lib])
            );

            if (foundNetworkLibs.length > 0) {
                this.logTest(
                    '网络库检测',
                    'WARN',
                    `发现网络库: ${foundNetworkLibs.join(', ')}`
                );
            }
        }
    }

    /**
     * 测试加密功能
     */
    testEncryption() {
        console.log('\n🔍 测试加密功能...');
        
        // 模拟Fernet加密测试
        const testData = 'test-data-for-encryption';
        const testKey = crypto.randomBytes(32);
        
        try {
            // 使用Node.js内置crypto模拟加密
            const cipher = crypto.createCipher('aes256', testKey);
            let encrypted = cipher.update(testData, 'utf8', 'hex');
            encrypted += cipher.final('hex');
            
            const decipher = crypto.createDecipher('aes256', testKey);
            let decrypted = decipher.update(encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            
            if (decrypted === testData) {
                this.logTest(
                    '加密功能测试',
                    'PASS',
                    '加密/解密测试成功'
                );
            } else {
                this.logTest(
                    '加密功能测试',
                    'FAIL',
                    '加密/解密测试失败'
                );
            }
        } catch (error) {
            this.logTest(
                '加密功能测试',
                'FAIL',
                `加密测试失败: ${error.message}`
            );
        }

        // 检查是否使用了Fernet库
        if (this.packageJson && this.packageJson.dependencies && this.packageJson.dependencies.fernet) {
            this.logTest(
                'Fernet加密库',
                'INFO',
                `Fernet版本: ${this.packageJson.dependencies.fernet}`
            );
        }
    }

    /**
     * 生成测试报告
     */
    generateTestReport() {
        console.log('\n📋 生成测试报告...');
        
        const summary = {
            totalTests: this.testResults.length,
            passed: this.testResults.filter(r => r.status === 'PASS').length,
            failed: this.testResults.filter(r => r.status === 'FAIL').length,
            warnings: this.testResults.filter(r => r.status === 'WARN').length,
            info: this.testResults.filter(r => r.status === 'INFO').length
        };

        const report = {
            timestamp: new Date().toISOString(),
            summary: summary,
            results: this.testResults,
            recommendations: [
                '定期更新依赖项以修复安全漏洞',
                '实施代码签名以确保完整性',
                '添加更多的单元测试覆盖',
                '考虑使用更安全的加密实现',
                '监控网络通信以检测异常行为'
            ]
        };

        const reportPath = path.join(__dirname, '扩展测试报告.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
        
        console.log(`📄 测试报告已保存到: ${reportPath}`);
        
        // 打印摘要
        console.log('\n📊 测试摘要:');
        console.log(`  总测试数: ${summary.totalTests}`);
        console.log(`  通过: ${summary.passed}`);
        console.log(`  失败: ${summary.failed}`);
        console.log(`  警告: ${summary.warnings}`);
        console.log(`  信息: ${summary.info}`);
        
        return report;
    }

    /**
     * 运行所有测试
     */
    runAllTests() {
        console.log('🚀 开始扩展功能测试...\n');
        
        this.testFileStructure();
        this.testPackageJson();
        this.testCodeSecurity();
        this.testNetworkCommunication();
        this.testEncryption();
        
        const report = this.generateTestReport();
        
        console.log('\n✅ 所有测试完成!');
        
        return report;
    }
}

// 使用示例
if (require.main === module) {
    const tester = new ExtensionTester();
    tester.runAllTests();
}

module.exports = ExtensionTester;
