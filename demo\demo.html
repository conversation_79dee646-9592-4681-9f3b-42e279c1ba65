<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智切扩展演示</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 30px 0; }
        .feature-card { padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #fafafa; }
        .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .demo-button { background: #007acc; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        .demo-button:hover { background: #005a9e; }
        .log-area { background: #2d2d30; color: #cccccc; padding: 15px; border-radius: 5px; font-family: 'Courier New', monospace; height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 AI智切扩展演示</h1>
            <p>VSCode扩展功能演示和测试环境</p>
        </div>

        <div class="status success">
            <strong>✅ 演示环境已就绪</strong> - 所有功能均为模拟实现，安全可靠
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h3>🔐 用户认证</h3>
                <p>模拟激活码验证和用户认证流程</p>
                <button class="demo-button" onclick="demoAuth()">演示认证</button>
            </div>

            <div class="feature-card">
                <h3>👥 账号管理</h3>
                <p>展示账号列表获取和切换功能</p>
                <button class="demo-button" onclick="demoAccounts()">演示账号管理</button>
            </div>

            <div class="feature-card">
                <h3>🌐 网络通信</h3>
                <p>模拟与服务器的通信过程</p>
                <button class="demo-button" onclick="demoNetwork()">演示网络请求</button>
            </div>

            <div class="feature-card">
                <h3>🔒 数据加密</h3>
                <p>展示数据加密和解密功能</p>
                <button class="demo-button" onclick="demoEncryption()">演示加密</button>
            </div>
        </div>

        <div style="margin-top: 30px;">
            <h3>📋 演示日志</h3>
            <div id="logArea" class="log-area">
                [2025-08-05T10:49:49.212Z] 演示环境初始化完成\n
                [2025-08-05T10:49:49.212Z] 等待用户操作...\n
            </div>
        </div>

        <div style="margin-top: 20px; text-align: center;">
            <button class="demo-button" onclick="clearLogs()">清空日志</button>
            <button class="demo-button" onclick="runAllTests()">运行所有测试</button>
        </div>
    </div>

    <script>
        function log(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toISOString();
            logArea.innerHTML += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function demoAuth() {
            log('🔐 开始用户认证演示...');
            setTimeout(() => log('📤 发送认证请求: POST /api/auth'), 500);
            setTimeout(() => log('📥 收到认证响应: 200 OK'), 1000);
            setTimeout(() => log('✅ 认证成功，获得访问令牌'), 1500);
        }

        function demoAccounts() {
            log('👥 开始账号管理演示...');
            setTimeout(() => log('📤 请求账号列表: GET /api/accounts'), 500);
            setTimeout(() => log('📥 收到账号数据: 3个可用账号'), 1000);
            setTimeout(() => log('🔄 切换到账号: <EMAIL>'), 1500);
            setTimeout(() => log('✅ 账号切换成功'), 2000);
        }

        function demoNetwork() {
            log('🌐 开始网络通信演示...');
            setTimeout(() => log('🔗 建立安全连接: HTTPS'), 500);
            setTimeout(() => log('📡 发送请求头验证'), 1000);
            setTimeout(() => log('🔐 应用请求签名'), 1500);
            setTimeout(() => log('✅ 网络通信正常'), 2000);
        }

        function demoEncryption() {
            log('🔒 开始数据加密演示...');
            setTimeout(() => log('🔑 生成加密密钥: AES-256'), 500);
            setTimeout(() => log('📝 加密敏感数据'), 1000);
            setTimeout(() => log('💾 安全存储到本地'), 1500);
            setTimeout(() => log('🔓 验证解密功能'), 2000);
            setTimeout(() => log('✅ 加密功能正常'), 2500);
        }

        function clearLogs() {
            document.getElementById('logArea').innerHTML = '';
            log('日志已清空');
        }

        function runAllTests() {
            log('🚀 开始运行所有演示测试...');
            demoAuth();
            setTimeout(demoAccounts, 3000);
            setTimeout(demoNetwork, 6000);
            setTimeout(demoEncryption, 9000);
            setTimeout(() => log('🎉 所有演示测试完成！'), 12000);
        }
    </script>
</body>
</html>