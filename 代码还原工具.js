/**
 * AI智切扩展代码还原工具
 * 专业的JavaScript混淆代码还原分析工具
 */

const fs = require('fs');
const path = require('path');

class CodeDeobfuscator {
    constructor(filePath) {
        this.filePath = filePath;
        this.obfuscatedCode = '';
        this.stringArray = [];
        this.decodedStrings = new Map();
        this.functionMappings = new Map();
        this.restoredCode = '';
    }

    /**
     * 加载混淆代码
     */
    loadCode() {
        try {
            this.obfuscatedCode = fs.readFileSync(this.filePath, 'utf8');
            console.log('✅ 混淆代码加载成功');
            return true;
        } catch (error) {
            console.error('❌ 加载失败:', error.message);
            return false;
        }
    }

    /**
     * 提取字符串数组
     */
    extractStringArray() {
        console.log('\n🔍 提取字符串数组...');
        
        // 查找字符串数组定义
        const arrayPattern = /var\s+_0x[a-f0-9]+\s*=\s*\[(.*?)\]/s;
        const match = this.obfuscatedCode.match(arrayPattern);
        
        if (match) {
            const arrayContent = match[1];
            // 提取所有字符串
            const stringMatches = arrayContent.match(/'([^'\\]|\\.)*'/g) || [];
            this.stringArray = stringMatches.map(s => {
                // 移除引号并解码
                const str = s.slice(1, -1);
                return this.decodeString(str);
            });
            
            console.log(`📊 提取到 ${this.stringArray.length} 个字符串`);
            return true;
        }
        
        console.log('⚠️ 未找到字符串数组');
        return false;
    }

    /**
     * 解码字符串
     */
    decodeString(encodedStr) {
        try {
            // 尝试Base64解码
            if (this.isBase64(encodedStr)) {
                return Buffer.from(encodedStr, 'base64').toString('utf8');
            }
            
            // 尝试URL解码
            if (encodedStr.includes('%')) {
                return decodeURIComponent(encodedStr);
            }
            
            // 返回原字符串
            return encodedStr;
        } catch (error) {
            return encodedStr;
        }
    }

    /**
     * 检查是否为Base64编码
     */
    isBase64(str) {
        try {
            return Buffer.from(str, 'base64').toString('base64') === str;
        } catch (error) {
            return false;
        }
    }

    /**
     * 分析解码函数
     */
    analyzeDecoderFunction() {
        console.log('\n🔍 分析解码函数...');
        
        // 查找主解码函数
        const decoderPattern = /function\s+(a0_0x[a-f0-9]+)\s*\([^)]*\)\s*{[^}]*return[^}]*}/;
        const match = this.obfuscatedCode.match(decoderPattern);
        
        if (match) {
            const funcName = match[1];
            console.log(`📊 找到主解码函数: ${funcName}`);
            
            // 分析函数内部逻辑
            this.analyzeDecoderLogic(match[0]);
            return funcName;
        }
        
        return null;
    }

    /**
     * 分析解码逻辑
     */
    analyzeDecoderLogic(funcCode) {
        console.log('🔍 分析解码逻辑...');
        
        // 检查Base64解码特征
        if (funcCode.includes('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=')) {
            console.log('  🔑 检测到Base64解码算法');
        }
        
        // 检查字符编码转换
        if (funcCode.includes('fromCharCode') || funcCode.includes('charCodeAt')) {
            console.log('  🔑 检测到字符编码转换');
        }
        
        // 检查URI解码
        if (funcCode.includes('decodeURIComponent')) {
            console.log('  🔑 检测到URI解码');
        }
    }

    /**
     * 还原字符串引用
     */
    restoreStringReferences() {
        console.log('\n🔍 还原字符串引用...');
        
        let restoredCode = this.obfuscatedCode;
        let replacements = 0;
        
        // 查找字符串引用模式 _0x53e5(0x123)
        const refPattern = /a0_0x53e5\(0x([a-f0-9]+)\)/g;
        
        restoredCode = restoredCode.replace(refPattern, (match, hexIndex) => {
            const index = parseInt(hexIndex, 16) - 0x7e; // 减去偏移量
            
            if (index >= 0 && index < this.stringArray.length) {
                const decodedString = this.stringArray[index];
                replacements++;
                
                // 如果是关键字符串，直接替换
                if (this.isKeyString(decodedString)) {
                    return `"${decodedString}"`;
                }
                
                // 其他字符串添加注释
                return `"${decodedString}" /* ${match} */`;
            }
            
            return match;
        });
        
        console.log(`📊 还原了 ${replacements} 个字符串引用`);
        this.restoredCode = restoredCode;
        return replacements > 0;
    }

    /**
     * 检查是否为关键字符串
     */
    isKeyString(str) {
        const keyStrings = [
            'vscode', 'window', 'document', 'console', 'exports',
            'smartshift-manager', 'openPanel', 'openLogs',
            'axios', 'fernet', 'crypto', 'SHA256',
            'activate', 'deactivate', 'commands', 'registerCommand',
            'createWebviewPanel', 'showInformationMessage', 'showErrorMessage'
        ];
        
        return keyStrings.some(key => str.toLowerCase().includes(key.toLowerCase()));
    }

    /**
     * 美化代码格式
     */
    beautifyCode() {
        console.log('\n🔍 美化代码格式...');
        
        let code = this.restoredCode;
        
        // 添加换行符
        code = code.replace(/;/g, ';\n');
        code = code.replace(/{/g, '{\n');
        code = code.replace(/}/g, '\n}\n');
        
        // 简单的缩进处理
        const lines = code.split('\n');
        let indentLevel = 0;
        const indentedLines = lines.map(line => {
            const trimmed = line.trim();
            if (!trimmed) return '';
            
            if (trimmed.includes('}')) indentLevel--;
            const indented = '  '.repeat(Math.max(0, indentLevel)) + trimmed;
            if (trimmed.includes('{')) indentLevel++;
            
            return indented;
        });
        
        this.restoredCode = indentedLines.join('\n');
        console.log('📊 代码格式美化完成');
    }

    /**
     * 识别关键功能模块
     */
    identifyModules() {
        console.log('\n🔍 识别功能模块...');
        
        const modules = {
            'VSCode扩展API': /vscode|commands|window|workspace/gi,
            '加密功能': /crypto|encrypt|decrypt|hash|sha256|fernet/gi,
            '网络请求': /axios|http|request|fetch|XMLHttpRequest/gi,
            '文件操作': /fs\.|readFile|writeFile|path\./gi,
            '配置管理': /config|settings|getConfiguration/gi,
            '面板管理': /webview|panel|html|createWebviewPanel/gi,
            '状态栏': /statusBar|createStatusBarItem/gi,
            '命令处理': /registerCommand|executeCommand/gi
        };

        const foundModules = {};
        Object.entries(modules).forEach(([name, pattern]) => {
            const matches = [...this.restoredCode.matchAll(pattern)];
            if (matches.length > 0) {
                foundModules[name] = matches.length;
                console.log(`📦 ${name}: ${matches.length} 个引用`);
            }
        });

        return foundModules;
    }

    /**
     * 生成还原报告
     */
    generateReport() {
        console.log('\n📋 生成还原报告...');
        
        const report = {
            timestamp: new Date().toISOString(),
            originalSize: this.obfuscatedCode.length,
            restoredSize: this.restoredCode.length,
            stringArraySize: this.stringArray.length,
            analysis: {
                obfuscationType: 'webpack-obfuscator',
                decodingMethods: ['Base64', 'URI解码', '字符编码转换'],
                restorationSuccess: this.restoredCode.length > this.obfuscatedCode.length
            },
            extractedStrings: this.stringArray.slice(0, 20), // 前20个字符串示例
            modules: this.identifyModules()
        };

        // 保存还原后的代码
        const restoredPath = path.join(__dirname, 'extension_restored.js');
        fs.writeFileSync(restoredPath, this.restoredCode, 'utf8');
        
        // 保存报告
        const reportPath = path.join(__dirname, '代码还原报告.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
        
        console.log(`📄 还原代码已保存到: ${restoredPath}`);
        console.log(`📄 还原报告已保存到: ${reportPath}`);
        
        return report;
    }

    /**
     * 执行完整的还原流程
     */
    restore() {
        console.log('🚀 开始代码还原...\n');
        
        if (!this.loadCode()) {
            return false;
        }

        // 1. 提取字符串数组
        this.extractStringArray();

        // 2. 分析解码函数
        this.analyzeDecoderFunction();

        // 3. 还原字符串引用
        this.restoreStringReferences();

        // 4. 美化代码格式
        this.beautifyCode();

        // 5. 生成报告
        const report = this.generateReport();

        console.log('\n✅ 代码还原完成!');
        console.log('\n📊 还原摘要:');
        console.log(`  原始大小: ${(this.obfuscatedCode.length / 1024).toFixed(2)} KB`);
        console.log(`  还原大小: ${(this.restoredCode.length / 1024).toFixed(2)} KB`);
        console.log(`  字符串数组: ${this.stringArray.length} 个条目`);
        console.log(`  功能模块: ${Object.keys(report.modules).length} 个`);

        return true;
    }
}

// 使用示例
if (require.main === module) {
    const deobfuscator = new CodeDeobfuscator('./extension/dist/extension.js');
    deobfuscator.restore();
}

module.exports = CodeDeobfuscator;
