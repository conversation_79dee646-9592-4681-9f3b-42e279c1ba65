{"timestamp": "2025-08-05T10:28:39.073Z", "originalSize": 213255, "restoredSize": 213255, "decodedStringsCount": 738, "analysis": {"obfuscationType": "webpack-obfuscator", "decodingSuccess": true, "restorationQuality": "High"}, "vscodeAPIs": [], "cryptoFunctions": [{"pattern": "SHA256", "count": 1}, {"pattern": "encrypt", "count": 13}, {"pattern": "decrypt", "count": 10}, {"pattern": "hash", "count": 14}], "networkCalls": [], "keyStrings": [], "decodedStrings": [["0x7e", "C2HVD1rLEhreB2n1BwvUDa"], ["0x7f", "y2LWAgvYDgv4Da"], ["0x80", "w29IAMvJDcbhzw5LCMf0B3jD"], ["0x81", "x2zPBMrbBMrqCM9JzxnZu3rHDgveyKzPBgvZ"], ["0x82", "z2XVyMfSu3rVCMfNzq"], ["0x83", "x2LUDM9Rzq"], ["0x84", "5l+U5Ps55P2d6zMq5AsX6lsLoIa"], ["0x85", "DvfguK0"], ["0x86", "DuLrBvu"], ["0x87", "DgLTzq"], ["0x88", "ANLYuKO"], ["0x89", "y29TChv0zq"], ["0x8a", "uhPQCfO"], ["0x8b", "uKHpwgS"], ["0x8c", "zxHPC3rZu3LUyW"], ["0x8d", "yMLUza"], ["0x8e", "CgvYzM9YBvvWBg9HzcGKmsL7CMv0DxjUifbYB21PC2uUCMvZB2X2zsGPoW"], ["0x8f", "r3P5vMW"], ["0x90", "lL9HDxrOu2vZC2LVBI5ZyxzLu2vZC2LVBI5HChbSEsG"], ["0x91", "ugf0AcbKB2vZig5VDcbLEgLZDdOG"], ["0x92", "zgvJCNLWDe1LC3nHz2u"], ["0x93", "5PEL5B+x5PAh5lU25lIn5A2y5zYO"], ["0x94", "AgfZt3DUuhjVCgvYDhK"], ["0x95", "6ycc6ywn5A6m5OIq77Yb"], ["0x96", "C3vIC2nYAxb0Aw9UCW"], ["0x97", "Bg9NBY5WBMC"], ["0x98", "Dg9tDhjPBMDuywC"], ["0x99", "BgvUz3rO"], ["0x9a", "uxrpCfa"], ["0x9b", "yxvNBwvUDe1HBMfNzxjby3rPDMf0Aw9Uq29Kzq"], ["0x9c", "x3vWzgf0zvn0B3jHz2vkC29U"], ["0x9d", "6i635y+w5A2y5ykO5l2n572U5AsX6lsLoIa"], ["0x9e", "x2rVq3j5ChrcBg9JAW"], ["0x9f", "Dg9mB3DLCKnHC2u"], ["0xa0", "5P2d6zMq5lIn6lAZ77Ym6k+35lUL566H55cg5zgy6lQR5lU96l+q6kgmvLndB2rL"], ["0xa1", "5B2t5yMn6lsM5y+3oIa"], ["0xa2", "yKzlv1y"], ["0xa3", "y2HTB2rtEw5J"], ["0xa4", "mZu1ndm0m2zJz0XhrG"], ["0xa5", "C2v0svy"], ["0xa6", "zgvJB2rLnJr0B0HLEa"], ["0xa7", "z2v0uhjVDg90ExbLt2y"], ["0xa8", "z2v0rxH0zw5ZAw9U"], ["0xa9", "u2vJCMv0ig5VDcbZzxq"], ["0xaa", "vxrMoa"], ["0xab", "zgLZCgXHEu5HBwu"], ["0xac", "ios4QUMHUEEBRIWG5AsX6lsLia"], ["0xad", "C3LTyM9S"], ["0xae", "B21rr08"], ["0xaf", "zw1HAwW"], ["0xb0", "qNvMzMvYzwrcBg9JA0fSz29YAxrOBq"], ["0xb1", "y3jLyxrLrw5JCNLWDg9Y"], ["0xb2", "AwfuCgC"], ["0xb3", "BwvKAwe"], ["0xb4", "x2nYzwf0zuHLBhbLCG"], ["0xb5", "D3jPDgvgAwXL"], ["0xb6", "t3bLBLntta"], ["0xb7", "5BEY5l+U5AsnigfNzw50rwrPDezPBgu"], ["0xb8", "5lUo5A2y5ykO5OgI5Asn55sO5OI35l+H5OgV"], ["0xb9", "Dw5KzwzPBMvK"], ["0xba", "sw52ywXPzcbuB2TLBJOGvfrm"], ["0xbb", "tezWwLe"], ["0xbc", "Agv4"], ["0xbd", "Dg9tDhjPBMC"], ["0xbe", "CM91BMq"], ["0xbf", "swLPufq"], ["0xc0", "cIaGicaGicaGcIaGicaGicaGpgrPDIbJBgfZCZ0IC2vJDgLVBIi+cIaGicaGicaGicaGidXOmJ5bDwDTzw505O+s5lU25Qoa5P+Lpc9OmJ4kicaGicaGicaGicaGpgj1DhrVBIbVBMnSAwnRpsjJAgvJA0f1z21LBNrqBhvNAw4Oksi+5Qoa5P+Lpc9IDxr0B24+cIaGicaGicaGicaGidXIDxr0B24GB25JBgLJAZ0IB3bLBLDLyNnPDguOksi+5l2/55sO6k+05PIopc9IDxr0B24+cGOGicaGicaGicaGica8zgL2igLKpsjWBhvNAw5tDgf0DxmIignSyxnZpsjZDgf0DxmIihn0EwXLpsjKAxnWBgf5oIbUB25LoYi+pc9KAxy+cIaGicaGicaGpc9KAxy+cGOGicaGicaGidXKAxyGy2XHC3m9iNnLy3rPB24IpGOGicaGicaGicaGica8Adi+5R+a5Rs756cb5QcH6AQmpc9OmJ4kicaGicaGicaGicaGpgrPDIbJBgfZCZ0IAw5WDxqTz3jVDxaIpGOGicaGicaGicaGicaGicaGpgXHyMvSigzVCJ0Iywn0AxzHDgLVBKnVzguIpUA/GoA0U+EGGtO8l2XHyMvSpGOGicaGicaGicaGicaGicaGpgLUChv0ihr5Cgu9iNrLEhqIigLKpsjHy3rPDMf0Aw9Uq29KzsiGCgXHy2vOB2XKzxi9iUIVT+I+K+wfPEs9OoEAHoA/GoA0U+EGGsiGDMfSDwu9iG"], ["0xc1", "C3rVCMfNzs5QC29U"], ["0xc2", "lMnVBw1HBMrZlNjLz2LZDgvYq29TBwfUzcGIDNnJB2rLlwf1z21LBNqUzgLYzwn0tg9NAw4Ilgz1BMn0Aw9UkcL7"], ["0xc3", "jcHLCNjVCIKG5O+s5lU25Qoa5P+L5AsX6lsL"], ["0xc4", "C3rVCMfNzuPZB25qyxrOoIa"], ["0xc5", "jcHZEw5JFNnWAw4PioEzU+w9LEs4Rs4UlG"], ["0xc6", "5OM+5yIWia"], ["0xc7", "sMPhy0C"], ["0xc8", "Cg9ZDe1LC3nHz2u"], ["0xc9", "D2vIDMLLD0XVywrLza"], ["0xca", "C3LbrK0"], ["0xcb", "C3rHy2S"], ["0xcc", "ruPOzK0"], ["0xcd", "sK9ps1G"], ["0xce", "C3bSAxq"], ["0xcf", "x2nOzwnRqxvNBwvUDfbSDwDPBG"], ["0xd0", "DgvZDa"], ["0xd1", "zgLNzxn0"], ["0xd2", "ELzOqwC"], ["0xd3", "CgfYC2vizxG"], ["0xd4", "DxrMoa"], ["0xd5", "ioACQUwUIEIJHq"], ["0xd6", "y29UC3rYDwn0B3i"], ["0xd7", "zw52"], ["0xd8", "C3vIC3rY"], ["0xd9", "yMLUyxj5"], ["0xda", "y2HTB2q"], ["0xdb", "C3rHDfn5BMm"], ["0xdc", "iowpR+IdVEATO+wCQoIIQ1ztq29KzEs9V+EuQa"], ["0xdd", "t1z5DeS"], ["0xde", "ntC5mfHKvg5wyG"], ["0xdf", "B25eAwreAxnWB3nL"], ["0xe0", "5BEY5l2/55sO5Ash55sO5PA55Qgi5RIf55cgief1z21LBNqG5PwW5O2U"], ["0xe1", "ChjVBwLZzxm"]]}