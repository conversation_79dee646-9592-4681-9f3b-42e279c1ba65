{"timestamp": "2025-08-05T10:47:28.854Z", "summary": {"totalTests": 52, "vulnerabilities": 24, "severityBreakdown": {"CRITICAL": 0, "HIGH": 17, "MEDIUM": 6, "LOW": 1}, "riskScore": 100}, "vulnerabilities": [{"category": "Input Validation", "testName": "Command Injection载荷测试", "severity": "HIGH", "status": "VULNERABLE", "description": "恶意输入未被正确过滤", "details": {"input": "; cat /etc/passwd..."}, "timestamp": "2025-08-05T10:47:28.847Z"}, {"category": "Input Validation", "testName": "Command Injection载荷测试", "severity": "HIGH", "status": "VULNERABLE", "description": "恶意输入未被正确过滤", "details": {"input": "| whoami..."}, "timestamp": "2025-08-05T10:47:28.847Z"}, {"category": "Input Validation", "testName": "Command Injection载荷测试", "severity": "HIGH", "status": "VULNERABLE", "description": "恶意输入未被正确过滤", "details": {"input": "`id`..."}, "timestamp": "2025-08-05T10:47:28.847Z"}, {"category": "Network Security", "testName": "URL验证测试", "severity": "HIGH", "status": "VULNERABLE", "description": "恶意URL未被拒绝: http://127.0.0.1:22/ssh...", "details": null, "timestamp": "2025-08-05T10:47:28.848Z"}, {"category": "Network Security", "testName": "请求头注入测试", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到请求头注入尝试", "details": null, "timestamp": "2025-08-05T10:47:28.849Z"}, {"category": "Network Security", "testName": "请求头注入测试", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到请求头注入尝试", "details": null, "timestamp": "2025-08-05T10:47:28.849Z"}, {"category": "Network Security", "testName": "请求头注入测试", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到请求头注入尝试", "details": null, "timestamp": "2025-08-05T10:47:28.849Z"}, {"category": "Cryptographic Security", "testName": "弱密钥检测", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到弱密钥: 123456", "details": {"keyStrength": 1}, "timestamp": "2025-08-05T10:47:28.849Z"}, {"category": "Cryptographic Security", "testName": "弱密钥检测", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到弱密钥: password", "details": {"keyStrength": 2}, "timestamp": "2025-08-05T10:47:28.850Z"}, {"category": "Cryptographic Security", "testName": "弱密钥检测", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到弱密钥: admin", "details": {"keyStrength": 1}, "timestamp": "2025-08-05T10:47:28.850Z"}, {"category": "Cryptographic Security", "testName": "弱密钥检测", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到弱密钥: 000000", "details": {"keyStrength": 1}, "timestamp": "2025-08-05T10:47:28.850Z"}, {"category": "Cryptographic Security", "testName": "弱密钥检测", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到弱密钥: qwerty", "details": {"keyStrength": 1}, "timestamp": "2025-08-05T10:47:28.850Z"}, {"category": "Cryptographic Security", "testName": "弱密钥检测", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到弱密钥: 12345678", "details": {"keyStrength": 2}, "timestamp": "2025-08-05T10:47:28.850Z"}, {"category": "Cryptographic Security", "testName": "弱密钥检测", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到弱密钥: aaaaaaaa", "details": {"keyStrength": 2}, "timestamp": "2025-08-05T10:47:28.850Z"}, {"category": "Cryptographic Security", "testName": "哈希碰撞测试", "severity": "LOW", "status": "VULNERABLE", "description": "检测到哈希碰撞（预期行为）", "details": null, "timestamp": "2025-08-05T10:47:28.851Z"}, {"category": "Cryptographic Security", "testName": "时序攻击测试", "severity": "MEDIUM", "status": "VULNERABLE", "description": "检测到时序差异", "details": {"password": "wro...", "duration": 0.0327}, "timestamp": "2025-08-05T10:47:28.851Z"}, {"category": "Cryptographic Security", "testName": "时序攻击测试", "severity": "MEDIUM", "status": "VULNERABLE", "description": "检测到时序差异", "details": {"password": "c...", "duration": 0.001}, "timestamp": "2025-08-05T10:47:28.851Z"}, {"category": "Cryptographic Security", "testName": "时序攻击测试", "severity": "MEDIUM", "status": "VULNERABLE", "description": "检测到时序差异", "details": {"password": "co...", "duration": 0.0023}, "timestamp": "2025-08-05T10:47:28.851Z"}, {"category": "Cryptographic Security", "testName": "时序攻击测试", "severity": "MEDIUM", "status": "VULNERABLE", "description": "检测到时序差异", "details": {"password": "cor...", "duration": 0.0036}, "timestamp": "2025-08-05T10:47:28.852Z"}, {"category": "Cryptographic Security", "testName": "时序攻击测试", "severity": "MEDIUM", "status": "VULNERABLE", "description": "检测到时序差异", "details": {"password": "cor...", "duration": 0.0019}, "timestamp": "2025-08-05T10:47:28.852Z"}, {"category": "File System Security", "testName": "路径遍历测试", "severity": "HIGH", "status": "VULNERABLE", "description": "路径遍历攻击可能成功", "details": {"path": "../../../etc/passwd", "normalized": "..\\..\\..\\etc\\passwd"}, "timestamp": "2025-08-05T10:47:28.852Z"}, {"category": "File System Security", "testName": "路径遍历测试", "severity": "HIGH", "status": "VULNERABLE", "description": "路径遍历攻击可能成功", "details": {"path": "..\\..\\..\\windows\\system32\\config\\sam", "normalized": "..\\..\\..\\windows\\system32\\config\\sam"}, "timestamp": "2025-08-05T10:47:28.853Z"}, {"category": "File System Security", "testName": "路径遍历测试", "severity": "HIGH", "status": "VULNERABLE", "description": "路径遍历攻击可能成功", "details": {"path": "....//....//....//etc/passwd", "normalized": "....\\....\\....\\etc\\passwd"}, "timestamp": "2025-08-05T10:47:28.853Z"}, {"category": "File System Security", "testName": "文件权限检查", "severity": "MEDIUM", "status": "VULNERABLE", "description": "文件权限过于宽松", "details": {"file": "./extension/dist/extension.js", "permissions": "100666"}, "timestamp": "2025-08-05T10:47:28.853Z"}], "allTests": [{"category": "Input Validation", "testName": "XSS载荷测试", "severity": "INFO", "status": "SECURE", "description": "恶意输入被正确拒绝", "details": null, "timestamp": "2025-08-05T10:47:28.846Z"}, {"category": "Input Validation", "testName": "XSS载荷测试", "severity": "INFO", "status": "SECURE", "description": "恶意输入被正确拒绝", "details": null, "timestamp": "2025-08-05T10:47:28.847Z"}, {"category": "Input Validation", "testName": "XSS载荷测试", "severity": "INFO", "status": "SECURE", "description": "恶意输入被正确拒绝", "details": null, "timestamp": "2025-08-05T10:47:28.847Z"}, {"category": "Input Validation", "testName": "XSS载荷测试", "severity": "INFO", "status": "SECURE", "description": "恶意输入被正确拒绝", "details": null, "timestamp": "2025-08-05T10:47:28.847Z"}, {"category": "Input Validation", "testName": "SQL Injection载荷测试", "severity": "INFO", "status": "SECURE", "description": "恶意输入被正确拒绝", "details": null, "timestamp": "2025-08-05T10:47:28.847Z"}, {"category": "Input Validation", "testName": "SQL Injection载荷测试", "severity": "INFO", "status": "SECURE", "description": "恶意输入被正确拒绝", "details": null, "timestamp": "2025-08-05T10:47:28.847Z"}, {"category": "Input Validation", "testName": "SQL Injection载荷测试", "severity": "INFO", "status": "SECURE", "description": "恶意输入被正确拒绝", "details": null, "timestamp": "2025-08-05T10:47:28.847Z"}, {"category": "Input Validation", "testName": "Command Injection载荷测试", "severity": "HIGH", "status": "VULNERABLE", "description": "恶意输入未被正确过滤", "details": {"input": "; cat /etc/passwd..."}, "timestamp": "2025-08-05T10:47:28.847Z"}, {"category": "Input Validation", "testName": "Command Injection载荷测试", "severity": "HIGH", "status": "VULNERABLE", "description": "恶意输入未被正确过滤", "details": {"input": "| whoami..."}, "timestamp": "2025-08-05T10:47:28.847Z"}, {"category": "Input Validation", "testName": "Command Injection载荷测试", "severity": "HIGH", "status": "VULNERABLE", "description": "恶意输入未被正确过滤", "details": {"input": "`id`..."}, "timestamp": "2025-08-05T10:47:28.847Z"}, {"category": "Input Validation", "testName": "Path Traversal载荷测试", "severity": "INFO", "status": "SECURE", "description": "恶意输入被正确拒绝", "details": null, "timestamp": "2025-08-05T10:47:28.848Z"}, {"category": "Input Validation", "testName": "Path Traversal载荷测试", "severity": "INFO", "status": "SECURE", "description": "恶意输入被正确拒绝", "details": null, "timestamp": "2025-08-05T10:47:28.848Z"}, {"category": "Input Validation", "testName": "Buffer Overflow载荷测试", "severity": "INFO", "status": "SECURE", "description": "恶意输入被正确拒绝", "details": null, "timestamp": "2025-08-05T10:47:28.848Z"}, {"category": "Input Validation", "testName": "Buffer Overflow载荷测试", "severity": "INFO", "status": "SECURE", "description": "恶意输入被正确拒绝", "details": null, "timestamp": "2025-08-05T10:47:28.848Z"}, {"category": "Input Validation", "testName": "Format String载荷测试", "severity": "INFO", "status": "SECURE", "description": "恶意输入被正确拒绝", "details": null, "timestamp": "2025-08-05T10:47:28.848Z"}, {"category": "Input Validation", "testName": "Format String载荷测试", "severity": "INFO", "status": "SECURE", "description": "恶意输入被正确拒绝", "details": null, "timestamp": "2025-08-05T10:47:28.848Z"}, {"category": "Network Security", "testName": "URL验证测试", "severity": "INFO", "status": "SECURE", "description": "恶意URL被正确拒绝: javascript:alert(\"xss\")...", "details": null, "timestamp": "2025-08-05T10:47:28.848Z"}, {"category": "Network Security", "testName": "URL验证测试", "severity": "INFO", "status": "SECURE", "description": "恶意URL被正确拒绝: data:text/html,<script>alert(\"...", "details": null, "timestamp": "2025-08-05T10:47:28.848Z"}, {"category": "Network Security", "testName": "URL验证测试", "severity": "INFO", "status": "SECURE", "description": "恶意URL被正确拒绝: file:///etc/passwd...", "details": null, "timestamp": "2025-08-05T10:47:28.848Z"}, {"category": "Network Security", "testName": "URL验证测试", "severity": "INFO", "status": "SECURE", "description": "恶意URL被正确拒绝: ftp://malicious.com/file...", "details": null, "timestamp": "2025-08-05T10:47:28.848Z"}, {"category": "Network Security", "testName": "URL验证测试", "severity": "HIGH", "status": "VULNERABLE", "description": "恶意URL未被拒绝: http://127.0.0.1:22/ssh...", "details": null, "timestamp": "2025-08-05T10:47:28.848Z"}, {"category": "Network Security", "testName": "请求头注入测试", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到请求头注入尝试", "details": null, "timestamp": "2025-08-05T10:47:28.849Z"}, {"category": "Network Security", "testName": "请求头注入测试", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到请求头注入尝试", "details": null, "timestamp": "2025-08-05T10:47:28.849Z"}, {"category": "Network Security", "testName": "请求头注入测试", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到请求头注入尝试", "details": null, "timestamp": "2025-08-05T10:47:28.849Z"}, {"category": "Network Security", "testName": "端口访问测试: 22", "severity": "INFO", "status": "SECURE", "description": "非标准端口访问被阻止", "details": null, "timestamp": "2025-08-05T10:47:28.849Z"}, {"category": "Network Security", "testName": "端口访问测试: 23", "severity": "INFO", "status": "SECURE", "description": "非标准端口访问被阻止", "details": null, "timestamp": "2025-08-05T10:47:28.849Z"}, {"category": "Network Security", "testName": "端口访问测试: 135", "severity": "INFO", "status": "SECURE", "description": "非标准端口访问被阻止", "details": null, "timestamp": "2025-08-05T10:47:28.849Z"}, {"category": "Network Security", "testName": "端口访问测试: 139", "severity": "INFO", "status": "SECURE", "description": "非标准端口访问被阻止", "details": null, "timestamp": "2025-08-05T10:47:28.849Z"}, {"category": "Network Security", "testName": "端口访问测试: 445", "severity": "INFO", "status": "SECURE", "description": "非标准端口访问被阻止", "details": null, "timestamp": "2025-08-05T10:47:28.849Z"}, {"category": "Network Security", "testName": "端口访问测试: 1433", "severity": "INFO", "status": "SECURE", "description": "非标准端口访问被阻止", "details": null, "timestamp": "2025-08-05T10:47:28.849Z"}, {"category": "Network Security", "testName": "端口访问测试: 3389", "severity": "INFO", "status": "SECURE", "description": "非标准端口访问被阻止", "details": null, "timestamp": "2025-08-05T10:47:28.849Z"}, {"category": "Cryptographic Security", "testName": "弱密钥检测", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到弱密钥: 123456", "details": {"keyStrength": 1}, "timestamp": "2025-08-05T10:47:28.849Z"}, {"category": "Cryptographic Security", "testName": "弱密钥检测", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到弱密钥: password", "details": {"keyStrength": 2}, "timestamp": "2025-08-05T10:47:28.850Z"}, {"category": "Cryptographic Security", "testName": "弱密钥检测", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到弱密钥: admin", "details": {"keyStrength": 1}, "timestamp": "2025-08-05T10:47:28.850Z"}, {"category": "Cryptographic Security", "testName": "弱密钥检测", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到弱密钥: 000000", "details": {"keyStrength": 1}, "timestamp": "2025-08-05T10:47:28.850Z"}, {"category": "Cryptographic Security", "testName": "弱密钥检测", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到弱密钥: qwerty", "details": {"keyStrength": 1}, "timestamp": "2025-08-05T10:47:28.850Z"}, {"category": "Cryptographic Security", "testName": "弱密钥检测", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到弱密钥: 12345678", "details": {"keyStrength": 2}, "timestamp": "2025-08-05T10:47:28.850Z"}, {"category": "Cryptographic Security", "testName": "弱密钥检测", "severity": "HIGH", "status": "VULNERABLE", "description": "检测到弱密钥: aaaaaaaa", "details": {"keyStrength": 2}, "timestamp": "2025-08-05T10:47:28.850Z"}, {"category": "Cryptographic Security", "testName": "随机数质量测试", "severity": "INFO", "status": "SECURE", "description": "随机数质量良好", "details": null, "timestamp": "2025-08-05T10:47:28.851Z"}, {"category": "Cryptographic Security", "testName": "哈希碰撞测试", "severity": "LOW", "status": "VULNERABLE", "description": "检测到哈希碰撞（预期行为）", "details": null, "timestamp": "2025-08-05T10:47:28.851Z"}, {"category": "Cryptographic Security", "testName": "时序攻击测试", "severity": "MEDIUM", "status": "VULNERABLE", "description": "检测到时序差异", "details": {"password": "wro...", "duration": 0.0327}, "timestamp": "2025-08-05T10:47:28.851Z"}, {"category": "Cryptographic Security", "testName": "时序攻击测试", "severity": "MEDIUM", "status": "VULNERABLE", "description": "检测到时序差异", "details": {"password": "c...", "duration": 0.001}, "timestamp": "2025-08-05T10:47:28.851Z"}, {"category": "Cryptographic Security", "testName": "时序攻击测试", "severity": "MEDIUM", "status": "VULNERABLE", "description": "检测到时序差异", "details": {"password": "co...", "duration": 0.0023}, "timestamp": "2025-08-05T10:47:28.851Z"}, {"category": "Cryptographic Security", "testName": "时序攻击测试", "severity": "MEDIUM", "status": "VULNERABLE", "description": "检测到时序差异", "details": {"password": "cor...", "duration": 0.0036}, "timestamp": "2025-08-05T10:47:28.852Z"}, {"category": "Cryptographic Security", "testName": "时序攻击测试", "severity": "MEDIUM", "status": "VULNERABLE", "description": "检测到时序差异", "details": {"password": "cor...", "duration": 0.0019}, "timestamp": "2025-08-05T10:47:28.852Z"}, {"category": "File System Security", "testName": "路径遍历测试", "severity": "HIGH", "status": "VULNERABLE", "description": "路径遍历攻击可能成功", "details": {"path": "../../../etc/passwd", "normalized": "..\\..\\..\\etc\\passwd"}, "timestamp": "2025-08-05T10:47:28.852Z"}, {"category": "File System Security", "testName": "路径遍历测试", "severity": "HIGH", "status": "VULNERABLE", "description": "路径遍历攻击可能成功", "details": {"path": "..\\..\\..\\windows\\system32\\config\\sam", "normalized": "..\\..\\..\\windows\\system32\\config\\sam"}, "timestamp": "2025-08-05T10:47:28.853Z"}, {"category": "File System Security", "testName": "路径遍历测试", "severity": "INFO", "status": "SECURE", "description": "路径遍历被正确阻止", "details": null, "timestamp": "2025-08-05T10:47:28.853Z"}, {"category": "File System Security", "testName": "路径遍历测试", "severity": "INFO", "status": "SECURE", "description": "路径遍历被正确阻止", "details": null, "timestamp": "2025-08-05T10:47:28.853Z"}, {"category": "File System Security", "testName": "路径遍历测试", "severity": "HIGH", "status": "VULNERABLE", "description": "路径遍历攻击可能成功", "details": {"path": "....//....//....//etc/passwd", "normalized": "....\\....\\....\\etc\\passwd"}, "timestamp": "2025-08-05T10:47:28.853Z"}, {"category": "File System Security", "testName": "路径遍历测试", "severity": "INFO", "status": "SECURE", "description": "路径遍历被正确阻止", "details": null, "timestamp": "2025-08-05T10:47:28.853Z"}, {"category": "File System Security", "testName": "文件权限检查", "severity": "MEDIUM", "status": "VULNERABLE", "description": "文件权限过于宽松", "details": {"file": "./extension/dist/extension.js", "permissions": "100666"}, "timestamp": "2025-08-05T10:47:28.853Z"}], "recommendations": ["实施严格的输入验证和输出编码", "使用参数化查询防止SQL注入", "实施XSS防护机制", "强制使用HTTPS通信", "实施请求头验证和清理", "配置适当的CORS策略", "使用强密钥和安全的随机数生成器", "实施常量时间比较防止时序攻击", "定期轮换加密密钥", "实施严格的文件路径验证", "设置适当的文件权限", "使用沙箱环境限制文件访问"]}