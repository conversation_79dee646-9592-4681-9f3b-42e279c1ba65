/**
 * 简单登录脚本 - 只用激活码登录，获取返回值
 * 激活码: 90909420-c7f4-4bd6-8517-0bfc572ed3e1
 */

const https = require('https');
const crypto = require('crypto');
const fs = require('fs');

// 配置
const CONFIG = {
    activationCode: "90909420-c7f4-4bd6-8517-0bfc572ed3e1",
    baseUrl: "https://aug.202578.xyz",
    timeout: 10000
};

// 生成机器ID
function generateMachineId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// HTTP请求函数
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: data ? JSON.parse(data) : null
                    });
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: null,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(CONFIG.timeout, () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

// 简单登录
async function simpleLogin() {
    console.log('🚀 开始登录...');
    console.log(`🔑 激活码: ${CONFIG.activationCode}`);
    console.log(`🌐 目标: ${CONFIG.baseUrl}`);
    console.log('');
    
    const machineId = generateMachineId();
    const timestamp = Date.now();
    
    // 构造请求体
    const requestBody = {
        activationCode: CONFIG.activationCode,
        clientVersion: "1.1.1",
        platform: "vscode",
        machineId: machineId,
        timestamp: timestamp
    };
    
    const payload = JSON.stringify(requestBody);
    
    // 构造请求头
    const headers = {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(payload),
        'User-Agent': 'SmartShift-VSCode/1.1.1',
        'Accept': 'application/json'
    };
    
    // 尝试 /api/auth 端点
    try {
        const url = new URL('/api/auth', CONFIG.baseUrl);
        
        const options = {
            hostname: url.hostname,
            port: url.port || 443,
            path: url.pathname,
            method: 'POST',
            headers: headers
        };
        
        console.log(`📡 发送请求到: ${CONFIG.baseUrl}/api/auth`);
        console.log(`📋 请求体:`, JSON.stringify(requestBody, null, 2));
        console.log('');
        
        const response = await makeRequest(options, payload);
        
        console.log(`📊 响应状态码: ${response.statusCode}`);
        console.log(`📋 响应头:`, JSON.stringify(response.headers, null, 2));
        console.log(`📄 响应体: ${response.body}`);
        console.log('');
        
        if (response.data) {
            console.log(`✅ 解析后的JSON数据:`);
            console.log(JSON.stringify(response.data, null, 2));
            
            // 保存完整响应
            const responseFile = `登录响应_${Date.now()}.json`;
            fs.writeFileSync(responseFile, JSON.stringify({
                request: {
                    url: `${CONFIG.baseUrl}/api/auth`,
                    method: 'POST',
                    headers: headers,
                    body: requestBody
                },
                response: {
                    statusCode: response.statusCode,
                    headers: response.headers,
                    body: response.body,
                    data: response.data
                },
                timestamp: new Date().toISOString()
            }, null, 2));
            
            console.log(`💾 完整响应已保存到: ${responseFile}`);
            
            // 分析响应数据
            console.log('\n🔍 分析响应数据:');
            if (response.data.accessToken || response.data.token) {
                console.log(`🔑 Token: ${response.data.accessToken || response.data.token}`);
            }
            if (response.data.tenantURL) {
                console.log(`🌐 Tenant URL: ${response.data.tenantURL}`);
            }
            if (response.data.email) {
                console.log(`📧 Email: ${response.data.email}`);
            }
            if (response.data.userId || response.data.user_id) {
                console.log(`👤 User ID: ${response.data.userId || response.data.user_id}`);
            }
            if (response.data.accounts) {
                console.log(`📊 账号数量: ${response.data.accounts.length}`);
            }
            if (response.data.refreshToken) {
                console.log(`🔄 Refresh Token: ${response.data.refreshToken}`);
            }
            
        } else {
            console.log(`❌ 无法解析JSON响应`);
            if (response.parseError) {
                console.log(`解析错误: ${response.parseError}`);
            }
        }
        
        return response;
        
    } catch (error) {
        console.error(`❌ 请求失败: ${error.message}`);
        return null;
    }
}

// 主函数
async function main() {
    const result = await simpleLogin();
    
    if (result && result.statusCode === 200) {
        console.log('\n🎉 登录成功!');
        
        if (result.data) {
            console.log('\n📋 现在您可以使用这些信息进行后续操作:');
            console.log('- accessToken: 用于API认证');
            console.log('- tenantURL: API基础地址');
            console.log('- email: 用户邮箱');
            console.log('- accounts: 可切换的账号列表');
            console.log('- refreshToken: 用于刷新token');
        }
    } else {
        console.log('\n❌ 登录失败');
        if (result) {
            console.log(`状态码: ${result.statusCode}`);
            console.log(`响应: ${result.body}`);
        }
    }
}

// 运行
main().catch(console.error);

/**
 * 使用方法:
 * node 简单登录获取信息.js
 * 
 * 功能:
 * 1. 使用激活码进行简单登录
 * 2. 显示完整的请求和响应信息
 * 3. 保存响应数据到JSON文件
 * 4. 分析响应中的关键信息
 */
