/**
 * 使用激活码登录获取完整信息，然后利用这些信息获取大量token
 * 激活码: 90909420-c7f4-4bd6-8517-0bfc572ed3e1
 */

const https = require('https');
const crypto = require('crypto');
const fs = require('fs');
const os = require('os');

// 配置
const CONFIG = {
    activationCode: "90909420-c7f4-4bd6-8517-0bfc572ed3e1",
    // 从项目中发现的真实API域名
    baseUrl: "https://aug.202578.xyz",
    // 备用域名
    fallbackUrls: [
        "https://i0.api.augmentcode.com",
        "https://api.augmentcode.com",
        "https://augment.api.com",
        "https://api.smartshift.com"
    ],
    timeout: 10000,
    maxRetries: 3
};

// 生成真实的VSCode机器ID
function generateVSCodeMachineId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// 生成设备信息
function generateDeviceInfo() {
    return {
        platform: os.platform(),
        arch: os.arch(),
        hostname: os.hostname(),
        cpus: os.cpus().length,
        memory: Math.round(os.totalmem() / 1024 / 1024),
        nodeVersion: process.version,
        vscodeVersion: "1.85.0",
        extensionVersion: "1.1.1"
    };
}

// HTTP请求函数
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: data ? JSON.parse(data) : null
                    });
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                        data: null,
                        parseError: error.message
                    });
                }
            });
        });
        
        req.on('error', reject);
        req.setTimeout(CONFIG.timeout, () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

// 尝试登录获取完整信息
async function attemptLogin(baseUrl) {
    const machineId = generateVSCodeMachineId();
    const deviceInfo = generateDeviceInfo();
    const timestamp = Date.now();
    const nonce = crypto.randomBytes(16).toString('hex');
    
    // 可能的登录端点
    const loginEndpoints = [
        '/api/auth',
        '/api/login',
        '/api/v1/auth',
        '/api/v1/login',
        '/auth',
        '/login',
        '/api/user/auth',
        '/api/user/login',
        '/api/activate',
        '/activate'
    ];
    
    for (const endpoint of loginEndpoints) {
        try {
            console.log(`🔍 尝试端点: ${baseUrl}${endpoint}`);
            
            const url = new URL(endpoint, baseUrl);
            
            // 构造请求体
            const requestBody = {
                activationCode: CONFIG.activationCode,
                clientVersion: "1.1.1",
                platform: "vscode",
                machineId: machineId,
                deviceInfo: deviceInfo,
                timestamp: timestamp,
                nonce: nonce
            };
            
            const payload = JSON.stringify(requestBody);
            
            // 构造请求头
            const headers = {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(payload),
                'User-Agent': 'SmartShift-VSCode/1.1.1',
                'X-Client-Version': '1.1.1',
                'X-Platform': 'vscode',
                'X-Timestamp': timestamp.toString(),
                'X-Request-ID': `req_${timestamp}_${crypto.randomBytes(4).toString('hex')}`,
                'X-Machine-ID': machineId,
                'X-Nonce': nonce,
                'Accept': 'application/json',
                'Cache-Control': 'no-cache'
            };
            
            const options = {
                hostname: url.hostname,
                port: url.port || 443,
                path: url.pathname,
                method: 'POST',
                headers: headers
            };
            
            const response = await makeRequest(options, payload);
            
            console.log(`📊 ${endpoint} - 状态码: ${response.statusCode}`);
            
            if (response.statusCode === 200 && response.data) {
                console.log(`✅ 登录成功! 端点: ${endpoint}`);
                console.log(`📋 响应数据:`, JSON.stringify(response.data, null, 2));
                
                return {
                    success: true,
                    endpoint: endpoint,
                    baseUrl: baseUrl,
                    response: response,
                    loginInfo: response.data,
                    machineId: machineId,
                    deviceInfo: deviceInfo
                };
            } else if (response.statusCode !== 404) {
                console.log(`⚠️  ${endpoint} - 状态码: ${response.statusCode}, 响应: ${response.body}`);
            }
            
        } catch (error) {
            console.log(`❌ ${endpoint} - 错误: ${error.message}`);
        }
    }
    
    return null;
}

// 利用登录信息获取大量token
async function generateMassiveTokens(loginResult) {
    console.log('\n🚀 开始利用登录信息生成大量token...');
    
    const { loginInfo, baseUrl, machineId, deviceInfo } = loginResult;
    
    // 从登录响应中提取关键信息
    const extractedInfo = {
        accessToken: loginInfo.accessToken || loginInfo.token,
        tenantURL: loginInfo.tenantURL || baseUrl,
        email: loginInfo.email,
        userId: loginInfo.userId || loginInfo.user_id,
        userInfo: loginInfo.userInfo || loginInfo.user,
        accounts: loginInfo.accounts || [],
        refreshToken: loginInfo.refreshToken,
        apiKeys: loginInfo.apiKeys || {},
        sessionId: loginInfo.sessionId || loginInfo.session_id
    };
    
    console.log('📋 提取的关键信息:');
    console.log(JSON.stringify(extractedInfo, null, 2));
    
    // 如果有refreshToken，尝试刷新获取新token
    if (extractedInfo.refreshToken) {
        console.log('\n🔄 尝试使用refreshToken获取新token...');
        await tryRefreshToken(extractedInfo, baseUrl);
    }
    
    // 如果有多个账号，尝试切换获取不同token
    if (extractedInfo.accounts && extractedInfo.accounts.length > 0) {
        console.log('\n🔀 尝试切换账号获取不同token...');
        await trySwitchAccounts(extractedInfo, baseUrl, machineId);
    }
    
    // 尝试生成多个机器ID获取token
    console.log('\n🖥️  尝试使用不同机器ID获取token...');
    await tryMultipleMachineIds(extractedInfo, baseUrl);
    
    return extractedInfo;
}

// 尝试刷新token
async function tryRefreshToken(info, baseUrl) {
    const refreshEndpoints = [
        '/api/auth/refresh',
        '/api/refresh',
        '/api/v1/auth/refresh',
        '/refresh'
    ];
    
    for (const endpoint of refreshEndpoints) {
        try {
            const url = new URL(endpoint, baseUrl);
            
            const payload = JSON.stringify({
                refreshToken: info.refreshToken,
                userId: info.userId
            });
            
            const options = {
                hostname: url.hostname,
                port: url.port || 443,
                path: url.pathname,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${info.accessToken}`,
                    'Content-Length': Buffer.byteLength(payload)
                }
            };
            
            const response = await makeRequest(options, payload);
            
            if (response.statusCode === 200 && response.data) {
                console.log(`✅ 刷新成功! 新token: ${response.data.accessToken || response.data.token}`);
                
                // 保存新token
                const tokenFile = `新token_${Date.now()}.json`;
                fs.writeFileSync(tokenFile, JSON.stringify(response.data, null, 2));
                console.log(`💾 新token已保存: ${tokenFile}`);
            }
            
        } catch (error) {
            console.log(`❌ 刷新失败: ${error.message}`);
        }
    }
}

// 尝试切换账号
async function trySwitchAccounts(info, baseUrl, machineId) {
    const switchEndpoints = [
        '/api/accounts/switch',
        '/api/v1/accounts/switch',
        '/api/switch',
        '/switch'
    ];
    
    for (const account of info.accounts) {
        for (const endpoint of switchEndpoints) {
            try {
                const url = new URL(endpoint, baseUrl);
                
                const payload = JSON.stringify({
                    accountId: account.id || account.accountId,
                    userId: info.userId,
                    machineId: machineId
                });
                
                const options = {
                    hostname: url.hostname,
                    port: url.port || 443,
                    path: url.pathname,
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${info.accessToken}`,
                        'Content-Length': Buffer.byteLength(payload)
                    }
                };
                
                const response = await makeRequest(options, payload);
                
                if (response.statusCode === 200 && response.data) {
                    console.log(`✅ 切换成功! 账号: ${account.id}, 新token: ${response.data.accessToken || response.data.token}`);
                    
                    // 保存切换后的token
                    const tokenFile = `切换token_${account.id}_${Date.now()}.json`;
                    fs.writeFileSync(tokenFile, JSON.stringify({
                        account: account,
                        response: response.data
                    }, null, 2));
                }
                
            } catch (error) {
                console.log(`❌ 切换失败: ${error.message}`);
            }
        }
    }
}

// 尝试多个机器ID
async function tryMultipleMachineIds(info, baseUrl) {
    const authEndpoints = ['/api/auth', '/api/login'];
    
    for (let i = 0; i < 5; i++) {
        const newMachineId = generateVSCodeMachineId();
        
        for (const endpoint of authEndpoints) {
            try {
                const url = new URL(endpoint, baseUrl);
                
                const payload = JSON.stringify({
                    activationCode: CONFIG.activationCode,
                    machineId: newMachineId,
                    clientVersion: "1.1.1",
                    platform: "vscode"
                });
                
                const options = {
                    hostname: url.hostname,
                    port: url.port || 443,
                    path: url.pathname,
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Content-Length': Buffer.byteLength(payload)
                    }
                };
                
                const response = await makeRequest(options, payload);
                
                if (response.statusCode === 200 && response.data) {
                    console.log(`✅ 新机器ID登录成功! MachineId: ${newMachineId}, Token: ${response.data.accessToken || response.data.token}`);
                    
                    // 保存新机器ID的token
                    const tokenFile = `机器ID_token_${i}_${Date.now()}.json`;
                    fs.writeFileSync(tokenFile, JSON.stringify({
                        machineId: newMachineId,
                        response: response.data
                    }, null, 2));
                }
                
            } catch (error) {
                console.log(`❌ 新机器ID登录失败: ${error.message}`);
            }
        }
    }
}

// 主函数
async function main() {
    console.log('🚀 开始使用激活码登录获取信息...');
    console.log(`🔑 激活码: ${CONFIG.activationCode}`);
    console.log('');
    
    // 尝试所有可能的域名
    const allUrls = [CONFIG.baseUrl, ...CONFIG.fallbackUrls];
    
    for (const url of allUrls) {
        console.log(`🌐 尝试域名: ${url}`);
        
        const loginResult = await attemptLogin(url);
        
        if (loginResult) {
            console.log('\n🎉 登录成功! 开始利用信息获取大量token...');
            
            // 保存完整的登录信息
            const loginFile = `完整登录信息_${Date.now()}.json`;
            fs.writeFileSync(loginFile, JSON.stringify(loginResult, null, 2));
            console.log(`💾 完整登录信息已保存: ${loginFile}`);
            
            // 利用登录信息获取大量token
            await generateMassiveTokens(loginResult);
            
            console.log('\n🏆 任务完成! 检查生成的token文件。');
            return;
        }
        
        console.log(`❌ ${url} 登录失败\n`);
    }
    
    console.log('❌ 所有域名都登录失败');
}

// 运行主函数
main().catch(console.error);

/**
 * 使用方法:
 * node 激活码登录获取信息.js
 * 
 * 功能:
 * 1. 使用激活码尝试登录多个可能的API域名
 * 2. 获取完整的登录响应信息
 * 3. 利用refreshToken获取新token
 * 4. 利用多账号切换获取不同token
 * 5. 利用不同机器ID获取多个token
 * 6. 自动保存所有获取的token信息
 */
